# 📱 Agent Leon - iOS Device Deployment Gids

## Overzicht
Deze gids helpt je om Agent Leon op een fysiek iOS apparaat te deployen.

## 🎯 Wat je nodig hebt

### Hardware
- Mac computer met macOS
- iOS apparaat (iPhone/iPad) 
- USB-C naar Lightning/USB-C kabel

### Software
- Xcode (nieuwste versie)
- iOS 17.5 of hoger op je apparaat

### Apple Developer Account
- **Optie 1**: Gratis <PERSON> ID (beperkt tot 7 dagen, moet hernieuwd worden)
- **Optie 2**: Apple Developer Program ($99/jaar) voor volledige functionaliteit

## 🚀 Stap-voor-stap Deployment

### Stap 1: Voorbereiding
```bash
# Controleer of je in de juiste directory bent
cd /Users/<USER>/ios-bouwapp/ios-app

# Run het deployment script
./deploy_to_device.sh
```

### Stap 2: Apple ID Setup
1. Open **Xcode**
2. Ga naar **Xcode > Settings > Accounts**
3. <PERSON><PERSON> op **+** en voeg je Apple ID toe
4. Selecteer je team
5. <PERSON><PERSON> **Download Manual Profiles**

### Stap 3: Project Configuratie
1. Open `AgentLeon.xcodeproj` in Xcode
2. Selecteer het **AgentLeon** target
3. Ga naar **Signing & Capabilities** tab
4. **Vink "Automatically manage signing" aan**
5. Selecteer je **Team**
6. Wijzig **Bundle Identifier** naar iets unieks:
   - Van: `com.agentleon.trading`
   - Naar: `com.jouwNaam.agentleon` (vervang jouwNaam)

### Stap 4: Apparaat Verbinden
1. Verbind je iPhone/iPad via USB
2. Ontgrendel je apparaat
3. Accepteer **"Vertrouw deze computer"**
4. Ga naar **Settings > General > VPN & Device Management** op je iOS apparaat
5. Vertrouw je developer certificaat

### Stap 5: Deployen
1. Selecteer je apparaat in Xcode (naast de run knop)
2. Klik **Run** (▶️) of gebruik **Cmd+R**
3. Wacht tot de build en installatie voltooid is

## 🔧 Troubleshooting

### Probleem: "No valid code signing identity"
**Oplossing:**
- Zorg dat je Apple ID is toegevoegd in Xcode
- Download provisioning profiles opnieuw
- Controleer of je team is geselecteerd

### Probleem: "Bundle identifier already exists"
**Oplossing:**
- Wijzig de Bundle Identifier naar iets unieks
- Gebruik format: `com.jouwNaam.agentleon`

### Probleem: "Device not found"
**Oplossing:**
- Controleer USB verbinding
- Vertrouw de computer op je iOS apparaat
- Herstart Xcode

### Probleem: "App crashes bij start"
**Oplossing:**
- Controleer iOS versie (minimaal 17.5)
- Kijk in Console.app voor crash logs
- Rebuild het project

## 📋 Gratis Apple ID Beperkingen

Met een gratis Apple ID:
- **7 dagen limiet**: App moet elke week opnieuw geïnstalleerd
- **3 apps limiet**: Maximaal 3 apps tegelijk
- **Geen App Store**: Kan niet naar App Store gepubliceerd worden

## 💰 Apple Developer Program Voordelen

Met een betaald account ($99/jaar):
- **1 jaar certificaten**: Geen wekelijkse hernieuwing
- **Onbeperkte apps**: Geen 3 apps limiet
- **App Store**: Kan naar App Store gepubliceerd worden
- **TestFlight**: Delen met anderen via TestFlight
- **Advanced features**: Push notifications, CloudKit, etc.

## 🎉 Na Successful Deployment

Agent Leon draait nu op je iOS apparaat met:
- 🤖 Volledig functionele AI agents
- 📊 Real-time crypto trading dashboard
- 💱 Multi-exchange ondersteuning (KuCoin, MEXC, Bybit)
- 🔒 Veilige API key opslag
- ⚡ Native iOS performance

## 📞 Support

Bij problemen:
1. Check de troubleshooting sectie hierboven
2. Run `./deploy_to_device.sh` voor diagnostics
3. Controleer Xcode build logs voor specifieke errors

---

**Tip**: Voor de beste ervaring, gebruik een betaald Apple Developer account voor stabiele, langdurige deployment. 