---
description: 
globs: 
alwaysApply: false
---
# Agent Leon Control Dashboard Rules

## Core Design Principles
- Mobile-first iOS design with 375px max width
- Dark theme met glassmorphism effects
- Nederlandse taal en spraakherkenning
- Real-time agent monitoring en voice commands

## UI/UX Guidelines
- Gebruik iOS native design patterns (status bar, rounded corners, haptic feedback)
- Implement glassmorphism: backdrop-filter: blur(20px), rgba backgrounds
- Color scheme: #ff9500 (orange), #00bcd4 (cyan), #f44336 (red), #4CAF50 (green)
- Smooth animations: 0.3s ease transitions
- Touch-friendly: minimum 44px touch targets

## Component Architecture
- Agent cards met status indicators (ACTIVE, THINKING, ERROR, IDLE)
- Real-time performance bars en metrics
- Voice command interface met Nederlandse spraakherkenning
- Floating notifications voor feedback
- Command input field met voice button

## Voice Integration
- Nederlandse spraakcommando's: "Start crypto bot", "Agent status", "Restart agent 3"
- webkitSpeechRecognition met lang: 'nl-NL'
- Visual feedback tijdens voice recognition (pulse animation)
- Error handling voor unsupported browsers

## State Management
- Gebruik Map() voor agent storage
- Real-time updates elke 10-15 seconden
- Performance calculation based op active agents
- Proper cleanup van intervals en event listeners

## Agent System
- 4 default agents: Data Analysis, NLP, Image Processing, Crypto Trading
- Status states: active, thinking, error, idle
- Performance tracking (0-100%)
- Capabilities tags en version tracking
- Last activity timestamps

## Dutch Market Features
- Nederlandse interface teksten
- Integration ready voor Bitvavo, ING Bank, PostNL
- AVG/GDPR compliance considerations
- Local business process alignment

## Mobile Optimizations
- Touch gestures voor agent interaction
- Responsive design voor verschillende schermgroottes
- Efficient memory usage
- Progressive Web App capabilities

## Error Handling
- Graceful degradation voor voice recognition
- Fallback mechanisms voor failed API calls
- User-friendly error messages in Nederlands
- Console logging voor debugging

## Performance Requirements
- <200ms response time voor UI interactions
- Smooth 60fps animations
- Minimal battery drain
- Efficient WebSocket usage voor real-time data

## Security Considerations
- Input sanitization voor voice commands
- Secure WebSocket connections
- No localStorage usage in artifacts
- Safe error handling without exposing system details

## Code Quality
- ES6+ JavaScript met proper error handling
- Modular class-based architecture
- Consistent naming conventions (Nederlandse context)
- Comprehensive console logging voor debugging

## Future Extensibility
- Plugin architecture voor nieuwe agent types
- API integration points voor externe services
- Scalable state management
- Internationalization support