#!/bin/bash

# Agent Leon iOS Device Deployment Script
# Dit script helpt bij het deployen van Agent Leon naar een fysiek iOS apparaat

echo "🚀 Agent Leon - iOS Device Deployment"
echo "======================================"
echo ""

# Controleer of Xcode geïnstalleerd is
if ! command -v xcodebuild &> /dev/null; then
    echo "❌ Xcode is niet geïnstalleerd. Installeer Xcode eerst."
    exit 1
fi

# Controleer verbonden apparaten
echo "📱 Controleren op verbonden iOS apparaten..."
DEVICES=$(xcrun xctrace list devices | grep -E "iPhone|iPad" | grep -v "Simulator")

if [ -z "$DEVICES" ]; then
    echo "❌ Geen iOS apparaten gevonden."
    echo "   Zorg ervoor dat:"
    echo "   - Je iPhone/iPad via USB verbonden is"
    echo "   - Het apparaat is ontgrendeld"
    echo "   - Je 'Vertrouw deze computer' hebt geaccepteerd"
    exit 1
fi

echo "✅ Gevonden apparaten:"
echo "$DEVICES"
echo ""

# Controleer code signing
echo "🔐 Controleren code signing setup..."
IDENTITIES=$(security find-identity -v -p codesigning | grep "Apple Development\|iPhone Developer")

if [ -z "$IDENTITIES" ]; then
    echo "❌ Geen geldige code signing identiteit gevonden."
    echo ""
    echo "📋 Volg deze stappen:"
    echo "1. Open Xcode"
    echo "2. Ga naar Xcode > Preferences > Accounts"
    echo "3. Voeg je Apple ID toe (+)"
    echo "4. Selecteer je team"
    echo "5. Klik 'Download Manual Profiles'"
    echo ""
    echo "Voor gratis Apple ID:"
    echo "- Open het AgentLeon.xcodeproj in Xcode"
    echo "- Selecteer het AgentLeon target"
    echo "- Ga naar 'Signing & Capabilities'"
    echo "- Vink 'Automatically manage signing' aan"
    echo "- Selecteer je team"
    echo "- Wijzig Bundle Identifier naar iets unieks (bijv. com.jouwNaam.agentleon)"
    echo ""
    read -p "Druk Enter wanneer je de setup hebt voltooid..."
fi

echo "✅ Code signing identiteiten gevonden:"
echo "$IDENTITIES"
echo ""

# Build voor device
echo "🔨 Bouwen voor iOS apparaat..."
xcodebuild clean build \
    -project AgentLeon.xcodeproj \
    -scheme AgentLeon \
    -destination generic/platform=iOS \
    -configuration Release

if [ $? -eq 0 ]; then
    echo "✅ Build succesvol!"
    echo ""
    echo "📱 Deployment instructies:"
    echo "1. Open AgentLeon.xcodeproj in Xcode"
    echo "2. Selecteer je verbonden apparaat als destination"
    echo "3. Klik op de Run knop (▶️) of gebruik Cmd+R"
    echo "4. Accepteer eventuele vertrouwensmeldingen op je apparaat"
    echo ""
    echo "🎉 Agent Leon is klaar voor deployment!"
else
    echo "❌ Build gefaald. Controleer de error berichten hierboven."
    echo ""
    echo "🔧 Mogelijke oplossingen:"
    echo "- Controleer je Bundle Identifier is uniek"
    echo "- Zorg dat je team correct is ingesteld"
    echo "- Update je provisioning profiles"
fi 