<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>AgentLeon.debug.dylib</key>
		<data>
		94IGh3w5FNlspjcYn3RfcHQgnzM=
		</data>
		<key>Assets.car</key>
		<data>
		bsk8TsdRk1wt4sjMJJ1bJIbfGxI=
		</data>
		<key>Info.plist</key>
		<data>
		ydOcnw2/QYYKjyPzmj2hAveAtEc=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>__preview.dylib</key>
		<data>
		kc0xJY9b+kSSmqcNITc7JWXyC7M=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>AgentLeon.debug.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			xerdON0o2lBPum6bI00VllFVJWjLZC2UjVKU0nsqlJM=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			kLMJhowheosC1X+SaLfGR60lyjb9tHkolynJ6eYuqI0=
			</data>
		</dict>
		<key>__preview.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			C5UWo5uZfzAyAZxgVZbg25f2r3CnyNta5opL9LbI6Ao=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
