{"source": "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/OrderPlacementView.swift", "tables": {"Localizable": [{"comment": "", "key": "Order Result", "location": {"startingColumn": 16, "startingLine": 45}}, {"comment": "", "key": "OK", "location": {"startingColumn": 20, "startingLine": 46}}, {"comment": "", "key": "Place Order", "location": {"startingColumn": 18, "startingLine": 66}}, {"comment": "", "key": "Cancel", "location": {"startingColumn": 20, "startingLine": 59}}, {"comment": "", "key": "Settings", "location": {"startingColumn": 20, "startingLine": 72}}, {"comment": "", "key": "Trading Pair", "location": {"startingColumn": 18, "startingLine": 81}}, {"comment": "", "key": "Select Pair", "location": {"startingColumn": 20, "startingLine": 85}}, {"comment": "", "key": "Order Configuration", "location": {"startingColumn": 18, "startingLine": 99}}, {"comment": "", "key": "Order Type", "location": {"startingColumn": 20, "startingLine": 105}}, {"comment": "", "key": "Market", "location": {"startingColumn": 22, "startingLine": 106}}, {"comment": "", "key": "Limit", "location": {"startingColumn": 22, "startingLine": 107}}, {"comment": "", "key": "BUY", "location": {"startingColumn": 20, "startingLine": 124}}, {"comment": "", "key": "SELL", "location": {"startingColumn": 20, "startingLine": 134}}, {"comment": "", "key": "Amount", "location": {"startingColumn": 18, "startingLine": 148}}, {"comment": "", "key": "Enter amount", "location": {"startingColumn": 23, "startingLine": 151}}, {"comment": "", "key": "Price", "location": {"startingColumn": 18, "startingLine": 162}}, {"comment": "", "key": "Enter price", "location": {"startingColumn": 23, "startingLine": 165}}, {"comment": "", "key": "Order Summary", "location": {"startingColumn": 18, "startingLine": 176}}, {"comment": "", "key": "Placing Order...", "location": {"startingColumn": 39, "startingLine": 217}}, {"comment": "", "key": "Place %@ Order", "location": {"startingColumn": 60, "startingLine": 217}}]}, "version": 1}