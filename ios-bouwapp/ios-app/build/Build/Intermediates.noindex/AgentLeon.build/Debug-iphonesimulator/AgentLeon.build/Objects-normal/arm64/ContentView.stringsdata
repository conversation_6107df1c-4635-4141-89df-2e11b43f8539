{"source": "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/ContentView.swift", "tables": {"Localizable": [{"comment": "", "key": "AGENT LEON CONTROL", "location": {"startingColumn": 22, "startingLine": 76}}, {"comment": "", "key": "System Performance", "location": {"startingColumn": 26, "startingLine": 102}}, {"comment": "", "key": "%lld%%", "location": {"startingColumn": 26, "startingLine": 108}}, {"comment": "", "key": "Agent Rules", "location": {"startingColumn": 30, "startingLine": 181}}, {"comment": "", "key": "Agent: %@", "location": {"startingColumn": 42, "startingLine": 174}}, {"comment": "", "key": "Done", "location": {"startingColumn": 28, "startingLine": 185}}, {"comment": "", "key": "Priority: %lld", "location": {"startingColumn": 22, "startingLine": 220}}]}, "version": 1}