{"source": "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CryptoTradingView.swift", "tables": {"Localizable": [{"comment": "", "key": "Crypto Trading", "location": {"startingColumn": 30, "startingLine": 55}}, {"comment": "", "key": "Trading Tabs", "location": {"startingColumn": 24, "startingLine": 18}}, {"comment": "", "key": "Watchlist", "location": {"startingColumn": 26, "startingLine": 19}}, {"comment": "", "key": "Portfolio", "location": {"startingColumn": 26, "startingLine": 20}}, {"comment": "", "key": "Signals", "location": {"startingColumn": 26, "startingLine": 21}}, {"comment": "", "key": "Strategies", "location": {"startingColumn": 26, "startingLine": 22}}, {"comment": "", "key": "Balance", "location": {"startingColumn": 26, "startingLine": 73}}, {"comment": "", "key": "$%@", "location": {"startingColumn": 26, "startingLine": 76}}, {"comment": "", "key": "Total P&L", "location": {"startingColumn": 26, "startingLine": 85}}, {"comment": "", "key": "$%@", "location": {"startingColumn": 26, "startingLine": 88}}, {"comment": "", "key": "Auto Trading", "location": {"startingColumn": 22, "startingLine": 97}}, {"comment": "", "key": "Auto Trading", "location": {"startingColumn": 24, "startingLine": 103}}, {"comment": "", "key": "Trading Strategies", "location": {"startingColumn": 22, "startingLine": 174}}, {"comment": "", "key": "Available Strategies", "location": {"startingColumn": 22, "startingLine": 184}}, {"comment": "", "key": "$%@", "location": {"startingColumn": 26, "startingLine": 221}}, {"comment": "", "key": "24h", "location": {"startingColumn": 30, "startingLine": 241}}, {"comment": "", "key": "$%@", "location": {"startingColumn": 26, "startingLine": 288}}, {"comment": "", "key": "%@%%", "location": {"startingColumn": 26, "startingLine": 293}}, {"comment": "", "key": "Execute", "location": {"startingColumn": 24, "startingLine": 358}}, {"comment": "", "key": "@ $%@", "location": {"startingColumn": 26, "startingLine": 343}}, {"comment": "", "key": "BUY", "location": {"startingColumn": 48, "startingLine": 333}}, {"comment": "", "key": "SELL", "location": {"startingColumn": 56, "startingLine": 333}}, {"comment": "", "key": "ACTIVE", "location": {"startingColumn": 26, "startingLine": 397}}, {"comment": "", "key": "Risk: %@", "location": {"startingColumn": 22, "startingLine": 413}}, {"comment": "", "key": "Timeframe: %@", "location": {"startingColumn": 22, "startingLine": 421}}, {"comment": "", "key": "No Active Positions", "location": {"startingColumn": 18, "startingLine": 476}}, {"comment": "", "key": "Start trading to see your positions here", "location": {"startingColumn": 18, "startingLine": 481}}, {"comment": "", "key": "No Trading Signals", "location": {"startingColumn": 18, "startingLine": 497}}, {"comment": "", "key": "Trading signals will appear here when market conditions are right", "location": {"startingColumn": 18, "startingLine": 502}}, {"comment": "", "key": "Trade %@", "location": {"startingColumn": 30, "startingLine": 579}}, {"comment": "", "key": "$%@", "location": {"startingColumn": 26, "startingLine": 529}}, {"comment": "", "key": "Trade Type", "location": {"startingColumn": 28, "startingLine": 541}}, {"comment": "", "key": "Amount", "location": {"startingColumn": 31, "startingLine": 547}}, {"comment": "", "key": "<PERSON>", "location": {"startingColumn": 30, "startingLine": 542}}, {"comment": "", "key": "Short", "location": {"startingColumn": 30, "startingLine": 543}}, {"comment": "", "key": "Cancel", "location": {"startingColumn": 28, "startingLine": 557}}, {"comment": "", "key": "Execute Trade", "location": {"startingColumn": 28, "startingLine": 566}}]}, "version": 1}