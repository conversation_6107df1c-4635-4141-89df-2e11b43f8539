{"source": "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/ExchangeConfigurationView.swift", "tables": {"Localizable": [{"comment": "", "key": "Connection Test", "location": {"startingColumn": 16, "startingLine": 44}}, {"comment": "", "key": "OK", "location": {"startingColumn": 20, "startingLine": 45}}, {"comment": "", "key": "Exchange Configuration", "location": {"startingColumn": 18, "startingLine": 61}}, {"comment": "", "key": "Cancel", "location": {"startingColumn": 20, "startingLine": 54}}, {"comment": "", "key": "Save", "location": {"startingColumn": 20, "startingLine": 67}}, {"comment": "", "key": "Select Exchange", "location": {"startingColumn": 18, "startingLine": 80}}, {"comment": "", "key": "API Configuration", "location": {"startingColumn": 18, "startingLine": 122}}, {"comment": "", "key": "API Key", "location": {"startingColumn": 26, "startingLine": 129}}, {"comment": "", "key": "Enter API Key", "location": {"startingColumn": 33, "startingLine": 133}}, {"comment": "", "key": "API Secret", "location": {"startingColumn": 26, "startingLine": 139}}, {"comment": "", "key": "Enter API Secret", "location": {"startingColumn": 33, "startingLine": 143}}, {"comment": "", "key": "Passphrase", "location": {"startingColumn": 30, "startingLine": 150}}, {"comment": "", "key": "Enter Passphrase", "location": {"startingColumn": 37, "startingLine": 154}}, {"comment": "", "key": "Trading Modes", "location": {"startingColumn": 18, "startingLine": 167}}, {"comment": "", "key": "Settings", "location": {"startingColumn": 18, "startingLine": 194}}, {"comment": "", "key": "", "location": {"startingColumn": 28, "startingLine": 212}}, {"comment": "", "key": "Use Testnet", "location": {"startingColumn": 30, "startingLine": 202}}, {"comment": "", "key": "Use test environment for safer testing", "location": {"startingColumn": 30, "startingLine": 205}}, {"comment": "", "key": "", "location": {"startingColumn": 28, "startingLine": 232}}, {"comment": "", "key": "Enable Exchange", "location": {"startingColumn": 30, "startingLine": 222}}, {"comment": "", "key": "Allow trading on this exchange", "location": {"startingColumn": 30, "startingLine": 225}}, {"comment": "", "key": "Connection Test", "location": {"startingColumn": 18, "startingLine": 244}}, {"comment": "", "key": "Test API Connection", "location": {"startingColumn": 26, "startingLine": 250}}, {"comment": "", "key": "Verify your API credentials", "location": {"startingColumn": 26, "startingLine": 253}}, {"comment": "", "key": "Test", "location": {"startingColumn": 30, "startingLine": 270}}, {"comment": "", "key": "Reset to Defaults", "location": {"startingColumn": 20, "startingLine": 289}}, {"comment": "", "key": "Save Configuration", "location": {"startingColumn": 20, "startingLine": 298}}]}, "version": 1}