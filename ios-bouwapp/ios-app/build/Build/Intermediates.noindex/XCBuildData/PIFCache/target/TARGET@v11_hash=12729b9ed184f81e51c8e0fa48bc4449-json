{"buildConfigurations": [{"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_ASSET_PATHS": "\"AgentLeon/Preview Content\"", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation": "YES", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.example.AgentLeon", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "6e75eb6abe09515617c5a9f906ee3277b40007c027d966a48b626a61826a17c3", "name": "Debug"}, {"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_ASSET_PATHS": "\"AgentLeon/Preview Content\"", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation": "YES", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.example.AgentLeon", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "6e75eb6abe09515617c5a9f906ee3277a432bf55789c2e790d9ebb02b4855034", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "6e75eb6abe09515617c5a9f906ee3277a4be9ddef90b0ff19f325d7a15dd1cda", "guid": "6e75eb6abe09515617c5a9f906ee3277aa533a2f6fadb8c089dc9e77fa511744"}, {"fileReference": "6e75eb6abe09515617c5a9f906ee32778827a3bb6ee81b486012ca3f0bf36888", "guid": "6e75eb6abe09515617c5a9f906ee3277bc60ef3bf0f5557d0e03df231be2b710"}, {"fileReference": "6e75eb6abe09515617c5a9f906ee3277671b9d6da0854d548602c848d4cac475", "guid": "6e75eb6abe09515617c5a9f906ee327753910c13cf2b13951bb7e39a93fe5183"}, {"fileReference": "6e75eb6abe09515617c5a9f906ee3277d5cba42eb44954cab4b235e92dd344f0", "guid": "6e75eb6abe09515617c5a9f906ee327715d2ba3b2c24c598d481a304f2ef28c1"}, {"fileReference": "6e75eb6abe09515617c5a9f906ee327745007dd99efc3f64cb9dba1f57c85d21", "guid": "6e75eb6abe09515617c5a9f906ee3277e023c057510cc6ab29c1dfcf9cbf1343"}, {"fileReference": "6e75eb6abe09515617c5a9f906ee3277511103bda219276f0771f7c6689c82fc", "guid": "6e75eb6abe09515617c5a9f906ee3277759297a9ab535102c044a8285265656c"}, {"fileReference": "6e75eb6abe09515617c5a9f906ee3277f012492013787625510502254d4307b3", "guid": "6e75eb6abe09515617c5a9f906ee3277c257e1d2ff5f7f8ba9b2cd8dcd7eb68a"}, {"fileReference": "6e75eb6abe09515617c5a9f906ee327701e498f8fd97079cee51fa19e0e2ef14", "guid": "6e75eb6abe09515617c5a9f906ee3277ed68eb543f933fed5604f3f360a6a38e"}, {"fileReference": "6e75eb6abe09515617c5a9f906ee327760e30708778963ee2adf75fd0ea7c9da", "guid": "6e75eb6abe09515617c5a9f906ee32770b896991139d5e2c75d6947f40de3a54"}, {"fileReference": "6e75eb6abe09515617c5a9f906ee3277921122be8497893a312dfb9de3c5879b", "guid": "6e75eb6abe09515617c5a9f906ee3277e33f702dd1f8e1e649b1bbe9489950fa"}, {"fileReference": "6e75eb6abe09515617c5a9f906ee327739560a10e94d470715bdc9b4c2d05116", "guid": "6e75eb6abe09515617c5a9f906ee327744dfd8ef040aa135a9a56dd4c674f8ac"}, {"fileReference": "6e75eb6abe09515617c5a9f906ee32773780631c5db6b12e5fdb87b565370ea2", "guid": "6e75eb6abe09515617c5a9f906ee32776f45846b52bcf17e927ebfcd89872046"}, {"fileReference": "6e75eb6abe09515617c5a9f906ee32770515476ba5093c27e9ef2d2c984a0ab9", "guid": "6e75eb6abe09515617c5a9f906ee327791594e262093c0cbf7b6b214ae96131d"}, {"fileReference": "6e75eb6abe09515617c5a9f906ee3277e3bfd24a639a113d21f9e389292b5dde", "guid": "6e75eb6abe09515617c5a9f906ee3277bb3f64d6e68bdfc95febeee8a5a3b6b3"}, {"fileReference": "6e75eb6abe09515617c5a9f906ee327774f4cadeceb13d79545806817db21556", "guid": "6e75eb6abe09515617c5a9f906ee327722a642005c68e1d57da5f3cfac4b8e48"}, {"fileReference": "6e75eb6abe09515617c5a9f906ee3277f317bed039b1e27c17cb58814ca1ac8d", "guid": "6e75eb6abe09515617c5a9f906ee327760ac3b84b3e4a73b76e2d1eaa0a4901e"}, {"fileReference": "6e75eb6abe09515617c5a9f906ee3277b18b6c83c2fdefede17761eeadae47d2", "guid": "6e75eb6abe09515617c5a9f906ee3277975f5ad7eb5d33c63620c8b5a65211c0"}, {"fileReference": "6e75eb6abe09515617c5a9f906ee327766ff2d093779c11c431051e13fc8c3d5", "guid": "6e75eb6abe09515617c5a9f906ee327708c0e9651dca024dae3e831c6b890d7f"}], "guid": "6e75eb6abe09515617c5a9f906ee32771562fbe9e6a8cac33374c3a41e97f1ee", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "6e75eb6abe09515617c5a9f906ee32779ca766af870cf53238d2fd29b17798f9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "6e75eb6abe09515617c5a9f906ee32776ab44978db5cad2e8589b22e9e792177", "guid": "6e75eb6abe09515617c5a9f906ee3277642085c5b181060f8e89b0a44f46c94d"}, {"fileReference": "6e75eb6abe09515617c5a9f906ee3277a90687f63c7dacdf44ed2cce2e74cb51", "guid": "6e75eb6abe09515617c5a9f906ee327717841c9c2fb8991a5ede7bbf3d2d20e3"}], "guid": "6e75eb6abe09515617c5a9f906ee3277e44ec7f5ad67fb2c5d3e4542a8758ed5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "6e75eb6abe09515617c5a9f906ee32774a7c32c45d244f6386562a678643f315", "name": "AgentLeon", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "6e75eb6abe09515617c5a9f906ee3277df0bbb8ab292f0695743ed6687c4bbe5", "name": "AgentLeon.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}