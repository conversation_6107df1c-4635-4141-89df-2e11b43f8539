{"": {"diagnostics": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-master.dia", "emit-module-dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-master.swiftdeps"}, "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Agent.swift": {"const-values": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.swiftconstvalues", "dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.d", "diagnostics": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.dia", "index-unit-output-path": "/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.o", "llvm-bc": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.bc", "object": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.o", "swift-dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.swiftdeps", "swiftmodule": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent~partial.swiftmodule"}, "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/AgentLeonApp.swift": {"const-values": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.swiftconstvalues", "dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.d", "diagnostics": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.dia", "index-unit-output-path": "/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.o", "llvm-bc": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.bc", "object": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.o", "swift-dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.swiftdeps", "swiftmodule": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp~partial.swiftmodule"}, "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/AgentManager.swift": {"const-values": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.swiftconstvalues", "dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.d", "diagnostics": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.dia", "index-unit-output-path": "/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.o", "llvm-bc": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.bc", "object": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.o", "swift-dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.swiftdeps", "swiftmodule": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager~partial.swiftmodule"}, "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/AgentView.swift": {"const-values": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.swiftconstvalues", "dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.d", "diagnostics": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.dia", "index-unit-output-path": "/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.o", "llvm-bc": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.bc", "object": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.o", "swift-dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.swiftdeps", "swiftmodule": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView~partial.swiftmodule"}, "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/BybitAPIExtension.swift": {"const-values": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.swiftconstvalues", "dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.d", "diagnostics": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.dia", "index-unit-output-path": "/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.o", "llvm-bc": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.bc", "object": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.o", "swift-dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.swiftdeps", "swiftmodule": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension~partial.swiftmodule"}, "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CommandInputView.swift": {"const-values": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.swiftconstvalues", "dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.d", "diagnostics": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.dia", "index-unit-output-path": "/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.o", "llvm-bc": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.bc", "object": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.o", "swift-dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.swiftdeps", "swiftmodule": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView~partial.swiftmodule"}, "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/ContentView.swift": {"const-values": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.swiftconstvalues", "dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.d", "diagnostics": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.dia", "index-unit-output-path": "/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.o", "llvm-bc": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.bc", "object": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.o", "swift-dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.swiftdeps", "swiftmodule": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView~partial.swiftmodule"}, "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CryptoTradingAgent.swift": {"const-values": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.swiftconstvalues", "dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.d", "diagnostics": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.dia", "index-unit-output-path": "/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.o", "llvm-bc": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.bc", "object": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.o", "swift-dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.swiftdeps", "swiftmodule": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent~partial.swiftmodule"}, "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CryptoTradingView.swift": {"const-values": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.swiftconstvalues", "dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.d", "diagnostics": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.dia", "index-unit-output-path": "/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.o", "llvm-bc": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.bc", "object": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.o", "swift-dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.swiftdeps", "swiftmodule": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView~partial.swiftmodule"}, "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Exchange.swift": {"const-values": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.swiftconstvalues", "dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.d", "diagnostics": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.dia", "index-unit-output-path": "/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.o", "llvm-bc": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.bc", "object": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.o", "swift-dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.swiftdeps", "swiftmodule": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange~partial.swiftmodule"}, "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/ExchangeConfigurationView.swift": {"const-values": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.swiftconstvalues", "dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.d", "diagnostics": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.dia", "index-unit-output-path": "/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.o", "llvm-bc": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.bc", "object": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.o", "swift-dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.swiftdeps", "swiftmodule": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView~partial.swiftmodule"}, "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/KuCoinAPIExtension.swift": {"const-values": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.swiftconstvalues", "dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.d", "diagnostics": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.dia", "index-unit-output-path": "/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.o", "llvm-bc": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.bc", "object": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.o", "swift-dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.swiftdeps", "swiftmodule": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension~partial.swiftmodule"}, "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MEXCAPIExtension.swift": {"const-values": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.swiftconstvalues", "dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.d", "diagnostics": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.dia", "index-unit-output-path": "/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.o", "llvm-bc": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.bc", "object": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.o", "swift-dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.swiftdeps", "swiftmodule": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension~partial.swiftmodule"}, "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MultiExchangeAPIService.swift": {"const-values": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.swiftconstvalues", "dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.d", "diagnostics": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.dia", "index-unit-output-path": "/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.o", "llvm-bc": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.bc", "object": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.o", "swift-dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.swiftdeps", "swiftmodule": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService~partial.swiftmodule"}, "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MultiExchangePortfolio.swift": {"const-values": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.swiftconstvalues", "dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.d", "diagnostics": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.dia", "index-unit-output-path": "/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.o", "llvm-bc": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.bc", "object": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.o", "swift-dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.swiftdeps", "swiftmodule": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio~partial.swiftmodule"}, "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MultiExchangeTradingView.swift": {"const-values": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.swiftconstvalues", "dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.d", "diagnostics": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.dia", "index-unit-output-path": "/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.o", "llvm-bc": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.bc", "object": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.o", "swift-dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.swiftdeps", "swiftmodule": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView~partial.swiftmodule"}, "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/OrderPlacementView.swift": {"const-values": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.swiftconstvalues", "dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.d", "diagnostics": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.dia", "index-unit-output-path": "/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.o", "llvm-bc": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.bc", "object": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.o", "swift-dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.swiftdeps", "swiftmodule": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView~partial.swiftmodule"}, "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/TradingStrategyManager.swift": {"const-values": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.swiftconstvalues", "dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.d", "diagnostics": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.dia", "index-unit-output-path": "/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.o", "llvm-bc": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.bc", "object": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.o", "swift-dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.swiftdeps", "swiftmodule": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager~partial.swiftmodule"}, "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols.swift": {"const-values": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.d", "diagnostics": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.dia", "index-unit-output-path": "/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "swift-dependencies": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftdeps", "swiftmodule": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols~partial.swiftmodule"}}