{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex": {"is-mutated": true}, "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products": {"is-mutated": true}, "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app": {"is-mutated": true}, "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon": {"is-mutated": true}, "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib": {"is-mutated": true}, "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/_CodeSignature", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib", "/Users/<USER>/ios-bouwapp/ios-app/build/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache", "<Linked Binary Debug Dylib /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib>", "<target-AgentLeon-****************************************************************--begin-scanning>", "<target-AgentLeon-****************************************************************--end>", "<target-AgentLeon-****************************************************************--linker-inputs-ready>", "<target-AgentLeon-****************************************************************--modules-ready>", "<workspace-Debug-iphonesimulator18.5-iphonesimulator--stale-file-removal>"], "outputs": ["<all>"]}, "<target-AgentLeon-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/ssu/root.ssu.yaml", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/_CodeSignature", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/thinned", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_dependencies_thinned", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/unthinned", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_generated_info.plist", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Assets.car", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_signature", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/thinned", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/unthinned", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Info.plist", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/PkgInfo", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent.der", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon Swift Compilation Finished", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_lto.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_dependency_info.dat", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon Swift Compilation Requirements Finished", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftmodule", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftsourceinfo", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.abi.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-Swift.h", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftdoc", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/AgentLeon-Swift.h", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-all-non-framework-target-headers.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-all-target-headers.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-generated-files.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-own-target-headers.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-project-headers.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.DependencyMetadataFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.DependencyStaticMetadataFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-OutputFileMap.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.LinkFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.SwiftConstValuesFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.SwiftFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_const_extract_protocols.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/empty-AgentLeon.plist"], "roots": ["/tmp/AgentLeon.dst", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products"], "outputs": ["<target-AgentLeon-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>"]}, "<workspace-Debug-iphonesimulator18.5-iphonesimulator--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon-6e75eb6abe09515617c5a9f906ee3277-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<workspace-Debug-iphonesimulator18.5-iphonesimulator--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk /Users/<USER>/ios-bouwapp/ios-app/build/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk /Users/<USER>/ios-bouwapp/ios-app/build/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache", "<ClangStatCache /Users/<USER>/ios-bouwapp/ios-app/build/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "-o", "/Users/<USER>/ios-bouwapp/ios-app/build/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon.xcodeproj", "signature": "9164c5d69a077bdc8207bc0dbf9861fb"}, "P0:::CreateBuildDirectory /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex"]}, "P0:::CreateBuildDirectory /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator"]}, "P0:::CreateBuildDirectory /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products"]}, "P0:::CreateBuildDirectory /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon-6e75eb6abe09515617c5a9f906ee3277-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-AgentLeon-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-AgentLeon-****************************************************************--begin-compiling>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/ssu/root.ssu.yaml", "<ExtractAppIntentsMetadata /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Metadata.appintents>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.DependencyMetadataFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.DependencyStaticMetadataFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.SwiftConstValuesFileList"], "outputs": ["<target-AgentLeon-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--Barrier-ChangePermissions>", "<target-AgentLeon-****************************************************************--will-sign>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-AgentLeon-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--Barrier-StripSymbols>", "<target-AgentLeon-****************************************************************--will-sign>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-AgentLeon-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-AgentLeon-****************************************************************--will-sign>", "<target-AgentLeon-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>", "<CodeSign /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib>", "<CodeSign /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib>"], "outputs": ["<target-AgentLeon-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-AgentLeon-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--Barrier-GenerateStubAPI>", "<target-AgentLeon-****************************************************************--will-sign>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-AgentLeon-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--ProductPostprocessingTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-AgentLeon-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--Barrier-CodeSign>", "<target-AgentLeon-****************************************************************--will-sign>", "<target-AgentLeon-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>"], "outputs": ["<target-AgentLeon-****************************************************************--<PERSON><PERSON>-RegisterExecutionPolicyException>"]}, "P0:::Gate target-AgentLeon-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--<PERSON>ier-Validate>", "<target-AgentLeon-****************************************************************--will-sign>", "<target-AgentLeon-****************************************************************--begin-compiling>", "<Touch /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>"], "outputs": ["<target-AgentLeon-****************************************************************--<PERSON><PERSON>-RegisterProduct>"]}, "P0:::Gate target-AgentLeon-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--Barrier-CopyAside>", "<target-AgentLeon-****************************************************************--will-sign>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-AgentLeon-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--<PERSON><PERSON>-RegisterExecutionPolicyException>", "<target-AgentLeon-****************************************************************--will-sign>", "<target-AgentLeon-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>"], "outputs": ["<target-AgentLeon-****************************************************************--<PERSON>ier-Validate>"]}, "P0:::Gate target-AgentLeon-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--CustomTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--GeneratedFilesTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--ProductStructureTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent.der", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/Entitlements-Simulated.plist"], "outputs": ["<target-AgentLeon-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-all-non-framework-target-headers.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-all-target-headers.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-generated-files.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-own-target-headers.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-project-headers.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.hmap"], "outputs": ["<target-AgentLeon-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Info.plist", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/PkgInfo", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/empty-AgentLeon.plist"], "outputs": ["<target-AgentLeon-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--RealityAssetsTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-AgentLeon-****************************************************************--ModuleMapTaskProducer>", "<target-AgentLeon-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-AgentLeon-****************************************************************--InfoPlistTaskProducer>", "<target-AgentLeon-****************************************************************--SanitizerTaskProducer>", "<target-AgentLeon-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-AgentLeon-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-AgentLeon-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-AgentLeon-****************************************************************--TestTargetTaskProducer>", "<target-AgentLeon-****************************************************************--TestHostTaskProducer>", "<target-AgentLeon-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-AgentLeon-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-AgentLeon-****************************************************************--DocumentationTaskProducer>", "<target-AgentLeon-****************************************************************--CustomTaskProducer>", "<target-AgentLeon-****************************************************************--StubBinaryTaskProducer>", "<target-AgentLeon-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--start>", "<target-AgentLeon-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>"], "outputs": ["<target-AgentLeon-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--HeadermapTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-AgentLeon-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>"], "outputs": ["<target-AgentLeon-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--ProductPostprocessingTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-AgentLeon-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--begin-compiling>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-AgentLeon-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-AgentLeon-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/thinned/", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_dependencies_thinned", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/unthinned/", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_generated_info.plist", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Assets.car", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_signature", "<MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/unthinned>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon Swift Compilation Finished", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_lto.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon Swift Compilation Requirements Finished", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftmodule", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftsourceinfo", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.abi.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-Swift.h", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftdoc", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-OutputFileMap.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.LinkFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.SwiftFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_const_extract_protocols.json"], "outputs": ["<target-AgentLeon-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-AgentLeon-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--begin-compiling>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-AgentLeon-****************************************************************--generated-headers>"]}, "P0:::Gate target-AgentLeon-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--begin-compiling>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon Swift Compilation Requirements Finished", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftmodule", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftsourceinfo", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.abi.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-Swift.h", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftdoc", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/AgentLeon-Swift.h"], "outputs": ["<target-AgentLeon-****************************************************************--swift-generated-headers>"]}, "P0:target-AgentLeon-****************************************************************-:Debug:AppIntentsSSUTraining": {"tool": "shell", "description": "AppIntentsSSUTraining", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Info.plist", "<ExtractAppIntentsMetadata /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Metadata.appintents>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.DependencyMetadataFileList", "<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-AgentLeon-****************************************************************--entry>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/ssu/root.ssu.yaml"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsnltrainingprocessor", "--infoplist-path", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Info.plist", "--temp-dir-path", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/ssu", "--bundle-id", "com.example.AgentLeon", "--product-path", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app", "--extracted-metadata-path", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Metadata.appintents", "--metadata-file-list", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.DependencyMetadataFileList", "--archive-ssu-assets"], "env": {}, "working-directory": "/Users/<USER>/ios-bouwapp/ios-app", "signature": "22627ad5e670bf8a73c96879ca4d96ba"}, "P0:target-AgentLeon-****************************************************************-:Debug:CodeSign /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Agent.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/AgentLeonApp.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/AgentManager.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/AgentView.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Assets.xcassets/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/BybitAPIExtension.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CommandInputView.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/ContentView.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CryptoTradingAgent.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CryptoTradingView.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Exchange.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/ExchangeConfigurationView.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/KuCoinAPIExtension.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MEXCAPIExtension.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MultiExchangeAPIService.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MultiExchangePortfolio.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MultiExchangeTradingView.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/OrderPlacementView.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/TradingStrategyManager.swift/", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Info.plist/", "<CodeSign /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib>", "<CodeSign /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib>", "<target-AgentLeon-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-AgentLeon-****************************************************************--will-sign>", "<target-AgentLeon-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon normal>", "<TRIGGER: MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/_CodeSignature", "<CodeSign /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>", "<TRIGGER: CodeSign /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>"]}, "P0:target-AgentLeon-****************************************************************-:Debug:CodeSign /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Agent.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/AgentLeonApp.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/AgentManager.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/AgentView.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Assets.xcassets/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/BybitAPIExtension.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CommandInputView.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/ContentView.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CryptoTradingAgent.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CryptoTradingView.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Exchange.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/ExchangeConfigurationView.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/KuCoinAPIExtension.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MEXCAPIExtension.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MultiExchangeAPIService.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MultiExchangePortfolio.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MultiExchangeTradingView.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/OrderPlacementView.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/TradingStrategyManager.swift/", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Info.plist/", "<target-AgentLeon-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-AgentLeon-****************************************************************--will-sign>", "<target-AgentLeon-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib>"]}, "P0:target-AgentLeon-****************************************************************-:Debug:CodeSign /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Agent.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/AgentLeonApp.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/AgentManager.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/AgentView.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Assets.xcassets/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/BybitAPIExtension.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CommandInputView.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/ContentView.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CryptoTradingAgent.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CryptoTradingView.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Exchange.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/ExchangeConfigurationView.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/KuCoinAPIExtension.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MEXCAPIExtension.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MultiExchangeAPIService.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MultiExchangePortfolio.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MultiExchangeTradingView.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/OrderPlacementView.swift/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/TradingStrategyManager.swift/", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Info.plist/", "<CodeSign /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib>", "<target-AgentLeon-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-AgentLeon-****************************************************************--will-sign>", "<target-AgentLeon-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib>"]}, "P0:target-AgentLeon-****************************************************************-:Debug:CompileAssetCatalogVariant thinned /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app /Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Preview Content/Preview Assets.xcassets /Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant thinned /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app /Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Preview Content/Preview Assets.xcassets /Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Assets.xcassets", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Assets.xcassets/", "<MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/thinned>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/thinned", "<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/thinned/", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_dependencies_thinned", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_generated_info.plist_thinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Preview Content/Preview Assets.xcassets", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Assets.xcassets", "--compile", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/thinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_dependencies_thinned", "--output-partial-info-plist", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_generated_info.plist_thinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--filter-for-thinning-device-configuration", "iPhone17,3", "--filter-for-device-os-version", "18.4", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "17.5", "--platform", "iphonesimulator"], "env": {}, "working-directory": "/Users/<USER>/ios-bouwapp/ios-app", "control-enabled": false, "deps": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_dependencies_thinned"], "deps-style": "dependency-info", "signature": "cbed6c2cc68b1cb3c2e8c09d4bc6bfd3"}, "P0:target-AgentLeon-****************************************************************-:Debug:CompileAssetCatalogVariant unthinned /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app /Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Preview Content/Preview Assets.xcassets /Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant unthinned /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app /Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Preview Content/Preview Assets.xcassets /Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Assets.xcassets", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Assets.xcassets/", "<MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/unthinned>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/unthinned", "<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/unthinned/", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_generated_info.plist_unthinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Preview Content/Preview Assets.xcassets", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Assets.xcassets", "--compile", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/unthinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_dependencies_unthinned", "--output-partial-info-plist", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_generated_info.plist_unthinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "17.5", "--platform", "iphonesimulator"], "env": {}, "working-directory": "/Users/<USER>/ios-bouwapp/ios-app", "control-enabled": false, "deps": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_dependencies_unthinned"], "deps-style": "dependency-info", "signature": "dff7d180697c97331304579c6451a89a"}, "P0:target-AgentLeon-****************************************************************-:Debug:CopySwiftLibs /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib", "<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-AgentLeon-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>"], "deps": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-AgentLeon-****************************************************************-:Debug:ExtractAppIntentsMetadata": {"tool": "shell", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Agent.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/AgentManager.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/ContentView.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/AgentView.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CommandInputView.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CryptoTradingAgent.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CryptoTradingView.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Exchange.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MultiExchangeAPIService.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MultiExchangeTradingView.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MultiExchangePortfolio.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/OrderPlacementView.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/ExchangeConfigurationView.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/TradingStrategyManager.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MEXCAPIExtension.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/BybitAPIExtension.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/KuCoinAPIExtension.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/AgentLeonApp.swift", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.DependencyMetadataFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.DependencyStaticMetadataFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_dependency_info.dat", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.SwiftFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.SwiftConstValuesFileList", "<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-AgentLeon-****************************************************************--entry>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Metadata.appintents>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor", "--toolchain-dir", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "--module-name", "AgentLeon", "--sdk-root", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "--xcode-version", "16F6", "--platform-family", "iOS", "--deployment-target", "17.5", "--bundle-identifier", "com.example.AgentLeon", "--output", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app", "--target-triple", "arm64-apple-ios17.5-simulator", "--binary-file", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon", "--dependency-file", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_dependency_info.dat", "--stringsdata-file", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "--source-file-list", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.SwiftFileList", "--metadata-file-list", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.DependencyMetadataFileList", "--static-metadata-file-list", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.DependencyStaticMetadataFileList", "--swift-const-vals-list", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.SwiftConstValuesFileList", "--compile-time-extraction", "--deployment-aware-processing", "--validate-assistant-intents", "--no-app-shortcuts-localization"], "env": {}, "working-directory": "/Users/<USER>/ios-bouwapp/ios-app", "signature": "29c2101824db04dab6cbe21543916a4e"}, "P0:target-AgentLeon-****************************************************************-:Debug:Gate target-AgentLeon-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/AgentLeon.dst>", "<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-AgentLeon-****************************************************************--begin-compiling>"]}, "P0:target-AgentLeon-****************************************************************-:Debug:Gate target-AgentLeon-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/AgentLeon.dst>", "<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-AgentLeon-****************************************************************--begin-linking>"]}, "P0:target-AgentLeon-****************************************************************-:Debug:Gate target-AgentLeon-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/AgentLeon.dst>", "<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--begin-scanning>"]}, "P0:target-AgentLeon-****************************************************************-:Debug:Gate target-AgentLeon-****************************************************************--end": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--entry>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/ssu/root.ssu.yaml", "<CodeSign /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>", "<CodeSign /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib>", "<CodeSign /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/thinned/", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_dependencies_thinned", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/unthinned/", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>", "<ExtractAppIntentsMetadata /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Metadata.appintents>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_generated_info.plist", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Assets.car", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_signature", "<MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/unthinned>", "<MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Info.plist", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/PkgInfo", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon Swift Compilation Finished", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "<Touch /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>", "<Validate /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>", "<ValidateDevelopmentAssets-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_lto.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon Swift Compilation Requirements Finished", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftmodule", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftsourceinfo", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.abi.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-Swift.h", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftdoc", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/AgentLeon-Swift.h", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/AgentLeon-Swift.h", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-all-non-framework-target-headers.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-all-target-headers.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-generated-files.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-own-target-headers.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-project-headers.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.DependencyMetadataFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.DependencyStaticMetadataFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-OutputFileMap.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.LinkFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.SwiftConstValuesFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.SwiftFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_const_extract_protocols.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/empty-AgentLeon.plist", "<target-AgentLeon-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-AgentLeon-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-AgentLeon-****************************************************************--Barrier-ChangePermissions>", "<target-AgentLeon-****************************************************************--Barrier-CodeSign>", "<target-AgentLeon-****************************************************************--Barrier-CopyAside>", "<target-AgentLeon-****************************************************************--Barrier-GenerateStubAPI>", "<target-AgentLeon-****************************************************************--<PERSON><PERSON>-RegisterExecutionPolicyException>", "<target-AgentLeon-****************************************************************--<PERSON><PERSON>-RegisterProduct>", "<target-AgentLeon-****************************************************************--Barrier-StripSymbols>", "<target-AgentLeon-****************************************************************--<PERSON>ier-Validate>", "<target-AgentLeon-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-AgentLeon-****************************************************************--CustomTaskProducer>", "<target-AgentLeon-****************************************************************--DocumentationTaskProducer>", "<target-AgentLeon-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-AgentLeon-****************************************************************--GeneratedFilesTaskProducer>", "<target-AgentLeon-****************************************************************--HeadermapTaskProducer>", "<target-AgentLeon-****************************************************************--InfoPlistTaskProducer>", "<target-AgentLeon-****************************************************************--ModuleMapTaskProducer>", "<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--ProductPostprocessingTaskProducer>", "<target-AgentLeon-****************************************************************--ProductStructureTaskProducer>", "<target-AgentLeon-****************************************************************--RealityAssetsTaskProducer>", "<target-AgentLeon-****************************************************************--SanitizerTaskProducer>", "<target-AgentLeon-****************************************************************--StubBinaryTaskProducer>", "<target-AgentLeon-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-AgentLeon-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-AgentLeon-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-AgentLeon-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-AgentLeon-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-AgentLeon-****************************************************************--TestHostTaskProducer>", "<target-AgentLeon-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-AgentLeon-****************************************************************--TestTargetTaskProducer>", "<target-AgentLeon-****************************************************************--copy-headers-completion>", "<target-AgentLeon-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-AgentLeon-****************************************************************--generated-headers>", "<target-AgentLeon-****************************************************************--swift-generated-headers>"], "outputs": ["<target-AgentLeon-****************************************************************--end>"]}, "P0:target-AgentLeon-****************************************************************-:Debug:Gate target-AgentLeon-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/AgentLeon.dst>", "<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-AgentLeon-****************************************************************--begin-compiling>"], "outputs": ["<target-AgentLeon-****************************************************************--entry>"]}, "P0:target-AgentLeon-****************************************************************-:Debug:Gate target-AgentLeon-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/AgentLeon.dst>", "<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-AgentLeon-****************************************************************--immediate>"]}, "P0:target-AgentLeon-****************************************************************-:Debug:Gate target-AgentLeon-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--begin-compiling>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-ExecutorLinkFileList-normal-arm64.txt", "<Linked Binary /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_lto.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon Swift Compilation Requirements Finished", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftmodule", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftsourceinfo", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.abi.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-Swift.h", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftdoc", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.LinkFileList"], "outputs": ["<target-AgentLeon-****************************************************************--linker-inputs-ready>"]}, "P0:target-AgentLeon-****************************************************************-:Debug:Gate target-AgentLeon-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--begin-compiling>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon Swift Compilation Requirements Finished", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftmodule", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftsourceinfo", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.abi.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-Swift.h", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftdoc", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/AgentLeon-Swift.h"], "outputs": ["<target-AgentLeon-****************************************************************--modules-ready>"]}, "P0:target-AgentLeon-****************************************************************-:Debug:Gate target-AgentLeon-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--begin-compiling>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/ssu/root.ssu.yaml", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/thinned/", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_dependencies_thinned", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/unthinned/", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>", "<ExtractAppIntentsMetadata /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Metadata.appintents>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_generated_info.plist", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Assets.car", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_signature", "<MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/unthinned>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent.der", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon Swift Compilation Finished", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_lto.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon Swift Compilation Requirements Finished", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftmodule", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftsourceinfo", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.abi.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-Swift.h", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftdoc", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/AgentLeon-Swift.h", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.DependencyMetadataFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.DependencyStaticMetadataFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-OutputFileMap.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.LinkFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.SwiftConstValuesFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.SwiftFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_const_extract_protocols.json", "<target-AgentLeon-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-AgentLeon-****************************************************************--unsigned-product-ready>"]}, "P0:target-AgentLeon-****************************************************************-:Debug:Gate target-AgentLeon-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-AgentLeon-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-AgentLeon-****************************************************************--will-sign>"]}, "P0:target-AgentLeon-****************************************************************-:Debug:GenerateAssetSymbols /Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Preview Content/Preview Assets.xcassets /Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Preview Content/Preview Assets.xcassets /Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Assets.xcassets", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Assets.xcassets/", "<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Preview Content/Preview Assets.xcassets", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Assets.xcassets", "--compile", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "17.5", "--platform", "iphonesimulator", "--bundle-identifier", "com.example.AgentLeon", "--generate-swift-asset-symbols", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/ios-bouwapp/ios-app", "control-enabled": false, "signature": "01892f143018f49a9415dcccfb231539"}, "P0:target-AgentLeon-****************************************************************-:Debug:LinkAssetCatalog /Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Preview Content/Preview Assets.xcassets /Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Assets.xcassets": {"tool": "link-assetcatalog", "description": "LinkAssetCatalog /Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Preview Content/Preview Assets.xcassets /Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Assets.xcassets", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Assets.xcassets/", "<MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/thinned/", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/unthinned/", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_signature", "<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_generated_info.plist", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Assets.car"], "deps": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_dependencies"}, "P0:target-AgentLeon-****************************************************************-:Debug:LinkAssetCatalogSignature": {"tool": "link-assetcatalog", "description": "LinkAssetCatalogSignature", "inputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_signature"], "always-out-of-date": true}, "P0:target-AgentLeon-****************************************************************-:Debug:MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/thinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/thinned", "inputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/thinned", "<MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/thinned>"]}, "P0:target-AgentLeon-****************************************************************-:Debug:MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/unthinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/unthinned", "inputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/unthinned", "<MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_output/unthinned>"]}, "P0:target-AgentLeon-****************************************************************-:Debug:MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app", "inputs": ["<target-AgentLeon-****************************************************************--start>", "<target-AgentLeon-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app", "<MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>", "<TRIGGER: MkDir /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>"]}, "P0:target-AgentLeon-****************************************************************-:Debug:ProcessInfoPlistFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Info.plist /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/empty-AgentLeon.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Info.plist /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/empty-AgentLeon.plist", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/empty-AgentLeon.plist", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/assetcatalog_generated_info.plist", "<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--entry>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Info.plist", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/PkgInfo"]}, "P0:target-AgentLeon-****************************************************************-:Debug:ProcessProductPackaging  /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging  /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/Entitlements-Simulated.plist", "<target-AgentLeon-****************************************************************--ProductStructureTaskProducer>", "<target-AgentLeon-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent"]}, "P0:target-AgentLeon-****************************************************************-:Debug:ProcessProductPackagingDER /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent.der", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent", "<target-AgentLeon-****************************************************************--ProductStructureTaskProducer>", "<target-AgentLeon-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent", "-o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/ios-bouwapp/ios-app", "signature": "9422a05036d95ae73da147d7aaa6a2b1"}, "P0:target-AgentLeon-****************************************************************-:Debug:RegisterExecutionPolicyException /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app", "<target-AgentLeon-****************************************************************--Barrier-CodeSign>", "<target-AgentLeon-****************************************************************--will-sign>", "<target-AgentLeon-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>"]}, "P0:target-AgentLeon-****************************************************************-:Debug:SwiftDriver Compilation AgentLeon normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation AgentLeon normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Agent.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/AgentManager.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/ContentView.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/AgentView.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CommandInputView.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CryptoTradingAgent.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CryptoTradingView.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Exchange.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MultiExchangeAPIService.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MultiExchangeTradingView.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MultiExchangePortfolio.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/OrderPlacementView.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/ExchangeConfigurationView.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/TradingStrategyManager.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MEXCAPIExtension.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/BybitAPIExtension.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/KuCoinAPIExtension.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/AgentLeonApp.swift", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.SwiftFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-OutputFileMap.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_const_extract_protocols.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-generated-files.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-own-target-headers.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-all-target-headers.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-project-headers.hmap", "<ClangStatCache /Users/<USER>/ios-bouwapp/ios-app/build/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache>", "<target-AgentLeon-****************************************************************--generated-headers>", "<target-AgentLeon-****************************************************************--copy-headers-completion>", "<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon Swift Compilation Finished", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.swiftconstvalues", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues"]}, "P0:target-AgentLeon-****************************************************************-:Debug:Touch /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app": {"tool": "shell", "description": "Touch /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app", "<target-AgentLeon-****************************************************************--<PERSON>ier-Validate>", "<target-AgentLeon-****************************************************************--will-sign>", "<target-AgentLeon-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app"], "env": {}, "working-directory": "/Users/<USER>/ios-bouwapp/ios-app", "signature": "34697551697d4d28a3cbd7167e2bc30e"}, "P0:target-AgentLeon-****************************************************************-:Debug:Validate /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/Info.plist", "<target-AgentLeon-****************************************************************--<PERSON><PERSON>-RegisterExecutionPolicyException>", "<target-AgentLeon-****************************************************************--will-sign>", "<target-AgentLeon-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>"], "outputs": ["<Validate /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app>"]}, "P0:target-AgentLeon-****************************************************************-:Debug:ValidateDevelopmentAssets /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build": {"tool": "validate-development-assets", "description": "ValidateDevelopmentAssets /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Preview Content", "<target-AgentLeon-****************************************************************--entry>"], "outputs": ["<ValidateDevelopmentAssets-/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build>"], "allow-missing-inputs": true}, "P2:::WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon-6e75eb6abe09515617c5a9f906ee3277-VFS-iphonesimulator/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon-6e75eb6abe09515617c5a9f906ee3277-VFS-iphonesimulator/all-product-headers.yaml", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon-6e75eb6abe09515617c5a9f906ee3277-VFS-iphonesimulator/all-product-headers.yaml"]}, "P2:target-AgentLeon-****************************************************************-:Debug:ConstructStubExecutorLinkFileList /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-ExecutorLinkFileList-normal-arm64.txt": {"tool": "construct-stub-executor-input-file-list", "description": "ConstructStubExecutorLinkFileList /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-ExecutorLinkFileList-normal-arm64.txt", "inputs": ["/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib/libPreviewsJITStubExecutor_no_swift_entry_point.a", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib/libPreviewsJITStubExecutor.a", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib", "<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-ExecutorLinkFileList-normal-arm64.txt"]}, "P2:target-AgentLeon-****************************************************************-:Debug:Copy /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftsourceinfo", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftsourceinfo/", "<target-AgentLeon-****************************************************************--copy-headers-completion>", "<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo"]}, "P2:target-AgentLeon-****************************************************************-:Debug:Copy /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.abi.json", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.abi.json/", "<target-AgentLeon-****************************************************************--copy-headers-completion>", "<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.abi.json"]}, "P2:target-AgentLeon-****************************************************************-:Debug:Copy /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftdoc", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftdoc/", "<target-AgentLeon-****************************************************************--copy-headers-completion>", "<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.swiftdoc"]}, "P2:target-AgentLeon-****************************************************************-:Debug:Copy /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftmodule", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftmodule/", "<target-AgentLeon-****************************************************************--copy-headers-completion>", "<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.swiftmodule/arm64-apple-ios-simulator.swiftmodule"]}, "P2:target-AgentLeon-****************************************************************-:Debug:Ld /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon normal": {"tool": "shell", "description": "Ld /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon normal", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent.der", "<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon", "<Linked Binary /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon>", "<TRIGGER: Ld /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios17.5-simulator", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "-O0", "-L/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-e", "___debug_blank_executor_main", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_dylib", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-DebugDylibPath-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_instlnm", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-DebugDylibInstallName-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-filelist", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-ExecutorLinkFileList-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent.der", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon"], "env": {}, "working-directory": "/Users/<USER>/ios-bouwapp/ios-app", "signature": "08216f17eaf9065d3a04d70360171662"}, "P2:target-AgentLeon-****************************************************************-:Debug:Ld /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib normal", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Agent.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentManager.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CommandInputView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingAgent.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/CryptoTradingView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/Exchange.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeAPIService.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangeTradingView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MultiExchangePortfolio.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/OrderPlacementView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/ExchangeConfigurationView.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/TradingStrategyManager.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/MEXCAPIExtension.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/BybitAPIExtension.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/KuCoinAPIExtension.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeonApp.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.LinkFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent.der", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator", "<target-AgentLeon-****************************************************************--generated-headers>", "<target-AgentLeon-****************************************************************--swift-generated-headers>", "<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib", "<Linked Binary Debug Dylib /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_lto.o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios17.5-simulator", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "-O0", "-L/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "-L/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "-F/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator", "-filelist", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.LinkFileList", "-install_name", "@rpath/AgentLeon.debug.dylib", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_lto.o", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_dependency_info.dat", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftmodule", "-<PERSON><PERSON><PERSON>", "-alias", "-<PERSON><PERSON><PERSON>", "_main", "-<PERSON><PERSON><PERSON>", "___debug_main_executable_dylib_entry_point", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/AgentLeon.debug.dylib"], "env": {}, "working-directory": "/Users/<USER>/ios-bouwapp/ios-app", "deps": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_dependency_info.dat"], "deps-style": "dependency-info", "signature": "6e4fe2ddb82f053008609a7dbf88f6f9"}, "P2:target-AgentLeon-****************************************************************-:Debug:Ld /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib normal", "inputs": ["<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib", "<Linked Binary Preview Injection Dylib /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib>", "<TRIGGER: Ld /Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios17.5-simulator", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "-O0", "-L/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator", "-install_name", "@rpath/AgentLeon.debug.dylib", "-dead_strip", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_dependency_info.dat", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.app-Simulated.xcent.der", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products/Debug-iphonesimulator/AgentLeon.app/__preview.dylib"], "env": {}, "working-directory": "/Users/<USER>/ios-bouwapp/ios-app", "signature": "22b63c8b0ef060315c56d2e24d8587cb"}, "P2:target-AgentLeon-****************************************************************-:Debug:SwiftDriver Compilation Requirements AgentLeon normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements AgentLeon normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Agent.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/AgentManager.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/ContentView.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/AgentView.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CommandInputView.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CryptoTradingAgent.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/CryptoTradingView.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/Exchange.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MultiExchangeAPIService.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MultiExchangeTradingView.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MultiExchangePortfolio.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/OrderPlacementView.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/ExchangeConfigurationView.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/TradingStrategyManager.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/MEXCAPIExtension.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/BybitAPIExtension.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/KuCoinAPIExtension.swift", "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon/AgentLeonApp.swift", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.SwiftFileList", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-OutputFileMap.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_const_extract_protocols.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-generated-files.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-own-target-headers.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-all-target-headers.hmap", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-project-headers.hmap", "<ClangStatCache /Users/<USER>/ios-bouwapp/ios-app/build/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache>", "<target-AgentLeon-****************************************************************--copy-headers-completion>", "<target-AgentLeon-****************************************************************--ModuleVerifierTaskProducer>", "<target-AgentLeon-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon Swift Compilation Requirements Finished", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftmodule", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftsourceinfo", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.abi.json", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-Swift.h", "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.swiftdoc"]}, "P2:target-AgentLeon-****************************************************************-:Debug:SwiftMergeGeneratedHeaders /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/AgentLeon-Swift.h /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/AgentLeon-Swift.h /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-Swift.h", "inputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-Swift.h", "<target-AgentLeon-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/AgentLeon-Swift.h"]}, "P2:target-AgentLeon-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-DebugDylibInstallName-normal-arm64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-DebugDylibInstallName-normal-arm64.txt", "inputs": ["<target-AgentLeon-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-DebugDylibInstallName-normal-arm64.txt"]}, "P2:target-AgentLeon-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-DebugDylibPath-normal-arm64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-DebugDylibPath-normal-arm64.txt", "inputs": ["<target-AgentLeon-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-DebugDylibPath-normal-arm64.txt"]}, "P2:target-AgentLeon-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-all-non-framework-target-headers.hmap", "inputs": ["<target-AgentLeon-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-all-non-framework-target-headers.hmap"]}, "P2:target-AgentLeon-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-all-target-headers.hmap", "inputs": ["<target-AgentLeon-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-all-target-headers.hmap"]}, "P2:target-AgentLeon-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-generated-files.hmap", "inputs": ["<target-AgentLeon-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-generated-files.hmap"]}, "P2:target-AgentLeon-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-own-target-headers.hmap", "inputs": ["<target-AgentLeon-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-own-target-headers.hmap"]}, "P2:target-AgentLeon-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-project-headers.hmap", "inputs": ["<target-AgentLeon-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon-project-headers.hmap"]}, "P2:target-AgentLeon-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.DependencyMetadataFileList", "inputs": ["<target-AgentLeon-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.DependencyMetadataFileList"]}, "P2:target-AgentLeon-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.DependencyStaticMetadataFileList", "inputs": ["<target-AgentLeon-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.DependencyStaticMetadataFileList"]}, "P2:target-AgentLeon-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.hmap", "inputs": ["<target-AgentLeon-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/AgentLeon.hmap"]}, "P2:target-AgentLeon-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/Entitlements-Simulated.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/Entitlements-Simulated.plist", "inputs": ["<target-AgentLeon-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/DerivedSources/Entitlements-Simulated.plist"]}, "P2:target-AgentLeon-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-OutputFileMap.json", "inputs": ["<target-AgentLeon-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon-OutputFileMap.json"]}, "P2:target-AgentLeon-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.LinkFileList", "inputs": ["<target-AgentLeon-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.LinkFileList"]}, "P2:target-AgentLeon-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.SwiftConstValuesFileList", "inputs": ["<target-AgentLeon-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.SwiftConstValuesFileList"]}, "P2:target-AgentLeon-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.SwiftFileList", "inputs": ["<target-AgentLeon-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon.SwiftFileList"]}, "P2:target-AgentLeon-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_const_extract_protocols.json", "inputs": ["<target-AgentLeon-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/Objects-normal/arm64/AgentLeon_const_extract_protocols.json"]}, "P2:target-AgentLeon-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/empty-AgentLeon.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/empty-AgentLeon.plist", "inputs": ["<target-AgentLeon-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/AgentLeon.build/Debug-iphonesimulator/AgentLeon.build/empty-AgentLeon.plist"]}}}