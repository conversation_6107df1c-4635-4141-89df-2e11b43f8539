{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "6e75eb6abe09515617c5a9f906ee32774a7c32c45d244f6386562a678643f315"}], "containerPath": "/Users/<USER>/ios-bouwapp/ios-app/AgentLeon.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "iphonesimulator", "sdk": "iphonesimulator18.5", "sdkVariant": "iphonesimulator", "supportedArchitectures": ["arm64", "x86_64"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Products", "derivedDataPath": "/Users/<USER>/ios-bouwapp/ios-app/build", "indexDataStoreFolderPath": "/Users/<USER>/ios-bouwapp/ios-app/build/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/ios-bouwapp/ios-app/build/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/ios-bouwapp/ios-app/build/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Debug", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "build", "ASSETCATALOG_FILTER_FOR_DEVICE_MODEL": "iPhone17,3", "ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION": "18.4", "ASSETCATALOG_FILTER_FOR_THINNING_DEVICE_CONFIGURATION": "iPhone17,3", "BUILD_ACTIVE_RESOURCES_ONLY": "YES", "COLOR_DIAGNOSTICS": "YES", "diagnostic_message_length": "108", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "YES", "ONLY_ACTIVE_ARCH": "YES", "TARGET_DEVICE_IDENTIFIER": "09CB48D8-0AE9-4ED8-8584-31EF53D1D45B", "TARGET_DEVICE_MODEL": "iPhone17,3", "TARGET_DEVICE_OS_VERSION": "18.4", "TARGET_DEVICE_PLATFORM_NAME": "iphonesimulator"}}}}, "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}