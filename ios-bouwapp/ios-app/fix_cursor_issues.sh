#!/bin/bash

echo "🔧 Cursor Issues Fix Script voor QuantumAgents iOS"
echo "=================================================="

# 1. Clean Xcode project
echo "📱 Cleaning Xcode project..."
xcodebuild clean -project AgentLeon.xcodeproj

# 2. Remove derived data
echo "🗑️  Removing DerivedData..."
rm -rf ~/Library/Developer/Xcode/DerivedData/AgentLeon-*

# 3. Reset Cursor cache
echo "🔄 Resetting Cursor cache..."
rm -rf ~/.cursor/logs/*
rm -rf ~/.cursor/CachedData/*

# 4. Fix file permissions
echo "🔐 Fixing file permissions..."
chmod -R 755 .
chmod +x *.sh

# 5. Clean temporary files
echo "🧹 Cleaning temporary files..."
find . -name ".DS_Store" -delete
find . -name "*.tmp" -delete
find . -name "*.log" -delete

# 6. Verify settings
echo "⚙️  Verifying settings..."
if [ -f ".vscode/settings.json" ]; then
    echo "✅ VSCode settings found"
else
    echo "❌ VSCode settings missing"
fi

# 7. Check Xcode project
echo "🔍 Checking Xcode project..."
if xcodebuild -project AgentLeon.xcodeproj -list > /dev/null 2>&1; then
    echo "✅ Xcode project is valid"
else
    echo "❌ Xcode project has issues"
fi

echo ""
echo "🎉 Fix completed!"
echo ""
echo "📋 Next steps:"
echo "1. Herstart Cursor volledig"
echo "2. Open alleen deze folder: ios-bouwapp/ios-app"
echo "3. Ga naar Cursor Settings en zet 'Remote Agent Mode' uit"
echo "4. Test de AI completion"
echo ""
echo "✨ Je Cursor zou nu perfect moeten werken!"
