# 📸 Agent Leon - Screenshot & Mockup Guide (NL)

## 📐 Resoluties & Devices
| Device | Resolutie (px) | Aantal screenshots |
|--------|----------------|--------------------|
| iPhone 6.7" (Pro Max) | 1290×2796 | 5 |
| iPhone 6.1" | 1179×2556 | 5 |
| iPad 12.9" | 2048×2732 | 5 |

---

## 🎨 Figma Template Structuur
- **Frame Naming**:
  - `01_Dashboard`
  - `02_MultiExchange`
  - `03_AI_Strategy`
  - `04_Trading`
  - `05_Portfolio`
- **Components**: status bar, notch mask, footer shadow
- **Export Settings**: PNG, @1x, sRGB

---

## 🏗️ Mockup Generator (Swift Package: `DevicePreview`) — Optie 1 (snel)
```swift
import DevicePreview

let previews: [DevicePreview.Preview] = [
    .init(image: "Screenshot_01_Dashboard", device: .iPhone14ProMax),
    .init(image: "Screenshot_02_MultiExchange", device: .iPhone14ProMax),
    .init(image: "Screenshot_03_AI_Strategy", device: .iPhone14ProMax)
]

DevicePreview.export(previews, output: URL("~/Desktop/Mockups"))
```

---

## 🤖 UITest Automatische Screenshots — Optie 2 (geavanceerd)
`UITests/AgentLeonSnapshotUITests.swift`
```swift
import XCTest
#if canImport(SnapshotTesting)
import SnapshotTesting
#endif

final class AgentLeonSnapshotUITests: XCTestCase {
    override func setUp() {
        continueAfterFailure = false
        XCUIApplication().launch()
    }

    func testDashboardScreenshot() {
        let app = XCUIApplication()
        assertSnapshot(matching: app, as: .image(on: .iPhone13ProMax), named: "01_Dashboard")
    }

    func testMultiExchangeScreenshot() {
        let app = XCUIApplication()
        // Navigate to Multi-Exchange view
        app.buttons["Exchanges"].tap()
        assertSnapshot(matching: app, as: .image(on: .iPhone13ProMax), named: "02_MultiExchange")
    }
}
```

### Fastlane Snapfile (optioneel)
`fastlane/Snapfile`
```ruby
devices(["iPhone 15 Pro Max", "iPad Pro (12.9-inch) (6th generation)"])
languages(["nl-NL"])
scheme("AgentLeon")
output_directory("fastlane/screenshots")
clear_previous_screenshots(true)
```

---

## 🖼️ Screenshot Titels & Ondertitels (NL)
| # | Titel | Ondertitel |
|---|-------|------------|
| 1 | 🤖 AI Trading Dashboard | Real-time marktanalyse |
| 2 | 🔄 Alle Exchanges Samen | KuCoin, MEXC, Bybit & meer |
| 3 | 🧠 AI-Gestuurde Strategieën | Slimme automatisering |
| 4 | 📊 Professioneel Traden | Buy/Sell met AI ondersteuning |
| 5 | 📈 Portfolio Tracking | Winst/verlies overzicht |

---

## 3 Mockup Opties
1. **Snel (DevicePreview)** – Genereer device renders direct in Xcode (sneller, minder custom).
2. **Professioneel (Figma + MockupShots)** – Export Figma frames → upload naar mockupshots.com voor 3D renders.
3. **Verrassend (AR Preview)** – Gebruik RealityKit om AR-scènes van je screenshots te tonen in promo-video's.

Kies optie 1–3 en ik lever de bijbehorende scripts of Figma assets. 🤖 