#!/bin/bash

# 🚀 Agent Leon - Quick Deployment Script
# Run this after setting up Apple Developer account in Xcode

echo "🤖 Agent Leon - Quick Deployment Setup"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if we're in the right directory
if [ ! -f "AgentLeon.xcodeproj/project.pbxproj" ]; then
    echo -e "${RED}❌ Error: AgentLeon.xcodeproj not found!"
    echo -e "Please run this script from the ios-app directory${NC}"
    exit 1
fi

echo -e "${BLUE}📍 Current directory: $(pwd)${NC}"
echo -e "${GREEN}✅ Found AgentLeon.xcodeproj${NC}"

# Check Xcode installation
if ! command -v xcodebuild &> /dev/null; then
    echo -e "${RED}❌ Error: Xcode command line tools not found!"
    echo -e "Please install Xcode from the App Store${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Xcode command line tools found${NC}"

# Verify project configuration
echo -e "\n${YELLOW}🔍 Verifying project configuration...${NC}"

# Check bundle identifier
BUNDLE_ID=$(grep -o 'PRODUCT_BUNDLE_IDENTIFIER = [^;]*' AgentLeon.xcodeproj/project.pbxproj | head -1 | cut -d' ' -f3 | tr -d ';')
if [ "$BUNDLE_ID" == "com.agentleon.trading" ]; then
    echo -e "${GREEN}✅ Bundle ID: $BUNDLE_ID${NC}"
else
    echo -e "${YELLOW}⚠️  Updating Bundle ID to com.agentleon.trading${NC}"
    sed -i '' 's/com.example.AgentLeon/com.agentleon.trading/g' AgentLeon.xcodeproj/project.pbxproj
fi

# Check marketing version
MARKETING_VERSION=$(grep -o 'MARKETING_VERSION = [^;]*' AgentLeon.xcodeproj/project.pbxproj | head -1 | cut -d' ' -f3 | tr -d ';')
echo -e "${GREEN}✅ Marketing Version: $MARKETING_VERSION${NC}"

# Clean previous builds
echo -e "\n${YELLOW}🧹 Cleaning previous builds...${NC}"
rm -rf ~/Library/Developer/Xcode/DerivedData/AgentLeon*
rm -rf ./AgentLeon.xcarchive
rm -rf ./build
echo -e "${GREEN}✅ Build cache cleaned${NC}"

# Test simulator build first
echo -e "\n${YELLOW}🧪 Testing simulator build...${NC}"
xcodebuild -project AgentLeon.xcodeproj \
    -scheme AgentLeon \
    -destination 'platform=iOS Simulator,name=iPhone 16,OS=latest' \
    build > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Simulator build successful${NC}"
else
    echo -e "${RED}❌ Simulator build failed!"
    echo -e "Please fix build errors before deploying${NC}"
    exit 1
fi

# Check for signing team
echo -e "\n${YELLOW}🔐 Checking code signing configuration...${NC}"

# This will require manual setup in Xcode
echo -e "${BLUE}📝 Manual steps required:${NC}"
echo -e "1. Open AgentLeon.xcodeproj in Xcode"
echo -e "2. Select AgentLeon project → AgentLeon target"
echo -e "3. Go to 'Signing & Capabilities' tab"
echo -e "4. Set Team to your Apple Developer Team"
echo -e "5. Verify Bundle Identifier: com.agentleon.trading"

# Ask if user has completed signing setup
echo -e "\n${YELLOW}❓ Have you completed the code signing setup in Xcode? (y/n)${NC}"
read -r response

if [[ "$response" != "y" && "$response" != "Y" ]]; then
    echo -e "${YELLOW}⏸️  Please complete the code signing setup first"
    echo -e "Then run this script again${NC}"
    exit 0
fi

# Create archive
echo -e "\n${YELLOW}📦 Creating archive for distribution...${NC}"
echo -e "${BLUE}This may take 5-10 minutes...${NC}"

xcodebuild -project AgentLeon.xcodeproj \
    -scheme AgentLeon \
    -destination "generic/platform=iOS" \
    -archivePath ./AgentLeon.xcarchive \
    clean archive

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Archive created successfully!${NC}"
    echo -e "${GREEN}📍 Archive location: $(pwd)/AgentLeon.xcarchive${NC}"
else
    echo -e "${RED}❌ Archive failed!"
    echo -e "Common issues:"
    echo -e "- Code signing not configured"
    echo -e "- Missing development team"
    echo -e "- Build errors in code"
    echo -e "\nPlease check Xcode for detailed error messages${NC}"
    exit 1
fi

# Open Xcode Organizer
echo -e "\n${YELLOW}📤 Opening Xcode Organizer for upload...${NC}"
open ~/Library/Developer/Xcode/Archives/

# Instructions for next steps
echo -e "\n${GREEN}🎉 Archive Ready for Distribution!${NC}"
echo -e "\n${BLUE}📋 Next steps in Xcode Organizer:${NC}"
echo -e "1. Select your AgentLeon archive"
echo -e "2. Click 'Distribute App'"
echo -e "3. Choose 'App Store Connect'"
echo -e "4. Choose 'Upload'"
echo -e "5. Select your team and certificate"
echo -e "6. Click 'Upload'"
echo -e "7. Wait for processing (10-30 minutes)"

echo -e "\n${BLUE}📱 After upload:${NC}"
echo -e "1. Go to https://appstoreconnect.apple.com/"
echo -e "2. Create new app if needed"
echo -e "3. Go to TestFlight tab"
echo -e "4. Configure beta testing"
echo -e "5. Add testers and share TestFlight link"

echo -e "\n${GREEN}🚀 You'll have a TestFlight link in 24-48 hours!${NC}"
echo -e "${GREEN}📱 Then anyone can install Agent Leon from:${NC}"
echo -e "${GREEN}https://testflight.apple.com/join/YOUR_LINK${NC}"

# Create deployment info file
cat > DEPLOYMENT_STATUS.md << EOF
# 🚀 Agent Leon Deployment Status

Generated: $(date)

## ✅ Completed Steps
- [x] Project configured for deployment
- [x] Bundle ID: com.agentleon.trading
- [x] Marketing Version: $MARKETING_VERSION
- [x] Archive created successfully
- [x] Ready for upload to App Store Connect

## 📋 Next Steps
1. Upload archive via Xcode Organizer
2. Configure App Store Connect
3. Set up TestFlight beta testing
4. Share TestFlight link with users

## 📱 Distribution URLs
- App Store Connect: https://appstoreconnect.apple.com/
- TestFlight Link: (Will be generated after upload)

## 🔧 Technical Details
- Bundle ID: com.agentleon.trading
- Version: $MARKETING_VERSION
- Archive Path: $(pwd)/AgentLeon.xcarchive
- Platform: iOS 17.5+
- Devices: iPhone, iPad

## 🤖 App Features
- Multi-exchange crypto trading (KuCoin, MEXC, Bybit)
- AI-powered trading decisions
- Real-time market analysis
- Telegram notifications
- Portfolio management
- Strategy automation
EOF

echo -e "\n${GREEN}📄 Deployment status saved to DEPLOYMENT_STATUS.md${NC}"
echo -e "${BLUE}🤖 Agent Leon is ready to revolutionize crypto trading!${NC}" 