// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		1D4E5F302C8A1B2F00A1B3C4 /* AgentLeonApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F2F2C8A1B2F00A1B3C4 /* AgentLeonApp.swift */; };
		1D4E5F322C8A1B2F00A1B3C4 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F312C8A1B2F00A1B3C4 /* ContentView.swift */; };
		1D4E5F342C8A1B3000A1B3C4 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1D4E5F332C8A1B3000A1B3C4 /* Assets.xcassets */; };
		1D4E5F372C8A1B3000A1B3C4 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1D4E5F362C8A1B3000A1B3C4 /* Preview Assets.xcassets */; };
		1D4E5F3E2C8A1B4000A1B3C4 /* Agent.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F3D2C8A1B4000A1B3C4 /* Agent.swift */; };
		1D4E5F402C8A1B5000A1B3C4 /* AgentManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F3F2C8A1B5000A1B3C4 /* AgentManager.swift */; };
		1D4E5F422C8A1B6000A1B3C4 /* AgentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F412C8A1B6000A1B3C4 /* AgentView.swift */; };
		1D4E5F442C8A1B7000A1B3C4 /* CommandInputView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F432C8A1B7000A1B3C4 /* CommandInputView.swift */; };
		1D4E5F462C8A1B8000A1B3C4 /* CryptoTradingAgent.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F452C8A1B8000A1B3C4 /* CryptoTradingAgent.swift */; };
		1D4E5F482C8A1B9000A1B3C4 /* CryptoTradingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F472C8A1B9000A1B3C4 /* CryptoTradingView.swift */; };
		1D4E5F4A2C8A1BA000A1B3C4 /* Exchange.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F492C8A1BA000A1B3C4 /* Exchange.swift */; };
		1D4E5F4C2C8A1BB000A1B3C4 /* MultiExchangeAPIService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F4B2C8A1BB000A1B3C4 /* MultiExchangeAPIService.swift */; };
		1D4E5F4E2C8A1BC000A1B3C4 /* MultiExchangeTradingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F4D2C8A1BC000A1B3C4 /* MultiExchangeTradingView.swift */; };
		1D4E5F502C8A1BD000A1B3C4 /* MultiExchangePortfolio.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F4F2C8A1BD000A1B3C4 /* MultiExchangePortfolio.swift */; };
		1D4E5F522C8A1BE000A1B3C4 /* OrderPlacementView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F512C8A1BE000A1B3C4 /* OrderPlacementView.swift */; };
		1D4E5F542C8A1BF000A1B3C4 /* ExchangeConfigurationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F532C8A1BF000A1B3C4 /* ExchangeConfigurationView.swift */; };
		1D4E5F562C8A1C0000A1B3C4 /* TradingStrategyManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F552C8A1C0000A1B3C4 /* TradingStrategyManager.swift */; };
		1D4E5F582C8A1C1000A1B3C4 /* MEXCAPIExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F572C8A1C1000A1B3C4 /* MEXCAPIExtension.swift */; };
		1D4E5F5A2C8A1C2000A1B3C4 /* BybitAPIExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F592C8A1C2000A1B3C4 /* BybitAPIExtension.swift */; };
		1D4E5F5C2C8A1C3000A1B3C4 /* KuCoinAPIExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F5B2C8A1C3000A1B3C4 /* KuCoinAPIExtension.swift */; };
		1D4E5F5E2C8A1C4000A1B3C4 /* AITradingService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F5D2C8A1C4000A1B3C4 /* AITradingService.swift */; };
		1D4E5F602C8A1C5000A1B3C4 /* MarketDataService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F5F2C8A1C5000A1B3C4 /* MarketDataService.swift */; };
		1D4E5F622C8A1C6000A1B3C4 /* APIConfigurationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F612C8A1C6000A1B3C4 /* APIConfigurationView.swift */; };
		1D4E5F642C8A1C7000A1B3C4 /* KeychainHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F632C8A1C7000A1B3C4 /* KeychainHelper.swift */; };
		1D4E5F652C8A1C8000A1B3C4 /* AIAnalysisEngine.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F642C8A1C8000A1B3C4 /* AIAnalysisEngine.swift */; };
		1D4E5F662C8A1C9000A1B3C4 /* NewsAnalysisService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F652C8A1C9000A1B3C4 /* NewsAnalysisService.swift */; };
		1D4E5F672C8A1CA000A1B3C4 /* TechnicalAnalysisService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F662C8A1CA000A1B3C4 /* TechnicalAnalysisService.swift */; };
		1D4E5F682C8A1CB000A1B3C4 /* AITradeManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F672C8A1CB000A1B3C4 /* AITradeManager.swift */; };
		1D4E5F692C8A1CC000A1B3C4 /* AITradingDashboard.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F682C8A1CC000A1B3C4 /* AITradingDashboard.swift */; };
		1D4E5F702C8A1CD000A1B3C4 /* SimpleAITradingDashboard.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4E5F6F2C8A1CD000A1B3C4 /* SimpleAITradingDashboard.swift */; };

		E1046F772E1469F30048EDBD /* BinanceAPIExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = E1046F772E1469F30048EDBA /* BinanceAPIExtension.swift */; };
		E1046F772E1469F30048EDBE /* AdvancedTradingAnalytics.swift in Sources */ = {isa = PBXBuildFile; fileRef = E1046F772E1469F30048EDBB /* AdvancedTradingAnalytics.swift */; };
		E1046F772E1469F30048EDBF /* ProfessionalTradingInterface.swift in Sources */ = {isa = PBXBuildFile; fileRef = E1046F772E1469F30048EDBC /* ProfessionalTradingInterface.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		1D4E5F2C2C8A1B2F00A1B3C4 /* AgentLeon.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = AgentLeon.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1D4E5F2F2C8A1B2F00A1B3C4 /* AgentLeonApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AgentLeonApp.swift; sourceTree = "<group>"; };
		1D4E5F312C8A1B2F00A1B3C4 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		1D4E5F332C8A1B3000A1B3C4 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		1D4E5F362C8A1B3000A1B3C4 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		1D4E5F3D2C8A1B4000A1B3C4 /* Agent.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Agent.swift; sourceTree = "<group>"; };
		1D4E5F3F2C8A1B5000A1B3C4 /* AgentManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AgentManager.swift; sourceTree = "<group>"; };
		1D4E5F412C8A1B6000A1B3C4 /* AgentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AgentView.swift; sourceTree = "<group>"; };
		1D4E5F432C8A1B7000A1B3C4 /* CommandInputView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommandInputView.swift; sourceTree = "<group>"; };
		1D4E5F452C8A1B8000A1B3C4 /* CryptoTradingAgent.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CryptoTradingAgent.swift; sourceTree = "<group>"; };
		1D4E5F472C8A1B9000A1B3C4 /* CryptoTradingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CryptoTradingView.swift; sourceTree = "<group>"; };
		1D4E5F492C8A1BA000A1B3C4 /* Exchange.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Exchange.swift; sourceTree = "<group>"; };
		1D4E5F4B2C8A1BB000A1B3C4 /* MultiExchangeAPIService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MultiExchangeAPIService.swift; sourceTree = "<group>"; };
		1D4E5F4D2C8A1BC000A1B3C4 /* MultiExchangeTradingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MultiExchangeTradingView.swift; sourceTree = "<group>"; };
		1D4E5F4F2C8A1BD000A1B3C4 /* MultiExchangePortfolio.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MultiExchangePortfolio.swift; sourceTree = "<group>"; };
		1D4E5F512C8A1BE000A1B3C4 /* OrderPlacementView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrderPlacementView.swift; sourceTree = "<group>"; };
		1D4E5F532C8A1BF000A1B3C4 /* ExchangeConfigurationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExchangeConfigurationView.swift; sourceTree = "<group>"; };
		1D4E5F552C8A1C0000A1B3C4 /* TradingStrategyManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TradingStrategyManager.swift; sourceTree = "<group>"; };
		1D4E5F572C8A1C1000A1B3C4 /* MEXCAPIExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MEXCAPIExtension.swift; sourceTree = "<group>"; };
		1D4E5F592C8A1C2000A1B3C4 /* BybitAPIExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BybitAPIExtension.swift; sourceTree = "<group>"; };
		1D4E5F5B2C8A1C3000A1B3C4 /* KuCoinAPIExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = KuCoinAPIExtension.swift; sourceTree = "<group>"; };
		1D4E5F5D2C8A1C4000A1B3C4 /* AITradingService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AITradingService.swift; sourceTree = "<group>"; };
		1D4E5F5F2C8A1C5000A1B3C4 /* MarketDataService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MarketDataService.swift; sourceTree = "<group>"; };
		1D4E5F612C8A1C6000A1B3C4 /* APIConfigurationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIConfigurationView.swift; sourceTree = "<group>"; };
		1D4E5F632C8A1C7000A1B3C4 /* KeychainHelper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = KeychainHelper.swift; sourceTree = "<group>"; };
		1D4E5F642C8A1C8000A1B3C4 /* AIAnalysisEngine.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIAnalysisEngine.swift; sourceTree = "<group>"; };
		1D4E5F652C8A1C9000A1B3C4 /* NewsAnalysisService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NewsAnalysisService.swift; sourceTree = "<group>"; };
		1D4E5F662C8A1CA000A1B3C4 /* TechnicalAnalysisService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TechnicalAnalysisService.swift; sourceTree = "<group>"; };
		1D4E5F672C8A1CB000A1B3C4 /* AITradeManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AITradeManager.swift; sourceTree = "<group>"; };
		1D4E5F682C8A1CC000A1B3C4 /* AITradingDashboard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AITradingDashboard.swift; sourceTree = "<group>"; };
		1D4E5F6F2C8A1CD000A1B3C4 /* SimpleAITradingDashboard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SimpleAITradingDashboard.swift; sourceTree = "<group>"; };

		E1046F772E1469F30048EDBA /* BinanceAPIExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BinanceAPIExtension.swift; sourceTree = "<group>"; };
		E1046F772E1469F30048EDBB /* AdvancedTradingAnalytics.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AdvancedTradingAnalytics.swift; sourceTree = "<group>"; };
		E1046F772E1469F30048EDBC /* ProfessionalTradingInterface.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfessionalTradingInterface.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1D4E5F292C8A1B2F00A1B3C4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1D4E5F232C8A1B2F00A1B3C4 = {
			isa = PBXGroup;
			children = (
				1D4E5F2E2C8A1B2F00A1B3C4 /* AgentLeon */,
				1D4E5F2D2C8A1B2F00A1B3C4 /* Products */,
			);
			sourceTree = "<group>";
		};
		1D4E5F2D2C8A1B2F00A1B3C4 /* Products */ = {
			isa = PBXGroup;
			children = (
				1D4E5F2C2C8A1B2F00A1B3C4 /* AgentLeon.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		1D4E5F2E2C8A1B2F00A1B3C4 /* AgentLeon */ = {
			isa = PBXGroup;
			children = (

				1D4E5F2F2C8A1B2F00A1B3C4 /* AgentLeonApp.swift */,
				1D4E5F312C8A1B2F00A1B3C4 /* ContentView.swift */,
				1D4E5F3D2C8A1B4000A1B3C4 /* Agent.swift */,
				1D4E5F3F2C8A1B5000A1B3C4 /* AgentManager.swift */,
				1D4E5F412C8A1B6000A1B3C4 /* AgentView.swift */,
				1D4E5F432C8A1B7000A1B3C4 /* CommandInputView.swift */,
				1D4E5F452C8A1B8000A1B3C4 /* CryptoTradingAgent.swift */,
				1D4E5F472C8A1B9000A1B3C4 /* CryptoTradingView.swift */,
				1D4E5F492C8A1BA000A1B3C4 /* Exchange.swift */,
				1D4E5F4B2C8A1BB000A1B3C4 /* MultiExchangeAPIService.swift */,
				1D4E5F4D2C8A1BC000A1B3C4 /* MultiExchangeTradingView.swift */,
				1D4E5F4F2C8A1BD000A1B3C4 /* MultiExchangePortfolio.swift */,
				1D4E5F512C8A1BE000A1B3C4 /* OrderPlacementView.swift */,
				1D4E5F532C8A1BF000A1B3C4 /* ExchangeConfigurationView.swift */,
				1D4E5F552C8A1C0000A1B3C4 /* TradingStrategyManager.swift */,
				1D4E5F572C8A1C1000A1B3C4 /* MEXCAPIExtension.swift */,
				1D4E5F592C8A1C2000A1B3C4 /* BybitAPIExtension.swift */,
				1D4E5F5B2C8A1C3000A1B3C4 /* KuCoinAPIExtension.swift */,
				1D4E5F5D2C8A1C4000A1B3C4 /* AITradingService.swift */,
				1D4E5F5F2C8A1C5000A1B3C4 /* MarketDataService.swift */,
				1D4E5F612C8A1C6000A1B3C4 /* APIConfigurationView.swift */,
				1D4E5F632C8A1C7000A1B3C4 /* KeychainHelper.swift */,
				1D4E5F642C8A1C8000A1B3C4 /* AIAnalysisEngine.swift */,
				1D4E5F652C8A1C9000A1B3C4 /* NewsAnalysisService.swift */,
				1D4E5F662C8A1CA000A1B3C4 /* TechnicalAnalysisService.swift */,
				1D4E5F672C8A1CB000A1B3C4 /* AITradeManager.swift */,
				1D4E5F682C8A1CC000A1B3C4 /* AITradingDashboard.swift */,
				1D4E5F6F2C8A1CD000A1B3C4 /* SimpleAITradingDashboard.swift */,
				1D4E5F332C8A1B3000A1B3C4 /* Assets.xcassets */,
				1D4E5F352C8A1B3000A1B3C4 /* Preview Content */,				E1046F772E1469F30048EDBA /* BinanceAPIExtension.swift */,
				E1046F772E1469F30048EDBB /* AdvancedTradingAnalytics.swift */,
				E1046F772E1469F30048EDBC /* ProfessionalTradingInterface.swift */,

			);
			path = AgentLeon;
			sourceTree = "<group>";
		};
		1D4E5F352C8A1B3000A1B3C4 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				1D4E5F362C8A1B3000A1B3C4 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1D4E5F2B2C8A1B2F00A1B3C4 /* AgentLeon */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1D4E5F3A2C8A1B3000A1B3C4 /* Build configuration list for PBXNativeTarget "AgentLeon" */;
			buildPhases = (
				1D4E5F282C8A1B2F00A1B3C4 /* Sources */,
				1D4E5F292C8A1B2F00A1B3C4 /* Frameworks */,
				1D4E5F2A2C8A1B2F00A1B3C4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = AgentLeon;
			productName = AgentLeon;
			productReference = 1D4E5F2C2C8A1B2F00A1B3C4 /* AgentLeon.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1D4E5F242C8A1B2F00A1B3C4 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1540;
				LastUpgradeCheck = 1540;
				TargetAttributes = {
					1D4E5F2B2C8A1B2F00A1B3C4 = {
						CreatedOnToolsVersion = 15.4;
					};
				};
			};
			buildConfigurationList = 1D4E5F272C8A1B2F00A1B3C4 /* Build configuration list for PBXProject "AgentLeon" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 1D4E5F232C8A1B2F00A1B3C4;
			productRefGroup = 1D4E5F2D2C8A1B2F00A1B3C4 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1D4E5F2B2C8A1B2F00A1B3C4 /* AgentLeon */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1D4E5F2A2C8A1B2F00A1B3C4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1D4E5F372C8A1B3000A1B3C4 /* Preview Assets.xcassets in Resources */,
				1D4E5F342C8A1B3000A1B3C4 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1D4E5F282C8A1B2F00A1B3C4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (

				1D4E5F3E2C8A1B4000A1B3C4 /* Agent.swift in Sources */,
				1D4E5F402C8A1B5000A1B3C4 /* AgentManager.swift in Sources */,
				1D4E5F322C8A1B2F00A1B3C4 /* ContentView.swift in Sources */,
				1D4E5F422C8A1B6000A1B3C4 /* AgentView.swift in Sources */,
				1D4E5F442C8A1B7000A1B3C4 /* CommandInputView.swift in Sources */,
				1D4E5F462C8A1B8000A1B3C4 /* CryptoTradingAgent.swift in Sources */,
				1D4E5F482C8A1B9000A1B3C4 /* CryptoTradingView.swift in Sources */,
				1D4E5F4A2C8A1BA000A1B3C4 /* Exchange.swift in Sources */,
				1D4E5F4C2C8A1BB000A1B3C4 /* MultiExchangeAPIService.swift in Sources */,
				1D4E5F4E2C8A1BC000A1B3C4 /* MultiExchangeTradingView.swift in Sources */,
				1D4E5F502C8A1BD000A1B3C4 /* MultiExchangePortfolio.swift in Sources */,
				1D4E5F522C8A1BE000A1B3C4 /* OrderPlacementView.swift in Sources */,
				1D4E5F542C8A1BF000A1B3C4 /* ExchangeConfigurationView.swift in Sources */,
				1D4E5F562C8A1C0000A1B3C4 /* TradingStrategyManager.swift in Sources */,
				1D4E5F582C8A1C1000A1B3C4 /* MEXCAPIExtension.swift in Sources */,
				1D4E5F5A2C8A1C2000A1B3C4 /* BybitAPIExtension.swift in Sources */,
				1D4E5F5C2C8A1C3000A1B3C4 /* KuCoinAPIExtension.swift in Sources */,
				1D4E5F5E2C8A1C4000A1B3C4 /* AITradingService.swift in Sources */,
				1D4E5F602C8A1C5000A1B3C4 /* MarketDataService.swift in Sources */,
				1D4E5F622C8A1C6000A1B3C4 /* APIConfigurationView.swift in Sources */,
				1D4E5F642C8A1C7000A1B3C4 /* KeychainHelper.swift in Sources */,
				1D4E5F652C8A1C8000A1B3C4 /* AIAnalysisEngine.swift in Sources */,
				1D4E5F662C8A1C9000A1B3C4 /* NewsAnalysisService.swift in Sources */,
				1D4E5F672C8A1CA000A1B3C4 /* TechnicalAnalysisService.swift in Sources */,
				1D4E5F682C8A1CB000A1B3C4 /* AITradeManager.swift in Sources */,
				1D4E5F692C8A1CC000A1B3C4 /* AITradingDashboard.swift in Sources */,
				1D4E5F702C8A1CD000A1B3C4 /* SimpleAITradingDashboard.swift in Sources */,
				1D4E5F302C8A1B2F00A1B3C4 /* AgentLeonApp.swift in Sources */,				E1046F772E1469F30048EDBD /* BinanceAPIExtension.swift in Sources */,
				E1046F772E1469F30048EDBE /* AdvancedTradingAnalytics.swift in Sources */,
				E1046F772E1469F30048EDBF /* ProfessionalTradingInterface.swift in Sources */,

			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		1D4E5F382C8A1B3000A1B3C4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALLOW_TARGET_PLATFORM_SPECIALIZATION = YES;
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		1D4E5F392C8A1B3000A1B3C4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALLOW_TARGET_PLATFORM_SPECIALIZATION = YES;
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				"SDKROOT[arch=*]" = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		1D4E5F3B2C8A1B3000A1B3C4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"AgentLeon/Preview Content\"";
				DEVELOPMENT_TEAM = SY49KDST85;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.developer-tools";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIRequiresFullScreen = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.roustam.agentleon;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1D4E5F3C2C8A1B3000A1B3C4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"AgentLeon/Preview Content\"";
				DEVELOPMENT_TEAM = SY49KDST85;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.developer-tools";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIRequiresFullScreen = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.roustam.agentleon;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1D4E5F272C8A1B2F00A1B3C4 /* Build configuration list for PBXProject "AgentLeon" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1D4E5F382C8A1B3000A1B3C4 /* Debug */,
				1D4E5F392C8A1B3000A1B3C4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1D4E5F3A2C8A1B3000A1B3C4 /* Build configuration list for PBXNativeTarget "AgentLeon" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1D4E5F3B2C8A1B3000A1B3C4 /* Debug */,
				1D4E5F3C2C8A1B3000A1B3C4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 1D4E5F242C8A1B2F00A1B3C4 /* Project object */;
}
