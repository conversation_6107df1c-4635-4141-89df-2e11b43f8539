import Foundation
import Combine

// MARK: - Real Trading Service
class RealTradingService: ObservableObject {
    @Published var isLiveTrading = false
    @Published var testnetMode = true // Start veilig met testnet
    @Published var tradingBalance: Double = 0.0
    @Published var realPositions: [RealPosition] = []
    @Published var executedTrades: [ExecutedTrade] = []
    @Published var lastTradingError: String?
    
    private var apiService = MultiExchangeAPIService()
    private var agentManager: AgentManager
    private var cancellables = Set<AnyCancellable>()
    
    // Trading Configuration
    @Published var allowedExchanges: [SupportedExchange] = [.kucoin, .mexc]
    @Published var maxTradeAmount: Double = 100.0 // Start klein
    @Published var stopLossPercentage: Double = 5.0 // 5% stop loss
    @Published var takeProfitPercentage: Double = 10.0 // 10% take profit
    
    init(agentManager: AgentManager) {
        self.agentManager = agentManager
        setupAPIKeys()
        setupTradingMonitoring()
    }
    
    // MARK: - Setup
    private func setupAPIKeys() {
        // Configure exchanges with API keys
        let kucoinConfig = ExchangeConfiguration(
            exchange: .kucoin,
            apiKey: "681275984985e300012f3789",
            apiSecret: "c4fab7b9-3120-4ede-a7c9-5f3645b6acec",
            passphrase: "Kucoinn",
            testnet: testnetMode,
            isEnabled: true
        )
        
        let mexcConfig = ExchangeConfiguration(
            exchange: .mexc,
            apiKey: "mx0vglmGyrf9LW7tY9",
            apiSecret: "b5bb5493128b483c9ebe199d013042c1",
            passphrase: nil,
            testnet: testnetMode,
            isEnabled: true
        )
        
        apiService.updateExchangeConfiguration(kucoinConfig)
        apiService.updateExchangeConfiguration(mexcConfig)
    }
    
    private func setupTradingMonitoring() {
        // Monitor crypto agent signals
        agentManager.cryptoAgent?.objectWillChange
            .sink { [weak self] in
                Task {
                    await self?.processAgentSignals()
                }
            }
            .store(in: &cancellables)
        
        // Regular balance updates
        Timer.scheduledTimer(withTimeInterval: 60.0, repeats: true) { [weak self] _ in
            Task {
                await self?.updateAccountBalances()
            }
        }
    }
    
    // MARK: - Account Management
    func updateAccountBalances() async {
        var totalBalance: Double = 0
        
        for exchange in allowedExchanges {
            let balances = await apiService.fetchAccountBalance(for: exchange)
            let usdtBalance = balances["USDT"] ?? 0.0
            totalBalance += usdtBalance
            
            print("💰 \(exchange.rawValue) Balance: \(usdtBalance) USDT")
        }
        
        await MainActor.run {
            self.tradingBalance = totalBalance
        }
    }
    
    func testExchangeConnections() async {
        print("🔍 Testing exchange connections...")
        
        for exchange in allowedExchanges {
            let isConnected = await apiService.testConnection(for: exchange)
            
            await MainActor.run {
                if isConnected {
                    print("✅ \(exchange.rawValue) connection successful")
                } else {
                    print("❌ \(exchange.rawValue) connection failed")
                    self.lastTradingError = "\(exchange.rawValue) connection failed"
                }
            }
        }
    }
    
    // MARK: - Agent Signal Processing
    private func processAgentSignals() async {
        guard isLiveTrading else { return }
        
        guard let cryptoAgent = agentManager.cryptoAgent else { return }
        
        // Process recent trading signals
        for signal in cryptoAgent.tradingSignals.suffix(3) {
            // Only process signals from last 5 minutes
            if signal.timestamp.timeIntervalSinceNow > -300 {
                await processSignal(signal)
            }
        }
    }
    
    private func processSignal(_ signal: TradingSignal) async {
        print("🤖 Processing agent signal: \(signal.type.rawValue) \(signal.symbol)")
        
        // Check if we should execute this signal
        guard shouldExecuteSignal(signal) else { return }
        
        // Find best exchange for this trade
        let bestExchange = await findBestExchange(for: signal.symbol)
        
        // Calculate trade amount
        let tradeAmount = min(signal.recommendedAmount * signal.price, maxTradeAmount)
        
        do {
            let order = try await apiService.placeOrder(
                exchange: bestExchange,
                symbol: signal.symbol,
                side: signal.type == .buy ? .buy : .sell,
                type: .market,
                amount: signal.recommendedAmount,
                price: nil, // Market order
                tradingMode: .spot,
                stopPrice: nil,
                leverage: nil
            )
            
            await recordExecutedTrade(from: signal, order: order)
            print("✅ Trade executed: \(order.exchangeOrderId)")
            
        } catch {
            await MainActor.run {
                self.lastTradingError = "Trade execution failed: \(error.localizedDescription)"
            }
            print("❌ Trade failed: \(error)")
        }
    }
    
    // MARK: - Trade Validation
    private func shouldExecuteSignal(_ signal: TradingSignal) -> Bool {
        // Safety checks
        guard signal.confidence > 60.0 else {
            print("⚠️ Signal confidence too low: \(signal.confidence)%")
            return false
        }
        
        guard signal.recommendedAmount * signal.price <= maxTradeAmount else {
            print("⚠️ Trade amount too large: \(signal.recommendedAmount * signal.price)")
            return false
        }
        
        // Check if we already have a position
        if realPositions.contains(where: { $0.symbol == signal.symbol }) {
            print("⚠️ Already have position in \(signal.symbol)")
            return false
        }
        
        return true
    }
    
    private func findBestExchange(for symbol: String) async -> SupportedExchange {
        // For now, prioritize KuCoin
        if allowedExchanges.contains(.kucoin) {
            return .kucoin
        } else if allowedExchanges.contains(.mexc) {
            return .mexc
        } else {
            return allowedExchanges.first ?? .kucoin
        }
    }
    
    // MARK: - Trade Recording
    private func recordExecutedTrade(from signal: TradingSignal, order: RealOrder) async {
        let executedTrade = ExecutedTrade(
            id: UUID(),
            exchangeOrderId: order.exchangeOrderId,
            symbol: signal.symbol,
            exchange: order.exchange,
            side: order.side,
            amount: order.amount,
            price: order.price ?? signal.price,
            timestamp: Date(),
            pnl: nil,
            fees: order.fees,
            agentReasoning: signal.reason,
            confidence: signal.confidence
        )
        
        await MainActor.run {
            self.executedTrades.append(executedTrade)
        }
    }
    
    // MARK: - Manual Trading Functions
    func executeBuyOrder(symbol: String, amount: Double, exchange: SupportedExchange) async {
        do {
            let order = try await apiService.placeOrder(
                exchange: exchange,
                symbol: symbol,
                side: .buy,
                type: .market,
                amount: amount,
                price: nil,
                tradingMode: .spot,
                stopPrice: nil,
                leverage: nil
            )
            
            print("✅ Manual buy order executed: \(order.exchangeOrderId)")
            
        } catch {
            await MainActor.run {
                self.lastTradingError = "Manual buy failed: \(error.localizedDescription)"
            }
        }
    }
    
    func executeSellOrder(symbol: String, amount: Double, exchange: SupportedExchange) async {
        do {
            let order = try await apiService.placeOrder(
                exchange: exchange,
                symbol: symbol,
                side: .sell,
                type: .market,
                amount: amount,
                price: nil,
                tradingMode: .spot,
                stopPrice: nil,
                leverage: nil
            )
            
            print("✅ Manual sell order executed: \(order.exchangeOrderId)")
            
        } catch {
            await MainActor.run {
                self.lastTradingError = "Manual sell failed: \(error.localizedDescription)"
            }
        }
    }
    
    // MARK: - Safety Controls
    func enableLiveTrading() async {
        // Final safety checks
        await testExchangeConnections()
        await updateAccountBalances()
        
        guard tradingBalance > 0 else {
            await MainActor.run {
                self.lastTradingError = "No trading balance available"
            }
            return
        }
        
        await MainActor.run {
            self.isLiveTrading = true
        }
        
        print("🚀 Live trading ENABLED - Balance: \(tradingBalance) USDT")
    }
    
    func disableLiveTrading() {
        isLiveTrading = false
        print("⏹️ Live trading DISABLED")
    }
    
    func switchToTestnet() {
        testnetMode = true
        setupAPIKeys() // Reload with testnet URLs
        print("🧪 Switched to TESTNET mode")
    }
    
    func switchToMainnet() {
        testnetMode = false
        setupAPIKeys() // Reload with mainnet URLs
        print("⚡ Switched to MAINNET mode")
    }
}

// MARK: - Supporting Types
struct ExecutedTrade: Identifiable, Codable {
    let id: UUID
    let exchangeOrderId: String
    let symbol: String
    let exchange: SupportedExchange
    let side: OrderSide
    let amount: Double
    let price: Double
    let timestamp: Date
    var pnl: Double?
    let fees: Double
    let agentReasoning: String
    let confidence: Double
} 