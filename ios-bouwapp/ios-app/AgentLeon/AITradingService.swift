import Foundation

// MARK: - AI Trading Service
class AITradingService: ObservableObject {
    @Published var isAIEnabled = false
    @Published var selectedModel = "anthropic/claude-3.5-sonnet"
    @Published var aiApiKey = ""
    @Published var lastAIDecision: AITradingDecision?
    @Published var aiPerformance: AIPerformanceMetrics = AIPerformanceMetrics()
    
    private let openRouterBaseURL = "https://openrouter.ai/api/v1/chat/completions"
    private let urlSession = URLSession.shared
    
    // MARK: - Available AI Models
    let availableModels = [
        AIModel(id: "anthropic/claude-3.5-sonnet", name: "Claude 3.5 Sonnet", provider: "Anthropic"),
        AIModel(id: "openai/gpt-4-turbo", name: "GPT-4 Turbo", provider: "OpenAI"),
        AIModel(id: "google/gemini-pro", name: "Gemini Pro", provider: "Google"),
        AIModel(id: "meta-llama/llama-3.1-70b-instruct", name: "<PERSON>lama 3.1 70B", provider: "Meta"),
        AIModel(id: "mistralai/mixtral-8x7b-instruct", name: "Mixtral 8x7B", provider: "Mistral"),
        AIModel(id: "anthropic/claude-3-haiku", name: "Claude 3 Haiku", provider: "Anthropic")
    ]
    
    // MARK: - AI Trading Analysis
    func analyzeMarketWithAI(
        marketData: MarketAnalysisData,
        strategy: AdvancedTradingStrategy,
        riskTolerance: RiskLevel
    ) async throws -> AITradingDecision {
        
        guard !aiApiKey.isEmpty else {
            throw AITradingError.missingAPIKey
        }
        
        let prompt = buildTradingPrompt(
            marketData: marketData,
            strategy: strategy,
            riskTolerance: riskTolerance
        )
        
        let decision = try await sendAIRequest(prompt: prompt)
        
        await MainActor.run {
            lastAIDecision = decision
            updateAIPerformance(decision: decision)
        }
        
        return decision
    }
    
    // MARK: - OpenRouter API Integration
    private func sendAIRequest(prompt: String) async throws -> AITradingDecision {
        guard let url = URL(string: openRouterBaseURL) else {
            throw AITradingError.invalidURL
        }
        
        let requestBody: [String: Any] = [
            "model": selectedModel,
            "messages": [
                [
                    "role": "system",
                    "content": """
                    You are an expert cryptocurrency trading AI assistant. Analyze market data and provide trading recommendations.
                    
                    CRITICAL: Always respond in valid JSON format with this exact structure:
                    {
                        "action": "BUY" | "SELL" | "HOLD",
                        "confidence": 0.0-1.0,
                        "reasoning": "explanation",
                        "suggested_amount": number,
                        "stop_loss": number,
                        "take_profit": number,
                        "risk_assessment": "LOW" | "MEDIUM" | "HIGH" | "VERY_HIGH",
                        "time_horizon": "SHORT" | "MEDIUM" | "LONG"
                    }
                    """
                ],
                [
                    "role": "user", 
                    "content": prompt
                ]
            ],
            "temperature": 0.1,
            "max_tokens": 1000
        ]
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(aiApiKey)", forHTTPHeaderField: "Authorization")
        request.setValue("Agent-Leon-Trading-Bot/1.0", forHTTPHeaderField: "HTTP-Referer")
        request.setValue("Agent-Leon-Trading-Bot/1.0", forHTTPHeaderField: "X-Title")
        
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (data, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw AITradingError.networkError
        }
        
        if httpResponse.statusCode != 200 {
            let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any]
            let errorMessage = errorData?["error"] as? String ?? "Unknown error"
            throw AITradingError.apiError(errorMessage)
        }
        
        return try parseAIResponse(data)
    }
    
    // MARK: - Prompt Building
    private func buildTradingPrompt(
        marketData: MarketAnalysisData,
        strategy: AdvancedTradingStrategy,
        riskTolerance: RiskLevel
    ) -> String {
        return """
        CRYPTO TRADING ANALYSIS REQUEST
        
        MARKET DATA:
        Symbol: \(marketData.symbol)
        Current Price: $\(marketData.currentPrice)
        24h Change: \(marketData.priceChange24h)%
        Volume 24h: $\(marketData.volume24h)
        Market Cap: $\(marketData.marketCap ?? 0)
        RSI: \(marketData.rsi ?? 0)
        MACD: \(marketData.macd ?? 0)
        Moving Averages: SMA20: \(marketData.sma20 ?? 0), SMA50: \(marketData.sma50 ?? 0)
        
        TRADING STRATEGY: \(strategy.displayName)
        RISK TOLERANCE: \(riskTolerance.displayName)
        
        TECHNICAL INDICATORS:
        \(marketData.technicalIndicators.map { indicator in "\(indicator.key): \(indicator.value)" }.joined(separator: ", "))
        
        NEWS SENTIMENT: \(marketData.newsSentiment ?? "Neutral")
        FEAR & GREED INDEX: \(marketData.fearGreedIndex ?? 50)
        
        Please provide a trading recommendation based on this data.
        Consider the strategy type and risk tolerance in your analysis.
        """
    }
    
    // MARK: - Response Parsing
    private func parseAIResponse(_ data: Data) throws -> AITradingDecision {
        let decoder = JSONDecoder()
        
        do {
            let openRouterResponse = try decoder.decode(OpenRouterResponse.self, from: data)
            guard let content = openRouterResponse.choices.first?.message.content else {
                throw AITradingError.invalidResponse
            }
            
            // Parse JSON content from AI response
            guard let jsonData = content.data(using: .utf8) else {
                throw AITradingError.invalidResponse
            }
            
            let aiDecision = try decoder.decode(AITradingDecision.self, from: jsonData)
            return aiDecision
            
        } catch {
            print("AI Response parsing error: \(error)")
            throw AITradingError.responseParsingFailed
        }
    }
    
    // MARK: - Performance Tracking
    private func updateAIPerformance(decision: AITradingDecision) {
        aiPerformance.totalDecisions += 1
        aiPerformance.lastDecisionTime = Date()
        
        // Track confidence levels
        if decision.confidence > 0.8 {
            aiPerformance.highConfidenceDecisions += 1
        }
        
        // Update strategy usage
        aiPerformance.avgConfidence = ((aiPerformance.avgConfidence * Double(aiPerformance.totalDecisions - 1)) + decision.confidence) / Double(aiPerformance.totalDecisions)
    }
}

// MARK: - Supporting Models
struct AIModel {
    let id: String
    let name: String
    let provider: String
}

struct AITradingDecision: Codable {
    let action: TradingAction
    let confidence: Double
    let reasoning: String
    let suggested_amount: Double
    let stop_loss: Double?
    let take_profit: Double?
    let risk_assessment: String
    let time_horizon: String
    
    enum TradingAction: String, Codable {
        case BUY, SELL, HOLD
        
        var orderSide: OrderSide? {
            switch self {
            case .BUY: return .buy
            case .SELL: return .sell
            case .HOLD: return nil
            }
        }
    }
}

struct MarketAnalysisData {
    let symbol: String
    let currentPrice: Double
    let priceChange24h: Double
    let volume24h: Double
    let marketCap: Double?
    let rsi: Double?
    let macd: Double?
    let sma20: Double?
    let sma50: Double?
    let technicalIndicators: [String: Double]
    let newsSentiment: String?
    let fearGreedIndex: Int?
}

struct AIPerformanceMetrics {
    var totalDecisions: Int = 0
    var successfulTrades: Int = 0
    var failedTrades: Int = 0
    var avgConfidence: Double = 0.0
    var highConfidenceDecisions: Int = 0
    var totalProfit: Double = 0.0
    var lastDecisionTime: Date?
    
    var successRate: Double {
        let totalTrades = successfulTrades + failedTrades
        return totalTrades > 0 ? Double(successfulTrades) / Double(totalTrades) : 0.0
    }
}

// MARK: - OpenRouter Response Models
struct OpenRouterResponse: Codable {
    let choices: [Choice]
    
    struct Choice: Codable {
        let message: Message
        
        struct Message: Codable {
            let content: String
        }
    }
}

// MARK: - Errors
enum AITradingError: Error, LocalizedError {
    case missingAPIKey
    case invalidURL
    case networkError
    case apiError(String)
    case invalidResponse
    case responseParsingFailed
    
    var errorDescription: String? {
        switch self {
        case .missingAPIKey:
            return "AI API key is missing"
        case .invalidURL:
            return "Invalid API URL"
        case .networkError:
            return "Network error occurred"
        case .apiError(let message):
            return "API Error: \(message)"
        case .invalidResponse:
            return "Invalid AI response format"
        case .responseParsingFailed:
            return "Failed to parse AI response"
        }
    }
} 