import Foundation

// MARK: - MEXC API Implementation
extension MultiExchangeAPIService {
    
    // MARK: - Market Data
    func fetchMEXCMarketData(symbol: String) async throws -> MarketData {
        let baseURL = "https://api.mexc.com"
        let endpoint = "/api/v3/ticker/24hr?symbol=\(symbol)"
        let url = URL(string: "\(baseURL)\(endpoint)")!
        
        let (data, response) = try await urlSession.data(from: url)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.invalidResponse
        }
        
        let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] ?? [:]
        
        // Convert MEXC response to MarketData with candlestick data
        let price = Double(json["lastPrice"] as? String ?? "0") ?? 0.0
        let volume = Double(json["volume"] as? String ?? "0") ?? 0.0
        let high = Double(json["highPrice"] as? String ?? "0") ?? 0.0
        let low = Double(json["lowPrice"] as? String ?? "0") ?? 0.0
        let open = Double(json["openPrice"] as? String ?? "0") ?? 0.0
        
        // Create a single candlestick from the 24hr ticker data
        let candlestick = Candlestick(
            open: open,
            high: high,
            low: low,
            close: price,
            volume: volume,
            timestamp: Date()
        )
        
        return MarketData(
            symbol: symbol,
            candlesticks: [candlestick],
            timestamp: Date()
        )
    }
    
    // MARK: - Connection Testing
    func testMEXCConnection(config: ExchangeConfiguration) async throws -> Bool {
        let baseURL = "https://api.mexc.com"
        guard let url = URL(string: "\(baseURL)/api/v3/account") else {
            throw APIError.invalidURL
        }
        
        let request = try createAuthenticatedRequest(url: url, method: "GET", body: nil as Data?, config: config)
        let (data, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.networkError(NSError(domain: "Invalid response", code: -1))
        }
        
        if httpResponse.statusCode == 200 {
            await MainActor.run {
                isConnected[.mexc] = true
            }
            return true
        } else {
            throw APIError.authenticationFailed
        }
    }
    
    // MARK: - Trading Pairs
    func fetchMEXCTradingPairs(config: ExchangeConfiguration, mode: TradingMode) async throws -> [TradingPair] {
        // MEXC primarily supports spot trading
        guard mode == .spot else { return [] }
        
        guard let url = URL(string: "https://api.mexc.com/api/v3/exchangeInfo") else {
            throw APIError.invalidURL
        }
        
        let request = URLRequest(url: url)
        let (data, _) = try await urlSession.data(for: request)
        
        let decoder = JSONDecoder()
        let response = try decoder.decode(MEXCExchangeInfoResponse.self, from: data)
        
        return response.symbols.compactMap { symbolData in
            createTradingPairFromMEXC(symbolData, config: config, mode: mode)
        }
    }
    
    // MARK: - Account Balance
    func fetchMEXCBalance(config: ExchangeConfiguration) async throws -> [String: Double] {
        guard let url = URL(string: "https://api.mexc.com/api/v3/account") else {
            throw APIError.invalidURL
        }
        
        let request = try createAuthenticatedRequest(url: url, method: "GET", body: nil as Data?, config: config)
        let (data, _) = try await urlSession.data(for: request)
        
        let decoder = JSONDecoder()
        let response = try decoder.decode(MEXCAccountResponse.self, from: data)
        
        var balances: [String: Double] = [:]
        response.balances.forEach { balance in
            if let freeBalance = Double(balance.free), let lockedBalance = Double(balance.locked) {
                let totalBalance = freeBalance + lockedBalance
                if totalBalance > 0 {
                    balances[balance.asset] = totalBalance
                }
            }
        }
        
        return balances
    }
    
    // MARK: - Order Placement
    func placeMEXCOrder(
        config: ExchangeConfiguration,
        symbol: String,
        side: OrderSide,
        type: OrderType,
        amount: Double,
        price: Double?,
        tradingMode: TradingMode,
        stopPrice: Double?,
        leverage: Double?
    ) async throws -> RealOrder {
        // MEXC primarily supports spot trading
        guard tradingMode == .spot else {
            throw APIError.apiError("MEXC only supports spot trading in this implementation")
        }
        
        guard let url = URL(string: "https://api.mexc.com/api/v3/order") else {
            throw APIError.invalidURL
        }
        
        var orderParams: [String: String] = [
            "symbol": symbol,
            "side": side.rawValue.uppercased(),
            "type": mexcOrderType(from: type),
            "quantity": String(amount),
            "timestamp": String(Int64(Date().timeIntervalSince1970 * 1000))
        ]
        
        if let price = price, type != .market {
            orderParams["price"] = String(price)
            orderParams["timeInForce"] = "GTC"
        }
        
        if let stopPrice = stopPrice {
            orderParams["stopPrice"] = String(stopPrice)
        }
        
        // Create query string for MEXC authentication
        let queryString = orderParams.sorted { $0.key < $1.key }
            .map { "\($0.key)=\($0.value)" }
            .joined(separator: "&")
        
        let signature = try hmacSHA256(key: config.apiSecret, message: queryString)
        orderParams["signature"] = signature
        
        let finalQueryString = orderParams.map { "\($0.key)=\($0.value)" }.joined(separator: "&")
        guard let finalURL = URL(string: "https://api.mexc.com/api/v3/order?\(finalQueryString)") else {
            throw APIError.invalidURL
        }
        
        var request = URLRequest(url: finalURL)
        request.httpMethod = "POST"
        request.setValue(config.apiKey, forHTTPHeaderField: "X-MEXC-APIKEY")
        request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        
        let (data, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.networkError(NSError(domain: "Invalid response", code: -1))
        }
        
        if httpResponse.statusCode != 200 {
            let errorResponse = try? JSONSerialization.jsonObject(with: data) as? [String: Any]
            let errorMessage: String = errorResponse?["msg"] as? String ?? "Unknown error"
            throw APIError.apiError(errorMessage)
        }
        
        let decoder = JSONDecoder()
        let orderResponse = try decoder.decode(MEXCOrderResponse.self, from: data)
        
        return RealOrder(
            exchange: .mexc,
            exchangeOrderId: String(orderResponse.orderId),
            clientOrderId: orderResponse.clientOrderId ?? "",
            symbol: symbol,
            side: side,
            type: type,
            amount: amount,
            price: price,
            stopPrice: stopPrice,
            tradingMode: tradingMode,
            status: .pending,
            filledAmount: 0,
            averagePrice: nil,
            fees: 0,
            feeCurrency: "USDT",
            createdAt: Date(),
            updatedAt: Date(),
            strategy: nil
        )
    }
    
    // MARK: - Order Cancellation
    func cancelMEXCOrder(config: ExchangeConfiguration, orderId: String, symbol: String) async throws -> Bool {
        var cancelParams: [String: String] = [
            "symbol": symbol,
            "orderId": orderId,
            "timestamp": String(Int64(Date().timeIntervalSince1970 * 1000))
        ]
        
        let queryString = cancelParams.sorted { $0.key < $1.key }
            .map { "\($0.key)=\($0.value)" }
            .joined(separator: "&")
        
        let signature = try hmacSHA256(key: config.apiSecret, message: queryString)
        cancelParams["signature"] = signature
        
        let finalQueryString = cancelParams.map { "\($0.key)=\($0.value)" }.joined(separator: "&")
        guard let url = URL(string: "https://api.mexc.com/api/v3/order?\(finalQueryString)") else {
            throw APIError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"
        request.setValue(config.apiKey, forHTTPHeaderField: "X-MEXC-APIKEY")
        
        let (_, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.networkError(NSError(domain: "Invalid response", code: -1))
        }
        
        return httpResponse.statusCode == 200
    }
    
    // MARK: - Positions
    func fetchMEXCPositions(config: ExchangeConfiguration, mode: TradingMode) async throws -> [RealPosition] {
        // MEXC spot trading doesn't have traditional positions like futures
        // This would return empty for spot trading
        return []
    }
    
    // MARK: - Market Data
    func fetchMEXCOrderBook(symbol: String) async throws -> OrderBook {
        guard let url = URL(string: "https://api.mexc.com/api/v3/depth?symbol=\(symbol)&limit=20") else {
            throw APIError.invalidURL
        }
        
        let request = URLRequest(url: url)
        let (data, _) = try await urlSession.data(for: request)
        
        let decoder = JSONDecoder()
        let response = try decoder.decode(MEXCOrderBookResponse.self, from: data)
        
        return OrderBook(
            bids: response.bids.map { OrderBookEntry(price: $0[0], size: $0[1]) },
            asks: response.asks.map { OrderBookEntry(price: $0[0], size: $0[1]) },
            timestamp: Int64(Date().timeIntervalSince1970 * 1000)
        )
    }
    
    func fetchMEXCTicker(symbol: String) async throws -> Ticker {
        guard let url = URL(string: "https://api.mexc.com/api/v3/ticker/24hr?symbol=\(symbol)") else {
            throw APIError.invalidURL
        }
        
        let request = URLRequest(url: url)
        let (data, _) = try await urlSession.data(for: request)
        
        let decoder = JSONDecoder()
        let response = try decoder.decode(MEXCTickerResponse.self, from: data)
        
        return Ticker(
            symbol: symbol,
            price: response.lastPrice,
            priceChange: response.priceChange,
            priceChangePercent: response.priceChangePercent,
            volume: response.volume,
            count: response.count
        )
    }
    
    // MARK: - Helper Functions
    private func mexcOrderType(from orderType: OrderType) -> String {
        switch orderType {
        case .market:
            return "MARKET"
        case .limit:
            return "LIMIT"
        case .stopLoss:
            return "STOP_LOSS"
        case .stopLossLimit:
            return "STOP_LOSS_LIMIT"
        case .takeProfit:
            return "TAKE_PROFIT"
        case .takeProfitLimit:
            return "TAKE_PROFIT_LIMIT"
        }
    }
    
    private func createTradingPairFromMEXC(_ data: MEXCSymbol, config: ExchangeConfiguration, mode: TradingMode) -> TradingPair? {
        guard data.status == "TRADING" else { return nil }
        
        return TradingPair(
            symbol: data.symbol,
            baseAsset: data.baseAsset,
            quoteAsset: data.quoteAsset,
            exchange: .mexc,
            tradingMode: mode,
            price: 0, // Would need separate ticker call
            priceChange24h: 0,
            volume24h: 0,
            minQty: data.filters.first { $0.filterType == "LOT_SIZE" }?.minQty.flatMap(Double.init) ?? 0,
            maxQty: data.filters.first { $0.filterType == "LOT_SIZE" }?.maxQty.flatMap(Double.init) ?? Double.greatestFiniteMagnitude,
            stepSize: data.filters.first { $0.filterType == "LOT_SIZE" }?.stepSize.flatMap(Double.init) ?? 0.001,
            tickSize: data.filters.first { $0.filterType == "PRICE_FILTER" }?.tickSize.flatMap(Double.init) ?? 0.01,
            isActive: data.status == "TRADING"
        )
    }
}

// MARK: - MEXC Response Models
struct MEXCExchangeInfoResponse: Codable {
    let timezone: String
    let serverTime: Int64
    let rateLimits: [MEXCRateLimit]
    let exchangeFilters: [String]
    let symbols: [MEXCSymbol]
}

struct MEXCRateLimit: Codable {
    let rateLimitType: String
    let interval: String
    let intervalNum: Int
    let limit: Int
}

struct MEXCSymbol: Codable {
    let symbol: String
    let status: String
    let baseAsset: String
    let baseAssetPrecision: Int
    let quoteAsset: String
    let quotePrecision: Int
    let quoteAssetPrecision: Int
    let orderTypes: [String]
    let icebergAllowed: Bool
    let ocoAllowed: Bool
    let isSpotTradingAllowed: Bool
    let isMarginTradingAllowed: Bool
    let filters: [MEXCSymbolFilter]
    let permissions: [String]
}

struct MEXCSymbolFilter: Codable {
    let filterType: String
    let minPrice: String?
    let maxPrice: String?
    let tickSize: String?
    let minQty: String?
    let maxQty: String?
    let stepSize: String?
    let limit: Int?
    let minTrailingAboveDelta: String?
    let maxTrailingAboveDelta: String?
    let minTrailingBelowDelta: String?
    let maxTrailingBelowDelta: String?
}

struct MEXCAccountResponse: Codable {
    let makerCommission: Int
    let takerCommission: Int
    let buyerCommission: Int
    let sellerCommission: Int
    let canTrade: Bool
    let canWithdraw: Bool
    let canDeposit: Bool
    let updateTime: Int64
    let accountType: String
    let balances: [MEXCBalance]
    let permissions: [String]
}

struct MEXCBalance: Codable {
    let asset: String
    let free: String
    let locked: String
}

struct MEXCOrderResponse: Codable {
    let symbol: String
    let orderId: Int64
    let orderListId: Int64
    let clientOrderId: String?
    let transactTime: Int64
    let price: String
    let origQty: String
    let executedQty: String
    let cummulativeQuoteQty: String
    let status: String
    let timeInForce: String
    let type: String
    let side: String
    let fills: [MEXCOrderFill]?
}

struct MEXCOrderFill: Codable {
    let price: String
    let qty: String
    let commission: String
    let commissionAsset: String
    let tradeId: Int64
}

struct MEXCOrderBookResponse: Codable {
    let lastUpdateId: Int64
    let bids: [[String]]
    let asks: [[String]]
}

struct MEXCTickerResponse: Codable {
    let symbol: String
    let priceChange: String
    let priceChangePercent: String
    let weightedAvgPrice: String
    let prevClosePrice: String
    let lastPrice: String
    let lastQty: String
    let bidPrice: String
    let bidQty: String
    let askPrice: String
    let askQty: String
    let openPrice: String
    let highPrice: String
    let lowPrice: String
    let volume: String
    let quoteVolume: String
    let openTime: Int64
    let closeTime: Int64
    let firstId: Int64
    let lastId: Int64
    let count: Int
} 