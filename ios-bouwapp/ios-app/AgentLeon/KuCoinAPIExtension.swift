import Foundation

// MARK: - KuCoin API Implementation
extension MultiExchangeAPIService {
    
    // MARK: - Market Data
    func fetchKuCoinMarketData(symbol: String) async throws -> MarketData {
        let baseURL = "https://api.kucoin.com"
        let endpoint = "/api/v1/market/stats?symbol=\(symbol)"
        let url = URL(string: "\(baseURL)\(endpoint)")!
        
        let (data, response) = try await urlSession.data(from: url)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.invalidResponse
        }
        
        let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] ?? [:]
        let dataJson = json["data"] as? [String: Any] ?? [:]
        
        // Convert KuCoin response to MarketData with candlestick data
        let price = Double(dataJson["last"] as? String ?? "0") ?? 0.0
        let volume = Double(dataJson["vol"] as? String ?? "0") ?? 0.0
        let high = Double(dataJson["high"] as? String ?? "0") ?? 0.0
        let low = Double(dataJson["low"] as? String ?? "0") ?? 0.0
        
        // KuCoin doesn't provide open price in 24hr stats, use close as approximation
        let candlestick = Candlestick(
            open: price,
            high: high,
            low: low,
            close: price,
            volume: volume,
            timestamp: Date()
        )
        
        return MarketData(
            symbol: symbol,
            candlesticks: [candlestick],
            timestamp: Date()
        )
    }
    
    // MARK: - Connection Testing
    func testKuCoinConnection(config: ExchangeConfiguration) async throws -> Bool {
        let baseURL = config.testnet ? "https://openapi-sandbox.kucoin.com" : "https://api.kucoin.com"
        guard let url = URL(string: "\(baseURL)/api/v1/accounts") else {
            throw APIError.invalidURL
        }
        
        let request = try createAuthenticatedRequest(url: url, method: "GET", body: nil, config: config)
        let (data, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.networkError(NSError(domain: "Invalid response", code: -1))
        }
        
        if httpResponse.statusCode == 200 {
            await MainActor.run {
                isConnected[.kucoin] = true
            }
            return true
        } else {
            throw APIError.authenticationFailed
        }
    }
    
    // MARK: - Trading Pairs
    func fetchKuCoinTradingPairs(config: ExchangeConfiguration, mode: TradingMode) async throws -> [TradingPair] {
        let baseURL = config.testnet ? "https://openapi-sandbox.kucoin.com" : "https://api.kucoin.com"
        
        let endpoint: String
        switch mode {
        case .spot:
            endpoint = "/api/v1/symbols"
        case .margin:
            endpoint = "/api/v1/margin/symbols"
        case .futures:
            endpoint = "/api/v1/contracts/active"
        case .leverageToken:
            endpoint = "/api/v3/etf/info"
        }
        
        guard let url = URL(string: "\(baseURL)\(endpoint)") else {
            throw APIError.invalidURL
        }
        
        let request = URLRequest(url: url)
        let (data, _) = try await urlSession.data(for: request)
        
        // Parse response and convert to TradingPair objects
        let decoder = JSONDecoder()
        
        if mode == .futures {
            let response = try decoder.decode(KuCoinFuturesSymbolsResponse.self, from: data)
            return response.data?.compactMap { symbolData in
                createTradingPairFromKuCoinFutures(symbolData, config: config, mode: mode)
            } ?? []
        } else {
            let response = try decoder.decode(KuCoinSpotSymbolsResponse.self, from: data)
            return response.data?.compactMap { symbolData in
                createTradingPairFromKuCoinSpot(symbolData, config: config, mode: mode)
            } ?? []
        }
    }
    
    // MARK: - Account Balance
    func fetchKuCoinBalance(config: ExchangeConfiguration) async throws -> [String: Double] {
        let baseURL = config.testnet ? "https://openapi-sandbox.kucoin.com" : "https://api.kucoin.com"
        guard let url = URL(string: "\(baseURL)/api/v1/accounts") else {
            throw APIError.invalidURL
        }
        
        let request = try createAuthenticatedRequest(url: url, method: "GET", body: nil, config: config)
        let (data, _) = try await urlSession.data(for: request)
        
        let decoder = JSONDecoder()
        let response = try decoder.decode(KuCoinAccountsResponse.self, from: data)
        
        var balances: [String: Double] = [:]
        response.data?.forEach { account in
            if let balance = Double(account.balance), balance > 0 {
                balances[account.currency] = balance
            }
        }
        
        return balances
    }
    
    // MARK: - Order Placement
    func placeKuCoinOrder(
        config: ExchangeConfiguration,
        symbol: String,
        side: OrderSide,
        type: OrderType,
        amount: Double,
        price: Double?,
        tradingMode: TradingMode,
        stopPrice: Double?,
        leverage: Double?
    ) async throws -> RealOrder {
        let baseURL = config.testnet ? "https://openapi-sandbox.kucoin.com" : "https://api.kucoin.com"
        
        let endpoint: String
        var orderData: [String: Any] = [:]
        
        switch tradingMode {
        case .spot:
            endpoint = "/api/v1/orders"
            orderData = [
                "clientOid": UUID().uuidString,
                "side": side.rawValue,
                "symbol": symbol,
                "type": type.rawValue,
                "size": String(amount)
            ]
            if let price = price {
                orderData["price"] = String(price)
            }
            
        case .margin:
            endpoint = "/api/v1/margin/order"
            orderData = [
                "clientOid": UUID().uuidString,
                "side": side.rawValue,
                "symbol": symbol,
                "type": type.rawValue,
                "size": String(amount),
                "marginModel": "cross"
            ]
            if let price = price {
                orderData["price"] = String(price)
            }
            
        case .futures:
            endpoint = "/api/v1/orders"
            orderData = [
                "clientOid": UUID().uuidString,
                "side": side.rawValue,
                "symbol": symbol,
                "type": type.rawValue,
                "size": Int(amount)
            ]
            if let leverage = leverage {
                orderData["leverage"] = String(Int(leverage))
            }
            if let price = price {
                orderData["price"] = String(price)
            }
            if let stopPrice = stopPrice {
                orderData["stopPrice"] = String(stopPrice)
                orderData["stop"] = side == .buy ? "up" : "down"
            }
            
        case .leverageToken:
            endpoint = "/api/v3/purchase"
            orderData = [
                "currency": symbol.replacingOccurrences(of: "3L", with: "").replacingOccurrences(of: "3S", with: ""),
                "size": String(amount)
            ]
        }
        
        guard let url = URL(string: "\(baseURL)\(endpoint)") else {
            throw APIError.invalidURL
        }
        
        let jsonData = try JSONSerialization.data(withJSONObject: orderData)
        let request = try createAuthenticatedRequest(url: url, method: "POST", body: jsonData, config: config)
        
        let (data, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.networkError(NSError(domain: "Invalid response", code: -1))
        }
        
        if httpResponse.statusCode != 200 {
            let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any]
            let errorMessage = errorData?["msg"] as? String ?? "Unknown error"
            throw APIError.apiError(errorMessage)
        }
        
        let decoder = JSONDecoder()
        let orderResponse = try decoder.decode(KuCoinOrderResponse.self, from: data)
        
        guard let orderResult = orderResponse.data else {
            throw APIError.noData
        }
        
        return RealOrder(
            exchange: .kucoin,
            exchangeOrderId: orderResult.orderId,
            clientOrderId: orderData["clientOid"] as? String ?? "",
            symbol: symbol,
            side: side,
            type: type,
            amount: amount,
            price: price,
            stopPrice: stopPrice,
            tradingMode: tradingMode,
            status: .pending,
            filledAmount: 0,
            averagePrice: nil,
            fees: 0,
            feeCurrency: "USDT",
            createdAt: Date(),
            updatedAt: Date(),
            strategy: nil
        )
    }
    
    // MARK: - Order Cancellation
    func cancelKuCoinOrder(config: ExchangeConfiguration, orderId: String, symbol: String) async throws -> Bool {
        let baseURL = config.testnet ? "https://openapi-sandbox.kucoin.com" : "https://api.kucoin.com"
        guard let url = URL(string: "\(baseURL)/api/v1/orders/\(orderId)") else {
            throw APIError.invalidURL
        }
        
        let request = try createAuthenticatedRequest(url: url, method: "DELETE", body: nil, config: config)
        let (data, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.networkError(NSError(domain: "Invalid response", code: -1))
        }
        
        return httpResponse.statusCode == 200
    }
    
    // MARK: - Positions
    func fetchKuCoinPositions(config: ExchangeConfiguration, mode: TradingMode) async throws -> [RealPosition] {
        guard mode == .futures || mode == .margin else { return [] }
        
        let baseURL = config.testnet ? "https://openapi-sandbox.kucoin.com" : "https://api.kucoin.com"
        
        let endpoint = mode == .futures ? "/api/v1/positions" : "/api/v1/margin/account"
        
        guard let url = URL(string: "\(baseURL)\(endpoint)") else {
            throw APIError.invalidURL
        }
        
        let request = try createAuthenticatedRequest(url: url, method: "GET", body: nil, config: config)
        let (data, _) = try await urlSession.data(for: request)
        
        let decoder = JSONDecoder()
        
        if mode == .futures {
            let response = try decoder.decode(KuCoinPositionsResponse.self, from: data)
            return response.data?.compactMap { positionData in
                createRealPositionFromKuCoin(positionData, mode: mode)
            } ?? []
        } else {
            // For margin positions, we need to parse differently
            return []
        }
    }
    
    // MARK: - Market Data
    func fetchKuCoinOrderBook(symbol: String) async throws -> OrderBook {
        guard let url = URL(string: "https://api.kucoin.com/api/v1/market/orderbook/level2_20?symbol=\(symbol)") else {
            throw APIError.invalidURL
        }
        
        let request = URLRequest(url: url)
        let (data, _) = try await urlSession.data(for: request)
        
        let decoder = JSONDecoder()
        let response = try decoder.decode(KuCoinOrderBookResponse.self, from: data)
        
        guard let orderBookData = response.data else {
            throw APIError.noData
        }
        
        return OrderBook(
            bids: orderBookData.bids.map { OrderBookEntry(price: $0[0], size: $0[1]) },
            asks: orderBookData.asks.map { OrderBookEntry(price: $0[0], size: $0[1]) },
            timestamp: orderBookData.time
        )
    }
    
    func fetchKuCoinTicker(symbol: String) async throws -> Ticker {
        guard let url = URL(string: "https://api.kucoin.com/api/v1/market/stats?symbol=\(symbol)") else {
            throw APIError.invalidURL
        }
        
        let request = URLRequest(url: url)
        let (data, _) = try await urlSession.data(for: request)
        
        let decoder = JSONDecoder()
        let response = try decoder.decode(KuCoinTickerResponse.self, from: data)
        
        guard let tickerData = response.data else {
            throw APIError.noData
        }
        
        return Ticker(
            symbol: symbol,
            price: tickerData.last,
            priceChange: tickerData.changePrice,
            priceChangePercent: tickerData.changeRate,
            volume: tickerData.vol,
            count: nil
        )
    }
    
    // MARK: - Helper Functions
    private func createTradingPairFromKuCoinSpot(_ data: KuCoinSpotSymbol, config: ExchangeConfiguration, mode: TradingMode) -> TradingPair? {
        guard data.enableTrading else { return nil }
        
        return TradingPair(
            symbol: data.symbol,
            baseAsset: data.baseCurrency,
            quoteAsset: data.quoteCurrency,
            exchange: .kucoin,
            tradingMode: mode,
            price: Double(data.last ?? "0") ?? 0,
            priceChange24h: Double(data.changeRate ?? "0") ?? 0,
            volume24h: Double(data.volValue ?? "0") ?? 0,
            minQty: Double(data.baseMinSize) ?? 0,
            maxQty: Double(data.baseMaxSize) ?? Double.greatestFiniteMagnitude,
            stepSize: Double(data.baseIncrement) ?? 0.00000001,
            tickSize: Double(data.priceIncrement) ?? 0.00000001,
            isActive: data.enableTrading
        )
    }
    
    private func createTradingPairFromKuCoinFutures(_ data: KuCoinFuturesSymbol, config: ExchangeConfiguration, mode: TradingMode) -> TradingPair? {
        guard data.status == "Open" else { return nil }
        
        return TradingPair(
            symbol: data.symbol,
            baseAsset: data.baseCurrency,
            quoteAsset: data.quoteCurrency,
            exchange: .kucoin,
            tradingMode: mode,
            price: Double(data.markPrice ?? "0") ?? 0,
            priceChange24h: 0, // Would need separate call to get 24h change
            volume24h: Double(data.turnoverOf24h ?? "0") ?? 0,
            minQty: Double(data.lotSize) ?? 1,
            maxQty: Double(data.maxOrderQty ?? "1000000") ?? 1000000,
            stepSize: Double(data.lotSize) ?? 1,
            tickSize: Double(data.tickSize) ?? 0.01,
            isActive: data.status == "Open"
        )
    }
    
    private func createRealPositionFromKuCoin(_ data: KuCoinPosition, mode: TradingMode) -> RealPosition? {
        guard let size = Double(data.currentQty), size != 0 else { return nil }
        
        return RealPosition(
            exchange: .kucoin,
            symbol: data.symbol,
            side: size > 0 ? .buy : .sell,
            size: abs(size),
            entryPrice: Double(data.avgEntryPrice) ?? 0,
            markPrice: Double(data.markPrice) ?? 0,
            leverage: Double(data.realLeverage) ?? 1,
            margin: Double(data.posMargin) ?? 0,
            tradingMode: mode,
            unrealizedPnl: Double(data.unrealisedPnl) ?? 0,
            unrealizedPnlPercentage: Double(data.unrealisedPnlPcnt) ?? 0,
            createdAt: Date(),
            strategy: nil
        )
    }
}

// MARK: - KuCoin Response Models
struct KuCoinSpotSymbolsResponse: Codable {
    let code: String
    let data: [KuCoinSpotSymbol]?
}

struct KuCoinSpotSymbol: Codable {
    let symbol: String
    let name: String
    let baseCurrency: String
    let quoteCurrency: String
    let baseMinSize: String
    let quoteMinSize: String
    let baseMaxSize: String
    let quoteMaxSize: String
    let baseIncrement: String
    let quoteIncrement: String
    let priceIncrement: String
    let enableTrading: Bool
    let isMarginEnabled: Bool
    let last: String?
    let changeRate: String?
    let volValue: String?
}

struct KuCoinFuturesSymbolsResponse: Codable {
    let code: String
    let data: [KuCoinFuturesSymbol]?
}

struct KuCoinFuturesSymbol: Codable {
    let symbol: String
    let rootSymbol: String
    let type: String
    let firstOpenDate: Int64
    let expireDate: Int64?
    let settleDate: Int64?
    let baseCurrency: String
    let quoteCurrency: String
    let settleCurrency: String
    let maxOrderQty: String?
    let maxPrice: String
    let lotSize: String
    let tickSize: String
    let indexPriceTickSize: String
    let multiplier: String
    let initialMargin: String
    let maintainMargin: String
    let maxRiskLimit: String
    let minRiskLimit: String
    let riskLimitStep: String
    let makerFeeRate: String
    let takerFeeRate: String
    let takerFixedFee: String
    let makerFixedFee: String
    let settlementFee: String?
    let isDeleverage: Bool
    let isQuanto: Bool
    let isInverse: Bool
    let markMethod: String
    let fairMethod: String
    let fundingBaseSymbol: String?
    let fundingQuoteSymbol: String?
    let fundingRateSymbol: String?
    let indexSymbol: String
    let settlementSymbol: String?
    let status: String
    let fundingFeeRate: String?
    let predictedFundingFeeRate: String?
    let openInterest: String?
    let turnoverOf24h: String?
    let volumeOf24h: String?
    let markPrice: String?
    let indexPrice: String?
    let lastTradePrice: String?
    let nextFundingRateTime: Int64?
}

struct KuCoinAccountsResponse: Codable {
    let code: String
    let data: [KuCoinAccount]?
}

struct KuCoinAccount: Codable {
    let id: String
    let currency: String
    let type: String
    let balance: String
    let available: String
    let holds: String
}

struct KuCoinOrderResponse: Codable {
    let code: String
    let data: KuCoinOrderResult?
}

struct KuCoinOrderResult: Codable {
    let orderId: String
}

struct KuCoinPositionsResponse: Codable {
    let code: String
    let data: [KuCoinPosition]?
}

struct KuCoinPosition: Codable {
    let id: String
    let symbol: String
    let autoDeposit: Bool
    let maintMarginReq: String
    let riskLimit: String
    let realLeverage: String
    let crossMode: Bool
    let delevPercentage: String
    let openingTimestamp: Int64
    let currentTimestamp: Int64
    let currentQty: String
    let currentCost: String
    let currentComm: String
    let unrealisedCost: String
    let realisedGrossCost: String
    let realisedCost: String
    let isOpen: Bool
    let markPrice: String
    let markValue: String
    let posCost: String
    let posCross: String
    let posInit: String
    let posComm: String
    let posLoss: String
    let posMargin: String
    let posMaint: String
    let maintMargin: String
    let realisedGrossPnl: String
    let realisedPnl: String
    let unrealisedPnl: String
    let unrealisedPnlPcnt: String
    let unrealisedRoePcnt: String
    let avgEntryPrice: String
    let liquidationPrice: String
    let bankruptPrice: String
    let settleCurrency: String
}

struct KuCoinOrderBookResponse: Codable {
    let code: String
    let data: KuCoinOrderBookData?
}

struct KuCoinOrderBookData: Codable {
    let time: Int64
    let sequence: String
    let bids: [[String]]
    let asks: [[String]]
}

struct KuCoinTickerResponse: Codable {
    let code: String
    let data: KuCoinTickerData?
}

struct KuCoinTickerData: Codable {
    let time: Int64
    let symbol: String
    let last: String
    let changePrice: String
    let changeRate: String
    let open: String
    let high: String
    let low: String
    let vol: String
    let volValue: String
    let buy: String
    let sell: String
    let sort: Int
    let averagePrice: String
    let takerFeeRate: String
    let makerFeeRate: String
    let takerCoefficient: String
    let makerCoefficient: String
} 