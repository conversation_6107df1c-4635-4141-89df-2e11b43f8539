import SwiftUI

struct ExchangeConfigurationView: View {
    @ObservedObject var apiService: MultiExchangeAPIService
    @Environment(\.presentationMode) var presentationMode
    @State private var selectedExchange: SupportedExchange = .kucoin
    @State private var editingConfig: ExchangeConfiguration?
    @State private var isTestingConnection = false
    @State private var testResult: String = ""
    @State private var showingTestResult = false
    
    // Form fields
    @State private var apiKey: String = ""
    @State private var apiSecret: String = ""
    @State private var passphrase: String = ""
    @State private var isTestnet: Bool = true
    @State private var isEnabled: Bool = false
    @State private var selectedTradingModes: Set<TradingMode> = [.spot]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerView
                
                // Exchange Selector
                exchangeSelectorView
                
                // Configuration Form
                configurationFormView
                
                Spacer()
                
                // Action Buttons
                actionButtonsView
            }
            .background(Color.black)
            .navigationBarHidden(true)
        }
        .preferredColorScheme(.dark)
        .onAppear {
            loadConfiguration()
        }
        .alert("Connection Test", isPresented: $showingTestResult) {
            Button("OK") { }
        } message: {
            Text(testResult)
        }
    }
    
    // MARK: - Header View
    private var headerView: some View {
        HStack {
            Button("Cancel") {
                presentationMode.wrappedValue.dismiss()
            }
            .foregroundColor(.cyan)
            
            Spacer()
            
            Text("Exchange Configuration")
                .font(.headline)
                .foregroundColor(.white)
            
            Spacer()
            
            Button("Save") {
                saveConfiguration()
            }
            .foregroundColor(.cyan)
            .fontWeight(.semibold)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
    }
    
    // MARK: - Exchange Selector
    private var exchangeSelectorView: some View {
        VStack(spacing: 12) {
            Text("Select Exchange")
                .font(.headline)
                .foregroundColor(.white)
            
            HStack(spacing: 20) {
                ForEach(SupportedExchange.allCases, id: \.self) { exchange in
                    ExchangeSelectionCard(
                        exchange: exchange,
                        isSelected: selectedExchange == exchange,
                        isConnected: apiService.isConnected[exchange] == true
                    ) {
                        selectedExchange = exchange
                        loadConfiguration()
                    }
                }
            }
        }
        .padding()
    }
    
    // MARK: - Configuration Form
    private var configurationFormView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // API Configuration Section
                apiConfigurationSection
                
                // Trading Modes Section
                tradingModesSection
                
                // Settings Section
                settingsSection
                
                // Connection Test Section
                connectionTestSection
            }
            .padding()
        }
    }
    
    private var apiConfigurationSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("API Configuration")
                .font(.headline)
                .foregroundColor(.cyan)
            
            VStack(spacing: 12) {
                // API Key Field
                VStack(alignment: .leading, spacing: 4) {
                    Text("API Key")
                        .font(.caption)
                        .foregroundColor(.gray)
                    
                    SecureField("Enter API Key", text: $apiKey)
                        .textFieldStyle(CustomTextFieldStyle())
                }
                
                // API Secret Field
                VStack(alignment: .leading, spacing: 4) {
                    Text("API Secret")
                        .font(.caption)
                        .foregroundColor(.gray)
                    
                    SecureField("Enter API Secret", text: $apiSecret)
                        .textFieldStyle(CustomTextFieldStyle())
                }
                
                // Passphrase Field (KuCoin only)
                if selectedExchange == .kucoin {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Passphrase")
                            .font(.caption)
                            .foregroundColor(.gray)
                        
                        SecureField("Enter Passphrase", text: $passphrase)
                            .textFieldStyle(CustomTextFieldStyle())
                    }
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var tradingModesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Trading Modes")
                .font(.headline)
                .foregroundColor(.cyan)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                ForEach(availableTradingModes, id: \.self) { mode in
                    TradingModeSelectionCard(
                        mode: mode,
                        isSelected: selectedTradingModes.contains(mode),
                        isAvailable: isModeAvailable(mode)
                    ) {
                        if selectedTradingModes.contains(mode) {
                            selectedTradingModes.remove(mode)
                        } else {
                            selectedTradingModes.insert(mode)
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var settingsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Settings")
                .font(.headline)
                .foregroundColor(.cyan)
            
            VStack(spacing: 12) {
                // Testnet Toggle
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Use Testnet")
                            .foregroundColor(.white)
                        
                        Text("Use test environment for safer testing")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                    
                    Spacer()
                    
                    Toggle("", isOn: $isTestnet)
                        .scaleEffect(0.9)
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
                
                // Enabled Toggle
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Enable Exchange")
                            .foregroundColor(.white)
                        
                        Text("Allow trading on this exchange")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                    
                    Spacer()
                    
                    Toggle("", isOn: $isEnabled)
                        .scaleEffect(0.9)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var connectionTestSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Connection Test")
                .font(.headline)
                .foregroundColor(.cyan)
            
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Test API Connection")
                        .foregroundColor(.white)
                    
                    Text("Verify your API credentials")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                
                Spacer()
                
                Button(action: testConnection) {
                    HStack {
                        if isTestingConnection {
                            ProgressView()
                                .scaleEffect(0.8)
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        } else {
                            Image(systemName: "network")
                        }
                        
                        Text("Test")
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(isTestingConnection ? Color.gray : Color.blue)
                    .cornerRadius(8)
                }
                .disabled(isTestingConnection || apiKey.isEmpty || apiSecret.isEmpty)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    // MARK: - Action Buttons
    private var actionButtonsView: some View {
        HStack(spacing: 16) {
            Button("Reset to Defaults") {
                resetConfiguration()
            }
            .foregroundColor(.orange)
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color.orange.opacity(0.2))
            .cornerRadius(12)
            
            Button("Save Configuration") {
                saveConfiguration()
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                LinearGradient(
                    colors: [.cyan, .blue],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(12)
        }
        .padding()
    }
    
    // MARK: - Helper Properties
    private var availableTradingModes: [TradingMode] {
        switch selectedExchange {
        case .kucoin:
            return TradingMode.allCases
        case .bybit:
            return [.spot, .margin, .futures, .leverageToken]
        case .mexc:
            return [.spot] // MEXC primarily supports spot trading
        case .binance:
            return TradingMode.allCases // Binance supports all trading modes
        }
    }
    
    // MARK: - Helper Functions
    private func isModeAvailable(_ mode: TradingMode) -> Bool {
        return availableTradingModes.contains(mode)
    }
    
    private func loadConfiguration() {
        if let config = apiService.getConfiguration(for: selectedExchange) {
            editingConfig = config
            apiKey = config.apiKey
            apiSecret = config.apiSecret
            passphrase = config.passphrase ?? ""
            isTestnet = config.testnet
            isEnabled = config.isEnabled
            selectedTradingModes = Set(config.tradingModes)
        } else {
            resetConfiguration()
        }
    }
    
    private func resetConfiguration() {
        editingConfig = ExchangeConfiguration(exchange: selectedExchange)
        apiKey = ""
        apiSecret = ""
        passphrase = ""
        isTestnet = true
        isEnabled = false
        selectedTradingModes = [.spot]
    }
    
    private func saveConfiguration() {
        var config = editingConfig ?? ExchangeConfiguration(exchange: selectedExchange)
        config.apiKey = apiKey
        config.apiSecret = apiSecret
        config.passphrase = selectedExchange == .kucoin ? passphrase : nil
        config.testnet = isTestnet
        config.isEnabled = isEnabled
        config.tradingModes = Array(selectedTradingModes)
        
        apiService.updateExchangeConfiguration(config)
        presentationMode.wrappedValue.dismiss()
    }
    
    private func testConnection() {
        guard !apiKey.isEmpty && !apiSecret.isEmpty else { return }
        
        isTestingConnection = true
        
        // Create temporary config for testing
        var tempConfig = ExchangeConfiguration(exchange: selectedExchange)
        tempConfig.apiKey = apiKey
        tempConfig.apiSecret = apiSecret
        tempConfig.passphrase = selectedExchange == .kucoin ? passphrase : nil
        tempConfig.testnet = isTestnet
        tempConfig.isEnabled = true
        
        // Update config temporarily for testing
        apiService.updateExchangeConfiguration(tempConfig)
        
        Task {
            let success = await apiService.testConnection(for: selectedExchange)
            
            await MainActor.run {
                isTestingConnection = false
                testResult = success ? "✅ Connection successful!" : "❌ Connection failed. Please check your credentials."
                showingTestResult = true
            }
        }
    }
}

// MARK: - Supporting Views
struct ExchangeSelectionCard: View {
    let exchange: SupportedExchange
    let isSelected: Bool
    let isConnected: Bool
    let onSelect: () -> Void
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: exchange.iconName)
                .font(.title)
                .foregroundColor(isSelected ? .cyan : .gray)
            
            Text(exchange.rawValue)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(isSelected ? .cyan : .white)
            
            Circle()
                .fill(isConnected ? Color.green : Color.red)
                .frame(width: 8, height: 8)
        }
        .frame(width: 80, height: 80)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isSelected ? Color.cyan.opacity(0.2) : Color.gray.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(isSelected ? Color.cyan : Color.clear, lineWidth: 2)
                )
        )
        .contentShape(Rectangle())
        .onTapGesture(perform: onSelect)
    }
}

struct TradingModeSelectionCard: View {
    let mode: TradingMode
    let isSelected: Bool
    let isAvailable: Bool
    let onSelect: () -> Void
    
    var body: some View {
        VStack(spacing: 8) {
            Text(mode.rawValue)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(isAvailable ? (isSelected ? .cyan : .white) : .gray)
            
            Text(mode.description)
                .font(.system(size: 10))
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 8)
        .frame(height: 60)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(isSelected ? Color.cyan.opacity(0.2) : Color.gray.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(isSelected ? Color.cyan : Color.clear, lineWidth: 1)
                )
        )
        .opacity(isAvailable ? 1.0 : 0.5)
        .contentShape(Rectangle())
        .onTapGesture {
            if isAvailable {
                onSelect()
            }
        }
    }
}

struct CustomTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(12)
            .background(Color.black.opacity(0.3))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
            )
            .foregroundColor(.white)
    }
} 