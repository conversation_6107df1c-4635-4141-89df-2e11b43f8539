import SwiftUI

struct ContentView: View {
    var body: some View {
        NavigationView {
            VStack {
            Text("🤖 Agent Leon")
                .font(.largeTitle)
                .fontWeight(.bold)
                .padding()

            Text("AI Trading Assistant")
                .font(.title2)
                .foregroundColor(.secondary)
                .padding()

            Spacer()

            VStack(spacing: 20) {
                NavigationLink(destination: CryptoTradingView(tradingAgent: CryptoTradingAgent(
                    name: "Crypto Trading Agent",
                    status: .idle,
                    performance: 100.0,
                    capabilities: ["Crypto Trading", "Market Analysis"],
                    version: "1.0.0"
                ))) {
                    HStack {
                        Image(systemName: "chart.line.uptrend.xyaxis")
                        Text("Crypto Trading")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }

                NavigationLink(destination: MultiExchangeTradingView()) {
                    HStack {
                        Image(systemName: "arrow.triangle.swap")
                        Text("Multi-Exchange Trading")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.green)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }

                NavigationLink(destination: APIConfigurationView()) {
                    HStack {
                        Image(systemName: "gear")
                        Text("API Configuration")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.purple)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }
            }
            .padding()

            Spacer()
            }
            .navigationTitle("Agent Leon")
        }
    }
}

// MARK: - Rules View
struct RulesView: View {
    @ObservedObject var agentManager: AgentManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            List {
                ForEach(agentManager.agents) { agent in
                    Section(header: Text("Agent: \(agent.name)")) {
                        ForEach(agent.rules, id: \.id) { rule in
                            RuleRow(rule: rule)
                        }
                    }
                }
            }
            .navigationTitle("Agent Rules")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct RuleRow: View {
    let rule: AgentRule
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(rule.name)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                if rule.isActive {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                } else {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.red)
                }
            }
            
            Text(rule.description)
                .font(.caption)
                .foregroundColor(.secondary)
            
            HStack {
                Text("Priority: \(rule.priority)")
                    .font(.caption2)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.2))
                    .cornerRadius(4)
                
                Spacer()
            }
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    ContentView()
} 
