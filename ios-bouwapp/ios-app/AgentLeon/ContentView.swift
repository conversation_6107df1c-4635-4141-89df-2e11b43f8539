import SwiftUI

struct ContentView: View {
    var body: some View {
        ModernDashboardView()
    }
    
    private var headerView: some View {
        VStack(spacing: 12) {
            HStack {
                Button(action: { isShowingRules = true }) {
                    Image(systemName: "gearshape.fill")
                        .foregroundColor(.white)
                        .font(.title2)
                }
                
                Spacer()
                
                Text("AGENT LEON CONTROL")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .tracking(2)
                
                Spacer()
                
                But<PERSON>(action: { isShowingCryptoTrading = true }) {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                        .foregroundColor(.orange)
                        .font(.title2)
                }
                
                Button(action: { isShowingMultiExchangeTrading = true }) {
                    Image(systemName: "arrow.triangle.swap")
                        .foregroundColor(.cyan)
                        .font(.title2)
                }
                
                Button(action: { isShowingAPIConfiguration = true }) {
                    Image(systemName: "gear")
                        .foregroundColor(.purple)
                        .font(.title2)
                }
                
                But<PERSON>(action: { isShowingAITrading = true }) {
                    Image(systemName: "brain.head.profile")
                        .foregroundColor(.pink)
                        .font(.title2)
                }
                
                systemStatusIndicator
            }
            
            // Global Performance Bar
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("System Performance")
                        .font(.caption)
                        .foregroundColor(.gray)
                    
                    Spacer()
                    
                    Text("\(Int(agentManager.globalPerformance))%")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                }
                
                ProgressView(value: agentManager.globalPerformance, total: 100)
                    .progressViewStyle(LinearProgressViewStyle(tint: performanceColor))
                    .scaleEffect(y: 2)
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 10)
    }
    
    private var systemStatusIndicator: some View {
        HStack(spacing: 8) {
            Circle()
                .fill(statusColor)
                .frame(width: 12, height: 12)
                .animation(.easeInOut(duration: 1.0).repeatForever(), value: agentManager.systemStatus)
            
            Text(agentManager.systemStatus)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.white)
        }
    }
    
    private var performanceColor: Color {
        if agentManager.globalPerformance > 80 {
            return .green
        } else if agentManager.globalPerformance > 60 {
            return .yellow
        } else if agentManager.globalPerformance > 40 {
            return .orange
        } else {
            return .red
        }
    }
    
    private var statusColor: Color {
        switch agentManager.systemStatus {
        case "Optimal":
            return .green
        case "Good":
            return .blue
        case "Warning":
            return .yellow
        case "Critical":
            return .red
        default:
            return .gray
        }
    }
}

// MARK: - Rules View
struct RulesView: View {
    @ObservedObject var agentManager: AgentManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            List {
                ForEach(agentManager.agents) { agent in
                    Section(header: Text("Agent: \(agent.name)")) {
                        ForEach(agent.rules, id: \.id) { rule in
                            RuleRow(rule: rule)
                        }
                    }
                }
            }
            .navigationTitle("Agent Rules")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct RuleRow: View {
    let rule: AgentRule
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(rule.name)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                if rule.isActive {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                } else {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.red)
                }
            }
            
            Text(rule.description)
                .font(.caption)
                .foregroundColor(.secondary)
            
            HStack {
                Text("Priority: \(rule.priority)")
                    .font(.caption2)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.2))
                    .cornerRadius(4)
                
                Spacer()
            }
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    ContentView()
} 