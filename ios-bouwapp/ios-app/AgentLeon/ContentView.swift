import SwiftUI

struct ContentView: View {
    var body: some View {
        ModernDashboardView()
    }
}

// MARK: - Rules View
struct RulesView: View {
    @ObservedObject var agentManager: AgentManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            List {
                ForEach(agentManager.agents) { agent in
                    Section(header: Text("Agent: \(agent.name)")) {
                        ForEach(agent.rules, id: \.id) { rule in
                            RuleRow(rule: rule)
                        }
                    }
                }
            }
            .navigationTitle("Agent Rules")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct RuleRow: View {
    let rule: AgentRule
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(rule.name)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                if rule.isActive {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                } else {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.red)
                }
            }
            
            Text(rule.description)
                .font(.caption)
                .foregroundColor(.secondary)
            
            HStack {
                Text("Priority: \(rule.priority)")
                    .font(.caption2)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.2))
                    .cornerRadius(4)
                
                Spacer()
            }
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    ContentView()
} 