import SwiftUI

struct CryptoTradingView: View {
    @ObservedObject var tradingAgent: CryptoTradingAgent
    @State private var selectedTab = 0
    @State private var showingTradeDialog = false
    @State private var selectedCrypto: Cryptocurrency?
    @State private var selectedExchange: String = "KuCoin"
    @State private var selectedTradingMode: String = "Spot"
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Portfolio Summary Header
                portfolioSummaryHeader
                
                // Tab Selector
                Picker("Trading Tabs", selection: $selectedTab) {
                    Text("Watchlist").tag(0)
                    Text("Portfolio").tag(1)
                    Text("Signals").tag(2)
                    Text("Strategies").tag(3)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding()
                .background(Color.black.opacity(0.3))
                
                // Content Based on Selected Tab
                TabView(selection: $selectedTab) {
                    // Watchlist Tab
                    watchlistView
                        .tag(0)
                    
                    // Portfolio Tab
                    portfolioView
                        .tag(1)
                    
                    // Trading Signals Tab
                    tradingSignalsView
                        .tag(2)
                    
                    // Strategies Tab
                    strategiesView
                        .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [Color.black, Color.gray.opacity(0.8)]),
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
            .navigationTitle("Crypto Trading")
            .navigationBarTitleDisplayMode(.large)
            .foregroundColor(.white)
            .sheet(isPresented: $showingTradeDialog) {
                if let crypto = selectedCrypto {
                    TradeDialogView(crypto: crypto, tradingAgent: tradingAgent)
                }
            }
        }
        .preferredColorScheme(.dark)
    }
    
    // MARK: - Portfolio Summary Header
    private var portfolioSummaryHeader: some View {
        VStack(spacing: 12) {
            // Balance & P&L
            HStack {
                VStack(alignment: .leading) {
                    Text("Balance")
                        .font(.caption)
                        .foregroundColor(.gray)
                    Text("$\(String(format: "%.2f", tradingAgent.balance))")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("Total P&L")
                        .font(.caption)
                        .foregroundColor(.gray)
                    Text("$\(String(format: "%.2f", tradingAgent.totalPnL))")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(tradingAgent.totalPnL >= 0 ? .green : .red)
                }
            }
            
            // Auto Trading Toggle
            HStack {
                Text("Auto Trading")
                    .font(.body)
                    .foregroundColor(.white)
                
                Spacer()
                
                Toggle("Auto Trading", isOn: $tradingAgent.isAutoTradingEnabled)
                    .toggleStyle(SwitchToggleStyle(tint: .orange))
                    .labelsHidden()
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black.opacity(0.4))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.orange.opacity(0.6), lineWidth: 1)
                )
        )
        .padding(.horizontal, 20)
    }
    
    // MARK: - Watchlist View
    private var watchlistView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(tradingAgent.watchlist) { crypto in
                    CryptoCard(crypto: crypto) {
                        selectedCrypto = crypto
                        showingTradeDialog = true
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
        }
    }
    
    // MARK: - Portfolio View
    private var portfolioView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                if tradingAgent.portfolio.isEmpty {
                    EmptyPortfolioView()
                } else {
                    ForEach(tradingAgent.portfolio) { position in
                        PositionCard(position: position, currentPrice: getCurrentPrice(for: position.symbol))
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
        }
    }
    
    // MARK: - Trading Signals View
    private var tradingSignalsView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                if tradingAgent.tradingSignals.isEmpty {
                    EmptySignalsView()
                } else {
                    ForEach(tradingAgent.tradingSignals.reversed()) { signal in
                        TradingSignalCard(signal: signal, tradingAgent: tradingAgent)
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
        }
    }
    
    // MARK: - Strategies View
    private var strategiesView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                Text("Trading Strategies")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .padding(.top)
                
                if let activeStrategy = tradingAgent.activeStrategy {
                    StrategyCard(strategy: activeStrategy, isActive: true)
                }
                
                Text("Available Strategies")
                    .font(.headline)
                    .foregroundColor(.gray)
                    .padding(.top)
                
                // Add other strategies here when implemented
            }
            .padding(.horizontal, 20)
        }
    }
    
    private func getCurrentPrice(for symbol: String) -> Double {
        return tradingAgent.watchlist.first { $0.symbol == symbol }?.price ?? 0.0
    }
}

// MARK: - Crypto Card
struct CryptoCard: View {
    let crypto: Cryptocurrency
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack {
                // Crypto Info
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(crypto.symbol)
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        Text(crypto.name)
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                    
                    Text("$\(String(format: "%.2f", crypto.price))")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                }
                
                Spacer()
                
                // Price Change
                VStack(alignment: .trailing) {
                    Text(crypto.changePercentage)
                        .font(.body)
                        .fontWeight(.bold)
                        .foregroundColor(crypto.isPositive ? .green : .red)
                    
                    HStack {
                        Image(systemName: crypto.isPositive ? "arrow.up.right" : "arrow.down.right")
                            .foregroundColor(crypto.isPositive ? .green : .red)
                            .font(.caption)
                        
                        Text("24h")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.black.opacity(0.4))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(crypto.isPositive ? Color.green.opacity(0.3) : Color.red.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Position Card
struct PositionCard: View {
    let position: TradingPosition
    let currentPrice: Double
    
    var body: some View {
        VStack(spacing: 12) {
            // Header
            HStack {
                VStack(alignment: .leading) {
                    Text(position.symbol)
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text(position.type.rawValue)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.blue.opacity(0.3))
                        .cornerRadius(6)
                        .foregroundColor(.blue)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("$\(String(format: "%.2f", position.pnl))")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(position.pnl >= 0 ? .green : .red)
                    
                    Text("\(String(format: "%.1f", position.pnlPercentage))%")
                        .font(.caption)
                        .foregroundColor(position.pnl >= 0 ? .green : .red)
                }
            }
            
            // Position Details
            HStack {
                DetailItem(title: "Amount", value: String(format: "%.4f", position.amount))
                Spacer()
                DetailItem(title: "Entry", value: "$\(String(format: "%.2f", position.entryPrice))")
                Spacer()
                DetailItem(title: "Current", value: "$\(String(format: "%.2f", currentPrice))")
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.black.opacity(0.4))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(position.pnl >= 0 ? Color.green.opacity(0.4) : Color.red.opacity(0.4), lineWidth: 1)
                )
        )
    }
}

// MARK: - Trading Signal Card
struct TradingSignalCard: View {
    let signal: TradingSignal
    let tradingAgent: CryptoTradingAgent
    
    var body: some View {
        HStack {
            // Signal Type Icon
            Text(signal.type.emoji)
                .font(.title2)
            
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(signal.type == .buy ? "BUY" : "SELL")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(signal.type == .buy ? .green : .red)
                    
                    Text(signal.symbol)
                        .font(.body)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                    
                    Text("@ $\(String(format: "%.2f", signal.price))")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                
                Text(signal.reason)
                    .font(.caption)
                    .foregroundColor(.gray)
                    .lineLimit(2)
            }
            
            Spacer()
            
            // Execute Button
            if !tradingAgent.isAutoTradingEnabled {
                Button("Execute") {
                    tradingAgent.executeTrade(signal: signal)
                }
                .font(.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.orange.opacity(0.8))
                .foregroundColor(.white)
                .cornerRadius(8)
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color.black.opacity(0.3))
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - Strategy Card
struct StrategyCard: View {
    let strategy: TradingStrategy
    let isActive: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(strategy.name)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Spacer()
                
                if isActive {
                    Text("ACTIVE")
                        .font(.caption)
                        .fontWeight(.bold)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.green.opacity(0.8))
                        .foregroundColor(.white)
                        .cornerRadius(6)
                }
            }
            
            Text(strategy.description)
                .font(.body)
                .foregroundColor(.gray)
            
            HStack {
                Text("Risk: \(strategy.riskLevel.rawValue)")
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(riskLevelColor.opacity(0.3))
                    .foregroundColor(riskLevelColor)
                    .cornerRadius(6)
                
                Text("Timeframe: \(strategy.timeFrame.rawValue)")
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.3))
                    .foregroundColor(.blue)
                    .cornerRadius(6)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.black.opacity(0.4))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(isActive ? Color.green.opacity(0.6) : Color.gray.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    private var riskLevelColor: Color {
        switch strategy.riskLevel {
        case .low: return .green
        case .medium: return .yellow
        case .high: return .orange
        case .extreme: return .red
        }
    }
}

// MARK: - Helper Views
struct DetailItem: View {
    let title: String
    let value: String
    
    var body: some View {
        VStack(alignment: .center) {
            Text(title)
                .font(.caption)
                .foregroundColor(.gray)
            Text(value)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.white)
        }
    }
}

struct EmptyPortfolioView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "briefcase")
                .font(.system(size: 50))
                .foregroundColor(.gray)
            
            Text("No Active Positions")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Text("Start trading to see your positions here")
                .font(.body)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
        }
        .padding(40)
    }
}

struct EmptySignalsView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "antenna.radiowaves.left.and.right")
                .font(.system(size: 50))
                .foregroundColor(.gray)
            
            Text("No Trading Signals")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Text("Trading signals will appear here when market conditions are right")
                .font(.body)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
        }
        .padding(40)
    }
}

// MARK: - Trade Dialog
struct TradeDialogView: View {
    let crypto: Cryptocurrency
    let tradingAgent: CryptoTradingAgent
    @Environment(\.dismiss) private var dismiss
    @State private var tradeAmount: String = ""
    @State private var tradeType: TradingPosition.PositionType = .long
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Crypto Header
                VStack {
                    Text(crypto.symbol)
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text("$\(String(format: "%.2f", crypto.price))")
                        .font(.title)
                        .foregroundColor(.white)
                    
                    Text(crypto.changePercentage)
                        .font(.body)
                        .foregroundColor(crypto.isPositive ? .green : .red)
                }
                .padding()
                
                // Trade Controls
                VStack(spacing: 16) {
                    Picker("Trade Type", selection: $tradeType) {
                        Text("Long").tag(TradingPosition.PositionType.long)
                        Text("Short").tag(TradingPosition.PositionType.short)
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    
                    TextField("Amount", text: $tradeAmount)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .keyboardType(.decimalPad)
                }
                .padding()
                
                Spacer()
                
                // Trade Buttons
                HStack(spacing: 16) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.gray.opacity(0.3))
                    .foregroundColor(.white)
                    .cornerRadius(10)
                    
                    Button("Execute Trade") {
                        // Execute trade logic here
                        dismiss()
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.orange)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }
                .padding()
            }
            .background(Color.black)
            .navigationTitle("Trade \(crypto.symbol)")
            .navigationBarTitleDisplayMode(.inline)
        }
        .preferredColorScheme(.dark)
    }
}

#Preview {
    let agent = CryptoTradingAgent(
        name: "Crypto Agent",
        capabilities: ["Crypto Trading", "Market Analysis", "Risk Management"]
    )
    
    return CryptoTradingView(tradingAgent: agent)
} 