import Foundation

// MARK: - Bybit API Implementation
extension MultiExchangeAPIService {
    
    // MARK: - Market Data
    func fetchBybitMarketData(symbol: String) async throws -> MarketData {
        let baseURL = "https://api.bybit.com"
        let endpoint = "/v5/market/tickers?category=spot&symbol=\(symbol)"
        let url = URL(string: "\(baseURL)\(endpoint)")!
        
        let (data, response) = try await urlSession.data(from: url)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.invalidResponse
        }
        
        let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] ?? [:]
        let result = json["result"] as? [String: Any] ?? [:]
        let list = result["list"] as? [[String: Any]] ?? []
        let ticker = list.first ?? [:]
        
        // Convert Bybit response to MarketData with candlestick data
        let price = Double(ticker["lastPrice"] as? String ?? "0") ?? 0.0
        let volume = Double(ticker["volume24h"] as? String ?? "0") ?? 0.0
        let high = Double(ticker["highPrice24h"] as? String ?? "0") ?? 0.0
        let low = Double(ticker["lowPrice24h"] as? String ?? "0") ?? 0.0
        
        // Bybit doesn't provide open price in ticker, use close as approximation
        let candlestick = Candlestick(
            open: price,
            high: high,
            low: low,
            close: price,
            volume: volume,
            timestamp: Date()
        )
        
        return MarketData(
            symbol: symbol,
            candlesticks: [candlestick],
            timestamp: Date()
        )
    }
    
    // MARK: - Connection Testing
    func testBybitConnection(config: ExchangeConfiguration) async throws -> Bool {
        let baseURL = config.testnet ? "https://api-testnet.bybit.com" : "https://api.bybit.com"
        guard let url = URL(string: "\(baseURL)/v5/account/wallet-balance?accountType=UNIFIED") else {
            throw APIError.invalidURL
        }
        
        let request = try createAuthenticatedRequest(url: url, method: "GET", body: nil, config: config)
        let (data, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.networkError(NSError(domain: "Invalid response", code: -1))
        }
        
        if httpResponse.statusCode == 200 {
            await MainActor.run {
                isConnected[.bybit] = true
            }
            return true
        } else {
            throw APIError.authenticationFailed
        }
    }
    
    // MARK: - Trading Pairs
    func fetchBybitTradingPairs(config: ExchangeConfiguration, mode: TradingMode) async throws -> [TradingPair] {
        let baseURL = config.testnet ? "https://api-testnet.bybit.com" : "https://api.bybit.com"
        
        let category: String
        switch mode {
        case .spot:
            category = "spot"
        case .margin:
            category = "spot" // Bybit handles margin within spot
        case .futures:
            category = "linear"
        case .leverageToken:
            category = "spot" // Leverage tokens are spot instruments on Bybit
        }
        
        guard let url = URL(string: "\(baseURL)/v5/market/instruments-info?category=\(category)") else {
            throw APIError.invalidURL
        }
        
        let request = URLRequest(url: url)
        let (data, _) = try await urlSession.data(for: request)
        
        let decoder = JSONDecoder()
        let response = try decoder.decode(BybitInstrumentsResponse.self, from: data)
        
        return response.result?.list?.compactMap { instrumentData in
            createTradingPairFromBybit(instrumentData, config: config, mode: mode)
        } ?? []
    }
    
    // MARK: - Account Balance
    func fetchBybitBalance(config: ExchangeConfiguration) async throws -> [String: Double] {
        let baseURL = config.testnet ? "https://api-testnet.bybit.com" : "https://api.bybit.com"
        guard let url = URL(string: "\(baseURL)/v5/account/wallet-balance?accountType=UNIFIED") else {
            throw APIError.invalidURL
        }
        
        let request = try createAuthenticatedRequest(url: url, method: "GET", body: nil, config: config)
        let (data, _) = try await urlSession.data(for: request)
        
        let decoder = JSONDecoder()
        let response = try decoder.decode(BybitWalletBalanceResponse.self, from: data)
        
        var balances: [String: Double] = [:]
        response.result?.list?.first?.coin?.forEach { coin in
            if let balance = Double(coin.walletBalance), balance > 0 {
                balances[coin.coin] = balance
            }
        }
        
        return balances
    }
    
    // MARK: - Order Placement
    func placeBybitOrder(
        config: ExchangeConfiguration,
        symbol: String,
        side: OrderSide,
        type: OrderType,
        amount: Double,
        price: Double?,
        tradingMode: TradingMode,
        stopPrice: Double?,
        leverage: Double?
    ) async throws -> RealOrder {
        let baseURL = config.testnet ? "https://api-testnet.bybit.com" : "https://api.bybit.com"
        
        let category: String
        switch tradingMode {
        case .spot, .margin, .leverageToken:
            category = "spot"
        case .futures:
            category = "linear"
        }
        
        var orderData: [String: Any] = [
            "category": category,
            "symbol": symbol,
            "side": side.rawValue.capitalized,
            "orderType": type == .market ? "Market" : "Limit",
            "qty": String(amount)
        ]
        
        if let price = price, type != .market {
            orderData["price"] = String(price)
        }
        
        if let stopPrice = stopPrice {
            orderData["stopPrice"] = String(stopPrice)
            orderData["orderType"] = "Stop"
        }
        
        // Set leverage for futures
        if tradingMode == .futures, let leverage = leverage {
            // First set leverage (separate API call)
            try await setBybitLeverage(config: config, symbol: symbol, leverage: leverage)
        }
        
        guard let url = URL(string: "\(baseURL)/v5/order/create") else {
            throw APIError.invalidURL
        }
        
        let jsonData = try JSONSerialization.data(withJSONObject: orderData)
        let request = try createAuthenticatedRequest(url: url, method: "POST", body: jsonData, config: config)
        
        let (data, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.networkError(NSError(domain: "Invalid response", code: -1))
        }
        
        if httpResponse.statusCode != 200 {
            let errorResponse = try? JSONSerialization.jsonObject(with: data) as? [String: Any]
            let errorMessage = errorResponse?["retMsg"] as? String ?? "Unknown error"
            throw APIError.apiError(errorMessage)
        }
        
        let decoder = JSONDecoder()
        let orderResponse = try decoder.decode(BybitOrderResponse.self, from: data)
        
        guard let orderResult = orderResponse.result else {
            throw APIError.noData
        }
        
        return RealOrder(
            exchange: .bybit,
            exchangeOrderId: orderResult.orderId,
            clientOrderId: orderResult.orderLinkId ?? "",
            symbol: symbol,
            side: side,
            type: type,
            amount: amount,
            price: price,
            stopPrice: stopPrice,
            tradingMode: tradingMode,
            status: .pending,
            filledAmount: 0,
            averagePrice: nil,
            fees: 0,
            feeCurrency: "USDT",
            createdAt: Date(),
            updatedAt: Date(),
            strategy: nil
        )
    }
    
    // MARK: - Set Leverage (Futures)
    private func setBybitLeverage(config: ExchangeConfiguration, symbol: String, leverage: Double) async throws {
        let baseURL = config.testnet ? "https://api-testnet.bybit.com" : "https://api.bybit.com"
        guard let url = URL(string: "\(baseURL)/v5/position/set-leverage") else {
            throw APIError.invalidURL
        }
        
        let leverageData: [String: Any] = [
            "category": "linear",
            "symbol": symbol,
            "buyLeverage": String(Int(leverage)),
            "sellLeverage": String(Int(leverage))
        ]
        
        let jsonData = try JSONSerialization.data(withJSONObject: leverageData)
        let request = try createAuthenticatedRequest(url: url, method: "POST", body: jsonData, config: config)
        
        let (_, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.networkError(NSError(domain: "Invalid response", code: -1))
        }
        
        if httpResponse.statusCode != 200 {
            throw APIError.apiError("Failed to set leverage")
        }
    }
    
    // MARK: - Order Cancellation
    func cancelBybitOrder(config: ExchangeConfiguration, orderId: String, symbol: String) async throws -> Bool {
        let baseURL = config.testnet ? "https://api-testnet.bybit.com" : "https://api.bybit.com"
        guard let url = URL(string: "\(baseURL)/v5/order/cancel") else {
            throw APIError.invalidURL
        }
        
        let cancelData: [String: Any] = [
            "category": "spot", // This would need to be determined based on the original order
            "symbol": symbol,
            "orderId": orderId
        ]
        
        let jsonData = try JSONSerialization.data(withJSONObject: cancelData)
        let request = try createAuthenticatedRequest(url: url, method: "POST", body: jsonData, config: config)
        
        let (_, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.networkError(NSError(domain: "Invalid response", code: -1))
        }
        
        return httpResponse.statusCode == 200
    }
    
    // MARK: - Positions
    func fetchBybitPositions(config: ExchangeConfiguration, mode: TradingMode) async throws -> [RealPosition] {
        guard mode == .futures || mode == .margin else { return [] }
        
        let baseURL = config.testnet ? "https://api-testnet.bybit.com" : "https://api.bybit.com"
        let category = mode == .futures ? "linear" : "spot"
        
        guard let url = URL(string: "\(baseURL)/v5/position/list?category=\(category)") else {
            throw APIError.invalidURL
        }
        
        let request = try createAuthenticatedRequest(url: url, method: "GET", body: nil, config: config)
        let (data, _) = try await urlSession.data(for: request)
        
        let decoder = JSONDecoder()
        let response = try decoder.decode(BybitPositionsResponse.self, from: data)
        
        return response.result?.list?.compactMap { positionData in
            createRealPositionFromBybit(positionData, mode: mode)
        } ?? []
    }
    
    // MARK: - Market Data
    func fetchBybitOrderBook(symbol: String) async throws -> OrderBook {
        guard let url = URL(string: "https://api.bybit.com/v5/market/orderbook?category=spot&symbol=\(symbol)&limit=25") else {
            throw APIError.invalidURL
        }
        
        let request = URLRequest(url: url)
        let (data, _) = try await urlSession.data(for: request)
        
        let decoder = JSONDecoder()
        let response = try decoder.decode(BybitOrderBookResponse.self, from: data)
        
        guard let orderBookData = response.result else {
            throw APIError.noData
        }
        
        return OrderBook(
            bids: orderBookData.b.map { OrderBookEntry(price: $0[0], size: $0[1]) },
            asks: orderBookData.a.map { OrderBookEntry(price: $0[0], size: $0[1]) },
            timestamp: orderBookData.ts
        )
    }
    
    func fetchBybitTicker(symbol: String) async throws -> Ticker {
        guard let url = URL(string: "https://api.bybit.com/v5/market/tickers?category=spot&symbol=\(symbol)") else {
            throw APIError.invalidURL
        }
        
        let request = URLRequest(url: url)
        let (data, _) = try await urlSession.data(for: request)
        
        let decoder = JSONDecoder()
        let response = try decoder.decode(BybitTickerResponse.self, from: data)
        
        guard let tickerData = response.result?.list?.first else {
            throw APIError.noData
        }
        
        return Ticker(
            symbol: symbol,
            price: tickerData.lastPrice,
            priceChange: tickerData.price24hPcnt,
            priceChangePercent: tickerData.price24hPcnt,
            volume: tickerData.volume24h,
            count: nil
        )
    }
    
    // MARK: - Helper Functions
    private func createTradingPairFromBybit(_ data: BybitInstrument, config: ExchangeConfiguration, mode: TradingMode) -> TradingPair? {
        guard data.status == "Trading" else { return nil }
        
        let parts = data.symbol.components(separatedBy: "USDT")
        let baseAsset = parts.first ?? data.symbol
        let quoteAsset = "USDT"
        
        return TradingPair(
            symbol: data.symbol,
            baseAsset: baseAsset,
            quoteAsset: quoteAsset,
            exchange: .bybit,
            tradingMode: mode,
            price: 0, // Would need separate ticker call
            priceChange24h: 0,
            volume24h: 0,
            minQty: Double(data.lotSizeFilter?.minOrderQty ?? "0") ?? 0,
            maxQty: Double(data.lotSizeFilter?.maxOrderQty ?? "1000000") ?? 1000000,
            stepSize: Double(data.lotSizeFilter?.qtyStep ?? "0.001") ?? 0.001,
            tickSize: Double(data.priceFilter?.tickSize ?? "0.01") ?? 0.01,
            isActive: data.status == "Trading"
        )
    }
    
    private func createRealPositionFromBybit(_ data: BybitPosition, mode: TradingMode) -> RealPosition? {
        guard let size = Double(data.size), size > 0 else { return nil }
        
        return RealPosition(
            exchange: .bybit,
            symbol: data.symbol,
            side: data.side.lowercased() == "buy" ? .buy : .sell,
            size: size,
            entryPrice: Double(data.avgPrice) ?? 0,
            markPrice: Double(data.markPrice) ?? 0,
            leverage: Double(data.leverage) ?? 1,
            margin: Double(data.positionIM) ?? 0,
            tradingMode: mode,
            unrealizedPnl: Double(data.unrealisedPnl) ?? 0,
            unrealizedPnlPercentage: Double(data.unrealisedPnl) ?? 0 / max(Double(data.positionValue) ?? 1, 1) * 100,
            createdAt: Date(),
            strategy: nil
        )
    }
}

// MARK: - Bybit Response Models
struct BybitInstrumentsResponse: Codable {
    let retCode: Int
    let retMsg: String
    let result: BybitInstrumentsResult?
}

struct BybitInstrumentsResult: Codable {
    let category: String
    let list: [BybitInstrument]?
}

struct BybitInstrument: Codable {
    let symbol: String
    let contractType: String?
    let status: String
    let baseCoin: String
    let quoteCoin: String
    let launchTime: String?
    let deliveryTime: String?
    let deliveryFeeRate: String?
    let priceScale: String?
    let leverageFilter: BybitLeverageFilter?
    let priceFilter: BybitPriceFilter?
    let lotSizeFilter: BybitLotSizeFilter?
}

struct BybitLeverageFilter: Codable {
    let minLeverage: String
    let maxLeverage: String
    let leverageStep: String
}

struct BybitPriceFilter: Codable {
    let minPrice: String
    let maxPrice: String
    let tickSize: String
}

struct BybitLotSizeFilter: Codable {
    let maxOrderQty: String
    let minOrderQty: String
    let qtyStep: String
    let postOnlyMaxOrderQty: String?
}

struct BybitWalletBalanceResponse: Codable {
    let retCode: Int
    let retMsg: String
    let result: BybitWalletBalanceResult?
}

struct BybitWalletBalanceResult: Codable {
    let list: [BybitWalletBalance]?
}

struct BybitWalletBalance: Codable {
    let totalEquity: String
    let accountIMRate: String
    let totalMarginBalance: String
    let totalInitialMargin: String
    let accountType: String
    let totalAvailableBalance: String
    let accountMMRate: String
    let totalPerpUPL: String
    let totalWalletBalance: String
    let accountLTV: String
    let totalMaintenanceMargin: String
    let coin: [BybitCoinBalance]?
}

struct BybitCoinBalance: Codable {
    let availableToBorrow: String
    let bonus: String
    let accruedInterest: String
    let availableToWithdraw: String
    let totalOrderIM: String
    let equity: String
    let totalPositionMM: String
    let usdValue: String
    let spotHedgingQty: String
    let unrealisedPnl: String
    let collateralSwitch: Bool
    let borrowAmount: String
    let totalPositionIM: String
    let walletBalance: String
    let cumRealisedPnl: String
    let locked: String
    let marginCollateral: Bool
    let coin: String
}

struct BybitOrderResponse: Codable {
    let retCode: Int
    let retMsg: String
    let result: BybitOrderResult?
}

struct BybitOrderResult: Codable {
    let orderId: String
    let orderLinkId: String?
}

struct BybitPositionsResponse: Codable {
    let retCode: Int
    let retMsg: String
    let result: BybitPositionsResult?
}

struct BybitPositionsResult: Codable {
    let list: [BybitPosition]?
    let nextPageCursor: String?
    let category: String
}

struct BybitPosition: Codable {
    let symbol: String
    let leverage: String
    let avgPrice: String
    let liqPrice: String
    let riskLimitValue: String
    let takeProfit: String
    let positionValue: String
    let tpslMode: String
    let riskId: Int
    let trailingStop: String
    let unrealisedPnl: String
    let markPrice: String
    let adlRankIndicator: Int
    let cumRealisedPnl: String
    let positionMM: String
    let createdTime: String
    let positionIdx: Int
    let positionIM: String
    let updatedTime: String
    let side: String
    let bustPrice: String
    let positionBalance: String
    let leverageSysUpdatedTime: String
    let mmrSysUpdatedTime: String
    let seq: Int64
    let isReduceOnly: Bool
    let realizedPnl: String
    let stopLoss: String
    let size: String
}

struct BybitOrderBookResponse: Codable {
    let retCode: Int
    let retMsg: String
    let result: BybitOrderBookResult?
}

struct BybitOrderBookResult: Codable {
    let s: String // symbol
    let a: [[String]] // asks
    let b: [[String]] // bids
    let ts: Int64 // timestamp
    let u: Int64 // update id
}

struct BybitTickerResponse: Codable {
    let retCode: Int
    let retMsg: String
    let result: BybitTickerResult?
}

struct BybitTickerResult: Codable {
    let category: String
    let list: [BybitTickerData]?
}

struct BybitTickerData: Codable {
    let symbol: String
    let lastPrice: String
    let indexPrice: String?
    let markPrice: String?
    let prevPrice24h: String
    let price24hPcnt: String
    let highPrice24h: String
    let lowPrice24h: String
    let prevPrice1h: String
    let openInterest: String?
    let openInterestValue: String?
    let turnover24h: String
    let volume24h: String
    let fundingRate: String?
    let nextFundingTime: String?
    let predictedDeliveryPrice: String?
    let basisRate: String?
    let deliveryFeeRate: String?
    let deliveryTime: String?
    let ask1Size: String
    let bid1Price: String
    let ask1Price: String
    let bid1Size: String
    let basis: String?
} 