import SwiftUI

struct OrderPlacementView: View {
    @ObservedObject var apiService: MultiExchangeAPIService
    let exchange: SupportedExchange
    let tradingMode: TradingMode
    let tradingPairs: [TradingPair]

    @Environment(\.presentationMode) var presentationMode
    @State private var selectedPair: TradingPair?
    @State private var orderSide: OrderSide = .buy
    @State private var orderType: OrderType = .market
    @State private var amount: String = ""
    @State private var price: String = ""
    @State private var isPlacingOrder = false
    @State private var orderResult: String = ""
    @State private var showingResult = false

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                headerView

                ScrollView {
                    VStack(spacing: 20) {
                        tradingPairSelectionView
                        orderConfigurationView

                        if selectedPair != nil {
                            orderSummaryView
                        }
                    }
                    .padding()
                }

                placeOrderButton
            }
            .background(Color.black)
            .navigationBarHidden(true)
        }
        .preferredColorScheme(.dark)
        .onAppear {
            selectedPair = tradingPairs.first
        }
        .alert("Order Result", isPresented: $showingResult) {
            Button("OK") {
                if orderResult.contains("✅") {
                    presentationMode.wrappedValue.dismiss()
                }
            }
        } message: {
            Text(orderResult)
        }
    }

    // MARK: - View Components
    private var headerView: some View {
                HStack {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .foregroundColor(.cyan)

                    Spacer()

                    Text("Place Order")
                        .font(.headline)
                        .foregroundColor(.white)

                    Spacer()

                    Button("Settings") { }
                        .foregroundColor(.cyan)
                }
                .padding()
                .background(Color.gray.opacity(0.1))
    }

    private var tradingPairSelectionView: some View {
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Trading Pair")
                                .font(.headline)
                                .foregroundColor(.cyan)

                            Picker("Select Pair", selection: $selectedPair) {
                                ForEach(tradingPairs.prefix(10), id: \.id) { pair in
                                    Text(pair.displaySymbol).tag(pair as TradingPair?)
                                }
                            }
                            .pickerStyle(MenuPickerStyle())
                        }
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(12)
    }

    private var orderConfigurationView: some View {
                        VStack(alignment: .leading, spacing: 16) {
                            Text("Order Configuration")
                                .font(.headline)
                                .foregroundColor(.cyan)

            orderSideButtons

            Picker("Order Type", selection: $orderType) {
                Text("Market").tag(OrderType.market)
                Text("Limit").tag(OrderType.limit)
            }
            .pickerStyle(SegmentedPickerStyle())

            amountInputView

            if orderType == .limit {
                priceInputView
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }

    private var orderSideButtons: some View {
                            HStack(spacing: 12) {
                                Button("BUY") {
                                    orderSide = .buy
                                }
                                .foregroundColor(orderSide == .buy ? .white : .green)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(orderSide == .buy ? Color.green : Color.clear)
                                .overlay(RoundedRectangle(cornerRadius: 8).stroke(Color.green, lineWidth: 1))
                                .cornerRadius(8)

                                Button("SELL") {
                                    orderSide = .sell
                                }
                                .foregroundColor(orderSide == .sell ? .white : .red)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(orderSide == .sell ? Color.red : Color.clear)
                                .overlay(RoundedRectangle(cornerRadius: 8).stroke(Color.red, lineWidth: 1))
                                .cornerRadius(8)
                            }
                            }

    private var amountInputView: some View {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("Amount")
                                    .foregroundColor(.white)

                                TextField("Enter amount", text: $amount)
                                    .padding(12)
                                    .background(Color.black.opacity(0.3))
                                    .cornerRadius(8)
                                    .foregroundColor(.white)
                                    .keyboardType(.decimalPad)
        }
                            }

    private var priceInputView: some View {
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("Price")
                                        .foregroundColor(.white)

                                    TextField("Enter price", text: $price)
                                        .padding(12)
                                        .background(Color.black.opacity(0.3))
                                        .cornerRadius(8)
                                        .foregroundColor(.white)
                                        .keyboardType(.decimalPad)
                                }
                            }

    private var orderSummaryView: some View {
                            VStack(alignment: .leading, spacing: 12) {
                                Text("Order Summary")
                                    .font(.headline)
                                    .foregroundColor(.cyan)

                                VStack(spacing: 8) {
                summaryRow("Exchange:", exchange.rawValue, .white)
                summaryRow("Symbol:", selectedPair?.displaySymbol ?? "", .white)
                summaryRow("Side:", orderSide.displayName, orderSide == .buy ? .green : .red)
                summaryRow("Amount:", "\(amount) \(selectedPair?.baseAsset ?? "")", .white)

                                    if orderType == .limit {
                    summaryRow("Price:", "$\(price)", .white)
                                    }
                                }
                            }
                            .padding()
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(12)
                        }

    private func summaryRow(_ label: String, _ value: String, _ valueColor: Color) -> some View {
        HStack {
            Text(label)
                .foregroundColor(.gray)
            Spacer()
            Text(value)
                .foregroundColor(valueColor)
        }
    }

    private var placeOrderButton: some View {
                Button(action: placeOrder) {
                    HStack {
                        if isPlacingOrder {
                            ProgressView()
                                .scaleEffect(0.8)
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        } else {
                            Image(systemName: orderSide == .buy ? "arrow.up.circle.fill" : "arrow.down.circle.fill")
                        }

                        Text(isPlacingOrder ? "Placing Order..." : "Place \(orderSide.displayName) Order")
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        LinearGradient(
                            colors: orderSide == .buy ? [.green, .green.opacity(0.8)] : [.red, .red.opacity(0.8)],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(12)
                }
                .disabled(isPlacingOrder || amount.isEmpty || selectedPair == nil)
                .padding()
    }

    private func placeOrder() {
        guard let pair = selectedPair else { return }
        guard let amountValue = Double(amount) else { return }

        isPlacingOrder = true

        let priceValue = orderType == .market ? nil : Double(price)

        Task {
            do {
                let order = try await apiService.placeOrder(
                    exchange: exchange,
                    symbol: pair.symbol,
                    side: orderSide,
                    type: orderType,
                    amount: amountValue,
                    price: priceValue,
                    tradingMode: tradingMode,
                    stopPrice: nil,
                    leverage: nil
                )

                await MainActor.run {
                    isPlacingOrder = false
                    orderResult = "✅ Order placed successfully!\nOrder ID: \(order.exchangeOrderId)"
                    showingResult = true
                }

            } catch {
                await MainActor.run {
                    isPlacingOrder = false
                    orderResult = "❌ Failed to place order:\n\(error.localizedDescription)"
                    showingResult = true
                }
            }
        }
    }
}
