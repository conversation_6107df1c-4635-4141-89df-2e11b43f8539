import Foundation
import CryptoKit

// MARK: - Market Data Models
struct MarketData {
    let symbol: String
    let candlesticks: [Candlestick]
    let timestamp: Date
}

struct Candlestick {
    let open: Double
    let high: Double
    let low: Double
    let close: Double
    let volume: Double
    let timestamp: Date
}

enum APIError: Error {
    case invalidURL
    case networkError(Error)
    case authenticationFailed
    case apiError(String)
    case noData
    case configurationNotFound
    case missingCredentials
    case invalidResponse
    case tradingError(String)
    
    var localizedDescription: String {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .authenticationFailed:
            return "Authentication failed"
        case .apiError(let message):
            return "API error: \(message)"
        case .noData:
            return "No data received"
        case .configurationNotFound:
            return "Exchange configuration not found"
        case .missingCredentials:
            return "Missing API credentials"
        case .invalidResponse:
            return "Invalid response from server"
        case .tradingError(let message):
            return "Trading error: \(message)"
        }
    }
}

class MultiExchangeAPIService: ObservableObject {
    @Published var exchangeConfigurations: [ExchangeConfiguration] = []
    @Published var isConnected: [SupportedExchange: Bool] = [:]
    @Published var lastError: String? = nil
    
    internal let urlSession = URLSession.shared
    
    init() {
        setupDefaultConfigurations()
    }
    
    private func setupDefaultConfigurations() {
        exchangeConfigurations = SupportedExchange.allCases.map { exchange in
            ExchangeConfiguration(exchange: exchange)
        }
        
        SupportedExchange.allCases.forEach { exchange in
            isConnected[exchange] = false
        }
    }
    
    // MARK: - Configuration Management
    func updateExchangeConfiguration(_ config: ExchangeConfiguration) {
        if let index = exchangeConfigurations.firstIndex(where: { $0.exchange == config.exchange }) {
            exchangeConfigurations[index] = config
        } else {
            exchangeConfigurations.append(config)
        }
    }
    
    func getConfiguration(for exchange: SupportedExchange) -> ExchangeConfiguration? {
        return exchangeConfigurations.first { $0.exchange == exchange }
    }
    
    // MARK: - Connection Testing
    func testConnection(for exchange: SupportedExchange) async -> Bool {
        guard let config = getConfiguration(for: exchange) else { return false }
        
        do {
            switch exchange {
            case .binance:
                return try await testBinanceConnection(config: config)
            case .kucoin:
                return try await testKuCoinConnection(config: config)
            case .bybit:
                return try await testBybitConnection(config: config)
            case .mexc:
                return try await testMEXCConnection(config: config)
            }
        } catch {
            await MainActor.run {
                lastError = error.localizedDescription
            }
            return false
        }
    }
    
    // MARK: - Trading Pairs
    func fetchTradingPairs(for exchange: SupportedExchange, mode: TradingMode) async -> [TradingPair] {
        guard let config = getConfiguration(for: exchange) else { return [] }
        
        do {
            switch exchange {
            case .binance:
                return try await fetchBinanceTradingPairs(config: config, mode: mode)
            case .kucoin:
                return try await fetchKuCoinTradingPairs(config: config, mode: mode)
            case .bybit:
                return try await fetchBybitTradingPairs(config: config, mode: mode)
            case .mexc:
                return try await fetchMEXCTradingPairs(config: config, mode: mode)
            }
        } catch {
            await MainActor.run {
                lastError = error.localizedDescription
            }
            return generateMockTradingPairs(exchange: exchange, mode: mode)
        }
    }
    
    // MARK: - Order Placement
    func placeOrder(
        exchange: SupportedExchange,
        symbol: String,
        side: OrderSide,
        type: OrderType,
        amount: Double,
        price: Double?,
        tradingMode: TradingMode,
        stopPrice: Double?,
        leverage: Double?
    ) async throws -> RealOrder {
        
        guard let config = getConfiguration(for: exchange) else {
            throw APIError.configurationNotFound
        }
        
        switch exchange {
        case .binance:
            return try await placeBinanceOrder(
                config: config,
                symbol: symbol,
                side: side,
                type: type,
                amount: amount,
                price: price,
                tradingMode: tradingMode,
                stopPrice: stopPrice,
                leverage: leverage
            )
        case .kucoin:
            return try await placeKuCoinOrder(
                config: config,
                symbol: symbol,
                side: side,
                type: type,
                amount: amount,
                price: price,
                tradingMode: tradingMode,
                stopPrice: stopPrice,
                leverage: leverage
            )
        case .bybit:
            return try await placeBybitOrder(
                config: config,
                symbol: symbol,
                side: side,
                type: type,
                amount: amount,
                price: price,
                tradingMode: tradingMode,
                stopPrice: stopPrice,
                leverage: leverage
            )
        case .mexc:
            return try await placeMEXCOrder(
                config: config,
                symbol: symbol,
                side: side,
                type: type,
                amount: amount,
                price: price,
                tradingMode: tradingMode,
                stopPrice: stopPrice,
                leverage: leverage
            )
        }
    }
    
    // MARK: - Mock Data Generation
    internal func generateMockTradingPairs(exchange: SupportedExchange, mode: TradingMode) -> [TradingPair] {
        let mockSymbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "DOTUSDT", "LINKUSDT", "SOLUSDT", "AVAXUSDT", "MATICUSDT"]
        
        return mockSymbols.map { symbol in
            let baseAsset = String(symbol.dropLast(4))
            let quoteAsset = "USDT"
            let price = Double.random(in: 0.1...50000)
            
            return TradingPair(
                symbol: symbol,
                baseAsset: baseAsset,
                quoteAsset: quoteAsset,
                exchange: exchange,
                tradingMode: mode,
                price: price,
                priceChange24h: Double.random(in: -10...10),
                volume24h: Double.random(in: 1000...100000),
                minQty: 0.001,
                maxQty: 1000000,
                stepSize: 0.001,
                tickSize: 0.01,
                isActive: true
            )
        }
    }
    

    

    
    // MARK: - Account Information
    func fetchAccountBalance(for exchange: SupportedExchange) async -> [String: Double] {
        guard let config = getConfiguration(for: exchange), config.isEnabled else { return [:] }
        
        do {
            switch exchange {
            case .binance:
                return try await fetchBinanceBalance(config: config)
            case .kucoin:
                return try await fetchKuCoinBalance(config: config)
            case .bybit:
                return try await fetchBybitBalance(config: config)
            case .mexc:
                return try await fetchMEXCBalance(config: config)
            }
        } catch {
            await MainActor.run {
                lastError = "Failed to fetch balance for \(exchange.rawValue): \(error.localizedDescription)"
            }
            return [:]
        }
    }
    
    // MARK: - Order Management
    
    func cancelOrder(exchange: SupportedExchange, orderId: String, symbol: String) async -> Bool {
        guard let config = getConfiguration(for: exchange), config.isEnabled else { return false }
        
        do {
            switch exchange {
            case .binance:
                return try await cancelBinanceOrder(config: config, orderId: orderId, symbol: symbol)
            case .kucoin:
                return try await cancelKuCoinOrder(config: config, orderId: orderId, symbol: symbol)
            case .bybit:
                return try await cancelBybitOrder(config: config, orderId: orderId, symbol: symbol)
            case .mexc:
                return try await cancelMEXCOrder(config: config, orderId: orderId, symbol: symbol)
            }
        } catch {
            await MainActor.run {
                lastError = "Failed to cancel order on \(exchange.rawValue): \(error.localizedDescription)"
            }
            return false
        }
    }
    
    // MARK: - Position Management
    func fetchPositions(for exchange: SupportedExchange, mode: TradingMode) async -> [RealPosition] {
        guard let config = getConfiguration(for: exchange), config.isEnabled else { return [] }
        
        do {
            switch exchange {
            case .binance:
                return try await fetchBinancePositions(config: config, mode: mode)
            case .kucoin:
                return try await fetchKuCoinPositions(config: config, mode: mode)
            case .bybit:
                return try await fetchBybitPositions(config: config, mode: mode)
            case .mexc:
                return try await fetchMEXCPositions(config: config, mode: mode)
            }
        } catch {
            await MainActor.run {
                lastError = "Failed to fetch positions for \(exchange.rawValue): \(error.localizedDescription)"
            }
            return []
        }
    }
    
    // MARK: - Market Data
    func fetchOrderBook(exchange: SupportedExchange, symbol: String) async -> OrderBook? {
        do {
            switch exchange {
            case .binance:
                return try await fetchBinanceOrderBook(symbol: symbol)
            case .kucoin:
                return try await fetchKuCoinOrderBook(symbol: symbol)
            case .bybit:
                return try await fetchBybitOrderBook(symbol: symbol)
            case .mexc:
                return try await fetchMEXCOrderBook(symbol: symbol)
            }
        } catch {
            await MainActor.run {
                lastError = "Failed to fetch order book for \(symbol) on \(exchange.rawValue): \(error.localizedDescription)"
            }
            return nil
        }
    }
    
    func fetchTicker(exchange: SupportedExchange, symbol: String) async -> Ticker? {
        do {
            switch exchange {
            case .binance:
                return try await fetchBinanceTicker(symbol: symbol)
            case .kucoin:
                return try await fetchKuCoinTicker(symbol: symbol)
            case .bybit:
                return try await fetchBybitTicker(symbol: symbol)
            case .mexc:
                return try await fetchMEXCTicker(symbol: symbol)
            }
        } catch {
            await MainActor.run {
                lastError = "Failed to fetch ticker for \(symbol) on \(exchange.rawValue): \(error.localizedDescription)"
            }
            return nil
        }
    }
    
    // MARK: - Market Data for AI Analysis
    func getMarketData(symbol: String, exchange: SupportedExchange) async -> MarketData? {
        do {
            switch exchange {
            case .binance:
                return try await fetchBinanceMarketData(symbol: symbol)
            case .kucoin:
                return try await fetchKuCoinMarketData(symbol: symbol)
            case .bybit:
                return try await fetchBybitMarketData(symbol: symbol)
            case .mexc:
                return try await fetchMEXCMarketData(symbol: symbol)
            }
        } catch {
            await MainActor.run {
                lastError = "Failed to fetch market data for \(symbol) on \(exchange.rawValue): \(error.localizedDescription)"
            }
            return nil
        }
    }
    
    func getOrderBook(symbol: String, exchange: SupportedExchange) async -> OrderBook? {
        return await fetchOrderBook(exchange: exchange, symbol: symbol)
    }
}

// MARK: - Utility Functions
extension MultiExchangeAPIService {
    internal func createAuthenticatedRequest(
        url: URL,
        method: String,
        body: Data?,
        config: ExchangeConfiguration
    ) throws -> URLRequest {
        var request = URLRequest(url: url)
        request.httpMethod = method
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        if let body = body {
            request.httpBody = body
        }
        
        let timestamp = String(Int64(Date().timeIntervalSince1970 * 1000))
        
        switch config.exchange {
        case .binance:
            try addBinanceAuthentication(to: &request, config: config, timestamp: timestamp, body: body)
        case .kucoin:
            try addKuCoinAuthentication(to: &request, config: config, timestamp: timestamp, body: body)
        case .bybit:
            try addBybitAuthentication(to: &request, config: config, timestamp: timestamp, body: body)
        case .mexc:
            try addMEXCAuthentication(to: &request, config: config, timestamp: timestamp, body: body)
        }
        
        return request
    }
    
    private func addKuCoinAuthentication(
        to request: inout URLRequest,
        config: ExchangeConfiguration,
        timestamp: String,
        body: Data?
    ) throws {
        let method = request.httpMethod ?? "GET"
        let path = request.url?.path ?? ""
        let query = request.url?.query ?? ""
        let bodyString = body != nil ? String(data: body!, encoding: .utf8) ?? "" : ""
        
        let stringToSign = timestamp + method + path + (query.isEmpty ? "" : "?\(query)") + bodyString
        let signature = try hmacSHA256(key: config.apiSecret, message: stringToSign)
        
        request.setValue(config.apiKey, forHTTPHeaderField: "KC-API-KEY")
        request.setValue(signature, forHTTPHeaderField: "KC-API-SIGN")
        request.setValue(timestamp, forHTTPHeaderField: "KC-API-TIMESTAMP")
        request.setValue(config.passphrase ?? "", forHTTPHeaderField: "KC-API-PASSPHRASE")
        request.setValue("2", forHTTPHeaderField: "KC-API-KEY-VERSION")
    }
    
    private func addBybitAuthentication(
        to request: inout URLRequest,
        config: ExchangeConfiguration,
        timestamp: String,
        body: Data?
    ) throws {
        let bodyString = body != nil ? String(data: body!, encoding: .utf8) ?? "" : ""
        let recvWindow = "5000"
        
        let paramString = timestamp + config.apiKey + recvWindow + bodyString
        let signature = try hmacSHA256(key: config.apiSecret, message: paramString)
        
        request.setValue(config.apiKey, forHTTPHeaderField: "X-BAPI-API-KEY")
        request.setValue(signature, forHTTPHeaderField: "X-BAPI-SIGN")
        request.setValue(timestamp, forHTTPHeaderField: "X-BAPI-TIMESTAMP")
        request.setValue(recvWindow, forHTTPHeaderField: "X-BAPI-RECV-WINDOW")
    }
    
    private func addMEXCAuthentication(
        to request: inout URLRequest,
        config: ExchangeConfiguration,
        timestamp: String,
        body: Data?
    ) throws {
        var queryItems: [URLQueryItem] = []
        
        if let url = request.url, let components = URLComponents(url: url, resolvingAgainstBaseURL: false) {
            queryItems = components.queryItems ?? []
        }
        
        queryItems.append(URLQueryItem(name: "timestamp", value: timestamp))
        
        let queryString = queryItems
            .sorted { $0.name < $1.name }
            .map { "\($0.name)=\($0.value ?? "")" }
            .joined(separator: "&")
        
        let signature = try hmacSHA256(key: config.apiSecret, message: queryString)
        queryItems.append(URLQueryItem(name: "signature", value: signature))
        
        if let baseURL = request.url?.absoluteString.components(separatedBy: "?").first {
            let newQueryString = queryItems.map { "\($0.name)=\($0.value ?? "")" }.joined(separator: "&")
            request.url = URL(string: "\(baseURL)?\(newQueryString)")
        }
        
        request.setValue(config.apiKey, forHTTPHeaderField: "X-MEXC-APIKEY")
    }
    
    internal func hmacSHA256(key: String, message: String) throws -> String {
        let keyData = Data(key.utf8)
        let messageData = Data(message.utf8)
        let signature = HMAC<SHA256>.authenticationCode(for: messageData, using: SymmetricKey(data: keyData))
        return Data(signature).map { String(format: "%02hhx", $0) }.joined()
    }
}

 