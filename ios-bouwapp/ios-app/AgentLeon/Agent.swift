import Foundation
import SwiftUI

// MARK: - Agent Status
enum AgentStatus: String, CaseIterable {
    case active = "ACTIVE"
    case thinking = "THINKING"
    case error = "ERROR"
    case idle = "IDLE"
    
    var color: Color {
        switch self {
        case .active:
            return .orange
        case .thinking:
            return .blue
        case .error:
            return .red
        case .idle:
            return .gray
        }
    }
}

// MARK: - Agent Types
enum AgentType: String, CaseIterable {
    case dataAnalyst = "Data Analyst"
    case cryptoTrader = "Crypto Trader"
    case nlpProcessor = "NLP Processor"
    case visionProcessor = "Vision Processor"
    case riskManager = "Risk Manager"
    case arbitrageHunter = "Arbitrage Hunter"
    case sentimentAnalyst = "Sentiment Analyst"
    case strategyOptimizer = "Strategy Optimizer"
    case complianceOfficer = "Compliance Officer"
    case portfolioManager = "Portfolio Manager"
    
    var icon: String {
        switch self {
        case .dataAnalyst: return "chart.bar.xaxis"
        case .cryptoTrader: return "bitcoinsign.circle.fill"
        case .nlpProcessor: return "text.bubble"
        case .visionProcessor: return "eye.fill"
        case .riskManager: return "shield.fill"
        case .arbitrageHunter: return "arrow.triangle.2.circlepath"
        case .sentimentAnalyst: return "face.smiling"
        case .strategyOptimizer: return "gear"
        case .complianceOfficer: return "checkmark.shield.fill"
        case .portfolioManager: return "briefcase.fill"
        }
    }
}

// MARK: - Agent Rules
struct AgentRule {
    let id = UUID()
    let name: String
    let description: String
    let priority: Int
    let condition: String
    let action: String
    var isActive: Bool
    
    static let defaultRules = [
        AgentRule(name: "Data Analysis Priority", 
                 description: "Prioriteer data analyse taken boven andere activiteiten", 
                 priority: 1, 
                 condition: "task.type == 'data_analysis'", 
                 action: "execute_immediately", 
                 isActive: true),
        
        AgentRule(name: "Error Recovery", 
                 description: "Bij errors, probeer automatisch te herstellen", 
                 priority: 2, 
                 condition: "status == 'error'", 
                 action: "attempt_recovery", 
                 isActive: true),
        
        AgentRule(name: "Performance Monitoring", 
                 description: "Monitor performance en pas snelheid aan", 
                 priority: 3, 
                 condition: "performance < 50", 
                 action: "reduce_load", 
                 isActive: true),
        
        AgentRule(name: "Collaboration Protocol", 
                 description: "Deel informatie met andere agents bij complexe taken", 
                 priority: 4, 
                 condition: "task.complexity > 'medium'", 
                 action: "share_with_network", 
                 isActive: true)
    ]
}

// MARK: - Agent Task
struct AgentTask {
    let id = UUID()
    let name: String
    let description: String
    let type: String
    let complexity: String
    let estimatedDuration: TimeInterval
    var progress: Double
    var isCompleted: Bool
    
    static let sampleTasks = [
        AgentTask(name: "Analyzing data", 
                 description: "Verwerken van grote datasets", 
                 type: "data_analysis", 
                 complexity: "high", 
                 estimatedDuration: 300, 
                 progress: 0.75, 
                 isCompleted: false),
        
        AgentTask(name: "Pattern recognition", 
                 description: "Zoeken naar patronen in data", 
                 type: "pattern_analysis", 
                 complexity: "medium", 
                 estimatedDuration: 180, 
                 progress: 0.45, 
                 isCompleted: false)
    ]
}

// MARK: - Agent Model
class Agent: ObservableObject, Identifiable {
    let id = UUID()
    @Published var name: String
    @Published var status: AgentStatus
    @Published var currentTask: AgentTask?
    @Published var performance: Double
    @Published var rules: [AgentRule]
    @Published var isOnline: Bool
    @Published var lastActivity: Date
    
    // Agent Capabilities
    let capabilities: [String]
    let version: String
    
    init(name: String, 
         status: AgentStatus = .idle, 
         performance: Double = 100.0, 
         capabilities: [String] = [], 
         version: String = "1.0.0") {
        self.name = name
        self.status = status
        self.performance = performance
        self.rules = AgentRule.defaultRules
        self.isOnline = true
        self.lastActivity = Date()
        self.capabilities = capabilities
        self.version = version
    }
    
    // MARK: - Agent Methods
    func executeRule(_ rule: AgentRule) {
        print("Agent \(name) executing rule: \(rule.name)")
        // Hier zou je de actual rule logic implementeren
        lastActivity = Date()
    }
    
    func updateStatus(to newStatus: AgentStatus) {
        status = newStatus
        lastActivity = Date()
    }
    
    func assignTask(_ task: AgentTask) {
        currentTask = task
        updateStatus(to: .active)
    }
    
    func completeCurrentTask() {
        currentTask?.isCompleted = true
        currentTask = nil
        updateStatus(to: .idle)
    }
    
    func updatePerformance() {
        // Simulate performance fluctuation
        let random = Double.random(in: -5...5)
        performance = max(0, min(100, performance + random))
        
        // Apply rules based on performance
        if performance < 50 {
            if let rule = rules.first(where: { $0.name == "Performance Monitoring" && $0.isActive }) {
                executeRule(rule)
            }
        }
    }
    
    func handleError() {
        updateStatus(to: .error)
        performance = max(0, performance - 20)
        
        if let rule = rules.first(where: { $0.name == "Error Recovery" && $0.isActive }) {
            executeRule(rule)
            // Simulate recovery
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                self.updateStatus(to: .idle)
                self.performance = min(100, self.performance + 10)
            }
        }
    }
    
    func processCommand(_ command: String) {
        print("Agent \(name) processing command: \(command)")
        updateStatus(to: .thinking)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            // Simulate command processing
            if command.lowercased().contains("error") {
                self.handleError()
            } else if command.lowercased().contains("analyze") {
                let task = AgentTask.sampleTasks.first!
                self.assignTask(task)
            } else {
                self.updateStatus(to: .idle)
            }
        }
    }
} 