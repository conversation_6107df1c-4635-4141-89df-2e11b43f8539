# 🍎 Agent Leon - App Store Deployment Guide

## ✅ VEREISTEN VOOR APP STORE

### 1. Apple Developer Account
- **<PERSON><PERSON>**: $99/jaar
- **<PERSON><PERSON><PERSON>den**: https://developer.apple.com/programs/
- **Voordelen**: 
  - App Store distributie
  - TestFlight beta testing
  - Push notifications
  - Advanced features

### 2. App Store Connect Setup
- **URL**: https://appstoreconnect.apple.com/
- **Benodigde info**:
  - App naam: "Agent Leon - AI Trading Bot"
  - Bundle ID: com.agentleon.trading
  - Beschrijving: "AI-powered cryptocurrency trading assistant"
  - Screenshots (verschillende iPhone formaten)
  - App icoon (1024x1024px)

### 3. Code Signing & Provisioning
```bash
# Update Bundle Identifier
sed -i '' 's/com.example.AgentLeon/com.agentleon.trading/g' AgentLeon.xcodeproj/project.pbxproj

# Set Team ID in Xcode
# Signing & Capabilities → Team: [Your Developer Team]
```

### 4. App Store Review Checklist
- ✅ API keys niet hardcoded (gebruik Keychain)
- ✅ Crypto trading disclaimer
- ✅ Privacy policy voor data usage
- ✅ Terms of service
- ✅ Geen developer-only features
- ✅ Stable performance op alle devices

### 5. Deployment Commands
```bash
# Archive for App Store
xcodebuild -workspace AgentLeon.xcworkspace \
  -scheme AgentLeon \
  -archivePath ./AgentLeon.xcarchive \
  archive

# Upload to App Store Connect
xcodebuild -exportArchive \
  -archivePath ./AgentLeon.xcarchive \
  -exportPath ./AppStore \
  -exportOptionsPlist ExportOptions.plist
```

## 📋 APP STORE METADATA

### App Beschrijving
"Agent Leon is een geavanceerde AI-gestuurde cryptocurrency trading assistent die gebruikmaakt van machine learning en sentiment analyse om intelligente handelsbeslissingen te nemen. Met ondersteuning voor meerdere exchanges (KuCoin, MEXC, Bybit) en real-time marktanalyse."

### Keywords
cryptocurrency, trading, AI, bitcoin, ethereum, portfolio, exchange, automation

### App Categorie
Finance

### Content Rating
17+ (Financial content, simulated gambling)

## 🚀 UPLOAD PROCESS

1. **Archive in Xcode**
   - Product → Archive
   - Distribute App → App Store Connect
   
2. **App Store Connect**
   - Upload screenshots
   - Set pricing (Free/Paid)
   - Submit for review
   
3. **Review Process**
   - 24-72 hours typical
   - Address any rejections
   - Auto-publish when approved

## ⚠️ COMPLIANCE NOTES

### Financial App Requirements
- Disclaimer over investment risks
- Clear terms over API usage
- Privacy policy for data collection
- Proper error handling voor API failures

### Crypto Trading Specific
- Duidelijke waarschuwing: "Trading cryptocurrency carries risk"
- Geen garanties over profits
- Educational content over trading risks
- Proper data encryption voor API keys 