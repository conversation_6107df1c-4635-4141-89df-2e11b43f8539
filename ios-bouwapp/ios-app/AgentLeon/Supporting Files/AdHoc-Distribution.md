# 📲 Agent Leon - Ad Hoc Distribution Guide

## 🎯 AD HOC DISTRIBUTIE (DIRECTE INSTALLATIE)

### Wanneer gebruiken?
- ✅ **Directe distributie** naar specifieke devices
- ✅ **Geen App Store review** proces
- ✅ **Tot 100 devices** per jaar
- ✅ **Volledige controle** over distributie
- ✅ **Enterprise/internal** gebruik
- ✅ **Bypassed** App Store beperkingen

### Vereisten
- Apple Developer Account ($99/jaar)
- Device UDIDs van alle target devices
- Ad Hoc provisioning profile
- Distribution certificate

## 🔧 SETUP PROCES

### Stap 1: Device UDIDs verzamelen
```bash
# Via Xcode
# Window → Devices and Simulators → Select device → Copy UDID

# Via iOS Settings op target device
# Settings → General → About → Copy "Identifier"

# Via Terminal (connected device)
system_profiler SPUSBDataType | grep "Serial Number" | awk '{print $3}'
```

### Stap 2: Provisioning Profile aanmaken
```bash
# In Apple Developer Portal
# Certificates, Identifiers & Profiles → Profiles → +

# Selecteer: Ad Hoc
# App ID: com.agentleon.trading
# Certificate: Distribution Certificate
# Devices: Add alle target device UDIDs
```

### Stap 3: Build configuratie
```bash
# Update project voor Ad Hoc
# In Xcode: Signing & Capabilities
# Provisioning Profile: AdHoc_AgentLeon
# Code Signing Identity: Distribution
```

### Stap 4: Archive & Export
```bash
# Archive the app
xcodebuild -project AgentLeon.xcodeproj \
  -scheme AgentLeon \
  -configuration Release \
  -destination "generic/platform=iOS" \
  -archivePath ./AgentLeon_AdHoc.xcarchive \
  clean archive

# Export IPA for Ad Hoc
xcodebuild -exportArchive \
  -archivePath ./AgentLeon_AdHoc.xcarchive \
  -exportPath ./AdHoc_Distribution \
  -exportOptionsPlist AdHocExportOptions.plist
```

### Stap 5: Distributie methoden

#### A. Via AirDrop
```bash
# Gewoon AirDrop de IPA file naar target device
# Device zal automatisch installatie prompts tonen
```

#### B. Via Website Download
```html
<!-- Create een simple download page -->
<!DOCTYPE html>
<html>
<head>
    <title>Agent Leon - Download</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body>
    <h1>🤖 Agent Leon AI Trading</h1>
    <p>Download de nieuwste versie:</p>
    
    <a href="itms-services://?action=download-manifest&url=https://your-domain.com/manifest.plist">
        <button>📱 Install Agent Leon</button>
    </a>
    
    <p><small>⚠️ Je device moet geregistreerd zijn voor installatie.</small></p>
</body>
</html>
```

#### C. Via Manifest (OTA Install)
```xml
<!-- manifest.plist for Over-The-Air installation -->
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>items</key>
    <array>
        <dict>
            <key>assets</key>
            <array>
                <dict>
                    <key>kind</key>
                    <string>software-package</string>
                    <key>url</key>
                    <string>https://your-domain.com/AgentLeon.ipa</string>
                </dict>
                <dict>
                    <key>kind</key>
                    <string>display-image</string>
                    <key>url</key>
                    <string>https://your-domain.com/icon57x57.png</string>
                </dict>
                <dict>
                    <key>kind</key>
                    <string>full-size-image</string>
                    <key>url</key>
                    <string>https://your-domain.com/icon512x512.png</string>
                </dict>
            </array>
            <key>metadata</key>
            <dict>
                <key>bundle-identifier</key>
                <string>com.agentleon.trading</string>
                <key>bundle-version</key>
                <string>1.0.0</string>
                <key>kind</key>
                <string>software</string>
                <key>title</key>
                <string>Agent Leon AI Trading</string>
            </dict>
        </dict>
    </array>
</dict>
</plist>
```

## 🌐 HOSTING OPTIONS

### Option 1: GitHub Pages (Gratis)
```bash
# Create distributie repository
git init agent-leon-distribution
cd agent-leon-distribution

# Add files
cp ../AgentLeon_AdHoc.ipa ./AgentLeon.ipa
cp ../manifest.plist ./
cp ../index.html ./

# Push to GitHub
git add .
git commit -m "Agent Leon Ad Hoc Distribution"
git push origin main

# Enable GitHub Pages
# Repository Settings → Pages → Source: main branch
# Access via: https://username.github.io/agent-leon-distribution/
```

### Option 2: Firebase Hosting
```bash
# Install Firebase CLI
npm install -g firebase-tools

# Initialize Firebase project
firebase init hosting

# Deploy
firebase deploy

# Custom domain mogelijk
```

### Option 3: AWS S3 + CloudFront
```bash
# Create S3 bucket
aws s3 mb s3://agent-leon-distribution

# Upload files
aws s3 sync ./AdHoc_Distribution s3://agent-leon-distribution/

# Enable static website hosting
aws s3 website s3://agent-leon-distribution --index-document index.html
```

## 📱 INSTALLATIE INSTRUCTIES VOOR GEBRUIKERS

### Stap 1: Device toevoegen
```
1. Ga naar: https://your-distribution-site.com/register
2. Voer je device UDID in
3. Wacht op bevestiging (24-48 uur)
```

### Stap 2: Download & Install
```
1. Open Safari op je iPhone/iPad
2. Ga naar: https://your-distribution-site.com/
3. Klik "Install Agent Leon"
4. Bevestig installatie in Settings
5. Trust developer certificate:
   Settings → General → VPN & Device Management → Trust
```

### Stap 3: Eerste configuratie
```
1. Open Agent Leon app
2. Ga naar AI Configuration (🧠 icoon)
3. Klik "Load Defaults" voor test API keys
4. Test de trading interface
5. Setup Telegram notifications
```

## 🔄 UPDATE PROCES

### Automatic Update Check
```swift
// In-app update checker
struct UpdateChecker: ObservableObject {
    @Published var updateAvailable = false
    @Published var downloadURL = ""
    
    func checkForUpdates() {
        // Check remote version.json
        // Compare with local version
        // Show update prompt if newer available
    }
}
```

### Update Distribution
```bash
# Create new build
# Increment build number
# Upload to distribution site
# Send push notification voor updates
```

## 🚨 TROUBLESHOOTING

### Common Issues
1. **"Untrusted Developer"**
   - Settings → General → VPN & Device Management → Trust

2. **"App cannot be verified"**
   - Check device UDID is registered
   - Ensure provisioning profile includes device

3. **Install failed**
   - Clear Safari cache
   - Reboot device
   - Try download again

### Debug Mode
```swift
#if ADHOC_BUILD
// Add debug info voor troubleshooting
struct DebugInfo: View {
    var body: some View {
        VStack {
            Text("🔧 Debug Info")
            Text("Build: \(Bundle.main.buildNumber)")
            Text("Version: \(Bundle.main.version)")
            Text("Device: \(UIDevice.current.name)")
            Text("UDID: \(UIDevice.current.identifierForVendor?.uuidString ?? "Unknown")")
        }
    }
}
#endif
``` 