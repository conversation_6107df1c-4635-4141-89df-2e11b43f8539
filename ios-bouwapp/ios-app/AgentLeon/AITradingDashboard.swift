import SwiftUI

struct AITradingDashboard: View {
    @StateObject private var aiEngine: AIAnalysisEngine
    @StateObject private var tradeManager: AITradeManager
    @State private var selectedTab = 0
    @State private var showSettings = false
    
    init(apiService: MultiExchangeAPIService) {
        self._aiEngine = StateObject(wrappedValue: AIAnalysisEngine(apiService: apiService))
        self._tradeManager = StateObject(wrappedValue: AITradeManager())
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header with AI Status
                aiStatusHeader
                
                // Tab Selection
                tabSelector
                
                // Content based on selected tab
                TabView(selection: $selectedTab) {
                    // AI Analysis Tab
                    aiAnalysisView
                        .tag(0)
                    
                    // Active Trades Tab
                    activeTradesView
                        .tag(1)
                    
                    // Trade History Tab
                    tradeHistoryView
                        .tag(2)
                    
                    // Risk Management Tab
                    riskManagementView
                        .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("🤖 AI Trading")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showSettings = true }) {
                        Image(systemName: "gear")
                            .foregroundColor(.orange)
                    }
                }
            }
            .sheet(isPresented: $showSettings) {
                AISettingsView(settings: $aiEngine.aiSettings)
            }
            .onAppear {
                Task {
                    await aiEngine.startComprehensiveAnalysis()
                }
            }
        }
    }
    
    // MARK: - AI Status Header
    private var aiStatusHeader: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("AI Analysis Engine")
                    .font(.headline)
                    .foregroundColor(.white)
                
                HStack {
                    Circle()
                        .fill(aiEngine.isAnalyzing ? Color.orange : Color.green)
                        .frame(width: 8, height: 8)
                    
                    Text(aiEngine.isAnalyzing ? "Analyzing Markets..." : "Ready")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text("Active Trades")
                    .font(.caption)
                    .foregroundColor(.gray)
                
                Text("\(tradeManager.activeTrades.count)")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.orange)
            }
            
            VStack(alignment: .trailing, spacing: 4) {
                Text("Total P&L")
                    .font(.caption)
                    .foregroundColor(.gray)
                
                Text("$\(String(format: "%.2f", tradeManager.riskMetrics.totalPnL))")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(tradeManager.riskMetrics.totalPnL >= 0 ? .green : .red)
            }
        }
        .padding()
        .background(Color.black.opacity(0.8))
    }
    
    // MARK: - Tab Selector
    private var tabSelector: some View {
        HStack(spacing: 0) {
            ForEach(0..<4) { index in
                Button(action: { selectedTab = index }) {
                    VStack(spacing: 4) {
                        Image(systemName: tabIcon(for: index))
                            .font(.system(size: 16))
                        
                        Text(tabTitle(for: index))
                            .font(.caption)
                    }
                    .foregroundColor(selectedTab == index ? .orange : .gray)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                }
            }
        }
        .background(Color.gray.opacity(0.1))
    }
    
    private func tabIcon(for index: Int) -> String {
        switch index {
        case 0: return "brain.head.profile"
        case 1: return "chart.line.uptrend.xyaxis"
        case 2: return "clock.arrow.circlepath"
        case 3: return "shield.checkered"
        default: return "questionmark"
        }
    }
    
    private func tabTitle(for index: Int) -> String {
        switch index {
        case 0: return "Analysis"
        case 1: return "Active"
        case 2: return "History"
        case 3: return "Risk"
        default: return ""
        }
    }
    
    // MARK: - AI Analysis View
    private var aiAnalysisView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // News Analysis Section
                newsAnalysisSection
                
                // Technical Signals Section
                technicalSignalsSection
                
                // Analysis Results Section
                analysisResultsSection
            }
            .padding()
        }
    }
    
    private var newsAnalysisSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "newspaper")
                    .foregroundColor(.blue)
                Text("News Analysis")
                    .font(.headline)
                    .foregroundColor(.white)
                Spacer()
                Text("\(aiEngine.newsAnalysis.count) items")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            
            ForEach(aiEngine.newsAnalysis.prefix(3)) { news in
                newsCard(news)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private func newsCard(_ news: NewsAnalysis) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(news.headline)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.white)
                .lineLimit(2)
            
            HStack {
                sentimentIndicator(news.sentiment)
                
                Spacer()
                
                Text(news.source)
                    .font(.caption)
                    .foregroundColor(.gray)
                
                Text(timeAgo(news.timestamp))
                    .font(.caption)
                    .foregroundColor(.gray)
            }
        }
        .padding()
        .background(Color.black.opacity(0.3))
        .cornerRadius(8)
    }
    
    private func sentimentIndicator(_ sentiment: Double) -> some View {
        HStack(spacing: 4) {
            Circle()
                .fill(sentimentColor(sentiment))
                .frame(width: 8, height: 8)
            
            Text(sentimentText(sentiment))
                .font(.caption)
                .foregroundColor(sentimentColor(sentiment))
        }
    }
    
    private func sentimentColor(_ sentiment: Double) -> Color {
        if sentiment > 0.6 { return .green }
        else if sentiment < 0.4 { return .red }
        else { return .yellow }
    }
    
    private func sentimentText(_ sentiment: Double) -> String {
        if sentiment > 0.6 { return "Bullish" }
        else if sentiment < 0.4 { return "Bearish" }
        else { return "Neutral" }
    }
    
    private var technicalSignalsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "chart.xyaxis.line")
                    .foregroundColor(.orange)
                Text("Technical Signals")
                    .font(.headline)
                    .foregroundColor(.white)
                Spacer()
                Text("\(aiEngine.technicalSignals.count) signals")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            
            ForEach(aiEngine.technicalSignals.prefix(5)) { signal in
                technicalSignalCard(signal)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private func technicalSignalCard(_ signal: TechnicalSignal) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(signal.symbol)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Text(signal.exchange.rawValue)
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text("RSI: \(String(format: "%.1f", signal.rsi))")
                    .font(.caption)
                    .foregroundColor(signal.rsi > 70 ? .red : signal.rsi < 30 ? .green : .gray)
                
                Text("Trend: \(signal.trend.rawValue)")
                    .font(.caption)
                    .foregroundColor(trendColor(signal.trend))
            }
        }
        .padding()
        .background(Color.black.opacity(0.3))
        .cornerRadius(8)
    }
    
    private func trendColor(_ trend: TrendDirection) -> Color {
        switch trend {
        case .uptrend: return .green
        case .downtrend: return .red
        case .sideways: return .yellow
        }
    }
    
    private var analysisResultsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "cpu")
                    .foregroundColor(.purple)
                Text("AI Analysis Results")
                    .font(.headline)
                    .foregroundColor(.white)
                Spacer()
                Text("\(aiEngine.analysisResults.count) results")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            
            ForEach(aiEngine.analysisResults.prefix(5)) { result in
                analysisResultCard(result)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private func analysisResultCard(_ result: AnalysisResult) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(result.type.rawValue)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(typeColor(result.type))
                        .cornerRadius(4)
                    
                    Text(result.symbol)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                }
                
                Text(result.description)
                    .font(.caption)
                    .foregroundColor(.gray)
                    .lineLimit(1)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                signalBadge(result.signal)
                
                Text("\(String(format: "%.0f", result.confidence * 100))%")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
        }
        .padding()
        .background(Color.black.opacity(0.3))
        .cornerRadius(8)
    }
    
    private func typeColor(_ type: AnalysisType) -> Color {
        switch type {
        case .technical: return .orange
        case .news: return .blue
        case .sentiment: return .purple
        case .volume: return .green
        }
    }
    
    private func signalBadge(_ signal: AnalysisSignal) -> some View {
        Text(signal.rawValue)
            .font(.caption)
            .fontWeight(.bold)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(signalColor(signal))
            .cornerRadius(4)
    }
    
    private func signalColor(_ signal: AnalysisSignal) -> Color {
        switch signal {
        case .bullish: return .green
        case .bearish: return .red
        case .neutral: return .yellow
        }
    }
    
    // MARK: - Active Trades View
    private var activeTradesView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if tradeManager.activeTrades.isEmpty {
                    emptyTradesView
                } else {
                    ForEach(tradeManager.activeTrades) { trade in
                        activeTradeCard(trade)
                    }
                }
            }
            .padding()
        }
    }
    
    private var emptyTradesView: some View {
        VStack(spacing: 16) {
            Image(systemName: "chart.line.flattrend.xyaxis")
                .font(.system(size: 48))
                .foregroundColor(.gray)
            
            Text("No Active Trades")
                .font(.headline)
                .foregroundColor(.white)
            
            Text("AI is analyzing markets for trading opportunities")
                .font(.subheadline)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
        }
        .padding()
    }
    
    private func activeTradeCard(_ trade: AITrade) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            // Trade Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(trade.symbol)
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    Text(trade.exchange.rawValue)
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    tradeBadge(trade.action)
                    
                    Text(trade.status.rawValue)
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }
            
            // Trade Details
            VStack(spacing: 8) {
                tradeDetailRow("Entry Price", "$\(String(format: "%.2f", trade.entryPrice))")
                tradeDetailRow("Quantity", "\(String(format: "%.6f", trade.quantity))")
                tradeDetailRow("Stop Loss", "$\(String(format: "%.2f", trade.stopLoss))")
                tradeDetailRow("Take Profit", "$\(String(format: "%.2f", trade.takeProfit))")
                tradeDetailRow("Trailing Stop", "\(String(format: "%.1f", trade.trailingStopPercent))%")
            }
            
            // AI Reasoning
            VStack(alignment: .leading, spacing: 4) {
                Text("AI Reasoning")
                    .font(.caption)
                    .foregroundColor(.gray)
                
                Text(trade.reasoning)
                    .font(.caption)
                    .foregroundColor(.white)
                    .lineLimit(3)
            }
            
            // Trade Actions
            HStack {
                Button("Close Trade") {
                    Task {
                        await tradeManager.closeTrade(trade)
                    }
                }
                                                .buttonStyle(DashboardActionButtonStyle(color: .red))
                
                Spacer()
                
                Button("Modify Stop") {
                    // Show modify stop loss dialog
                }
                                                .buttonStyle(DashboardActionButtonStyle(color: .orange))
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private func tradeBadge(_ action: TradeAction) -> some View {
        Text(action.rawValue.uppercased())
            .font(.caption)
            .fontWeight(.bold)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(action == .buy ? Color.green : Color.red)
            .cornerRadius(6)
    }
    
    private func tradeDetailRow(_ label: String, _ value: String) -> some View {
        HStack {
            Text(label)
                .font(.caption)
                .foregroundColor(.gray)
            
            Spacer()
            
            Text(value)
                .font(.caption)
                .foregroundColor(.white)
        }
    }
    
    // MARK: - Trade History View
    private var tradeHistoryView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(tradeManager.tradeHistory.sorted { $0.timestamp > $1.timestamp }) { execution in
                    tradeHistoryCard(execution)
                }
            }
            .padding()
        }
    }
    
    private func tradeHistoryCard(_ execution: TradeExecution) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(execution.symbol)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Text(execution.type.rawValue)
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text("$\(String(format: "%.2f", execution.price))")
                    .font(.subheadline)
                    .foregroundColor(.white)
                
                if let pnl = execution.pnl {
                    Text("P&L: $\(String(format: "%.2f", pnl))")
                        .font(.caption)
                        .foregroundColor(pnl >= 0 ? .green : .red)
                }
            }
        }
        .padding()
        .background(Color.black.opacity(0.3))
        .cornerRadius(8)
    }
    
    // MARK: - Risk Management View
    private var riskManagementView: some View {
        ScrollView {
            VStack(spacing: 16) {
                riskMetricsCard
                riskSettingsCard
            }
            .padding()
        }
    }
    
    private var riskMetricsCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Risk Metrics")
                .font(.headline)
                .foregroundColor(.white)
            
            VStack(spacing: 8) {
                riskMetricRow("Total Balance", "$\(String(format: "%.2f", tradeManager.riskMetrics.totalBalance))")
                riskMetricRow("Total Exposure", "$\(String(format: "%.2f", tradeManager.riskMetrics.totalExposure))")
                riskMetricRow("Win Rate", "\(String(format: "%.1f", tradeManager.riskMetrics.winRate * 100))%")
                riskMetricRow("Max Drawdown", "\(String(format: "%.1f", tradeManager.riskMetrics.maxDrawdown * 100))%")
                riskMetricRow("Sharpe Ratio", String(format: "%.2f", tradeManager.riskMetrics.sharpeRatio))
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private func riskMetricRow(_ label: String, _ value: String) -> some View {
        HStack {
            Text(label)
                .font(.subheadline)
                .foregroundColor(.gray)
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.white)
        }
    }
    
    private var riskSettingsCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Risk Settings")
                .font(.headline)
                .foregroundColor(.white)
            
            VStack(spacing: 12) {
                Toggle("Auto Trading", isOn: $aiEngine.aiSettings.autoTradingEnabled)
                    .toggleStyle(SwitchToggleStyle(tint: .orange))
                
                HStack {
                    Text("Risk per Trade")
                        .foregroundColor(.gray)
                    Spacer()
                    Text("\(String(format: "%.1f", aiEngine.aiSettings.riskPercentage))%")
                        .foregroundColor(.white)
                }
                
                HStack {
                    Text("Max Concurrent Trades")
                        .foregroundColor(.gray)
                    Spacer()
                    Text("\(aiEngine.aiSettings.maxConcurrentTrades)")
                        .foregroundColor(.white)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    // MARK: - Helper Functions
    private func timeAgo(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

// MARK: - Action Button Style
struct DashboardActionButtonStyle: ButtonStyle {
    let color: Color
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.caption)
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(color.opacity(configuration.isPressed ? 0.7 : 1.0))
            .cornerRadius(6)
    }
}

// MARK: - AI Settings View
struct AISettingsView: View {
    @Binding var settings: AISettings
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            Form {
                Section("Trading Settings") {
                    HStack {
                        Text("Decision Threshold")
                        Spacer()
                        Text("\(String(format: "%.1f", settings.decisionThreshold * 100))%")
                    }
                    Slider(value: $settings.decisionThreshold, in: 0.1...1.0, step: 0.1)
                    
                    HStack {
                        Text("Risk per Trade")
                        Spacer()
                        Text("\(String(format: "%.1f", settings.riskPercentage))%")
                    }
                    Slider(value: $settings.riskPercentage, in: 0.5...10.0, step: 0.5)
                    
                    HStack {
                        Text("Stop Loss")
                        Spacer()
                        Text("\(String(format: "%.1f", settings.stopLossPercent))%")
                    }
                    Slider(value: $settings.stopLossPercent, in: 1.0...20.0, step: 1.0)
                    
                    HStack {
                        Text("Trailing Stop")
                        Spacer()
                        Text("\(String(format: "%.1f", settings.trailingStopPercent))%")
                    }
                    Slider(value: $settings.trailingStopPercent, in: 0.5...10.0, step: 0.5)
                }
                
                Section("Analysis Settings") {
                    Toggle("News Analysis", isOn: $settings.enableNewsAnalysis)
                    Toggle("Sentiment Analysis", isOn: $settings.enableSentimentAnalysis)
                    Toggle("Technical Analysis", isOn: $settings.enableTechnicalAnalysis)
                }
                
                Section("Risk Management") {
                    Toggle("Auto Trading", isOn: $settings.autoTradingEnabled)
                    
                    Stepper("Max Trades: \(settings.maxConcurrentTrades)", 
                           value: $settings.maxConcurrentTrades, 
                           in: 1...20)
                }
            }
            .navigationTitle("AI Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}