import Foundation

class MultiExchangePortfolio: ObservableObject {
    @Published var totalValueUSD: Double = 0
    @Published var profitLoss24h: Double = 0
    @Published var profitLossPercentage24h: Double = 0
    @Published var activeStrategies: Int = 0
    @Published var balances: [SupportedExchange: [String: Double]] = [:]
    @Published var openPositions: [RealPosition] = []
    
    init() {
        // Initialize with default values
        setupDefaultData()
    }
    
    private func setupDefaultData() {
        // Simulate portfolio data
        totalValueUSD = 25847.32
        profitLoss24h = 1284.67
        profitLossPercentage24h = 5.23
        activeStrategies = 3
        
        // Simulate balances across exchanges
        balances[.kucoin] = [
            "BTC": 0.5,
            "ETH": 5.2,
            "USDT": 1000.0
        ]
        
        balances[.bybit] = [
            "BTC": 0.3,
            "ETH": 2.1,
            "USDT": 500.0
        ]
        
        balances[.mexc] = [
            "BTC": 0.1,
            "USDT": 250.0
        ]
    }
    
    func updateBalance(exchange: SupportedExchange, balances: [String: Double]) {
        self.balances[exchange] = balances
        calculateTotalValue()
    }
    
    func addPosition(_ position: RealPosition) {
        openPositions.append(position)
        calculateTotalValue()
    }
    
    func removePosition(id: UUID) {
        openPositions.removeAll { $0.id == id }
        calculateTotalValue()
    }
    
    private func calculateTotalValue() {
        // Calculate total portfolio value
        var total: Double = 0
        
        for (_, balanceDict) in balances {
            for (_, amount) in balanceDict {
                total += amount // Simplified - would need price conversion
            }
        }
        
        totalValueUSD = total
    }
}

// MARK: - Time In Force (unique to this file)
enum TimeInForce: String, CaseIterable {
    case gtc = "GTC"
    case ioc = "IOC"
    case fok = "FOK"
    
    var displayName: String {
        switch self {
        case .gtc:
            return "Good Till Cancel"
        case .ioc:
            return "Immediate or Cancel"
        case .fok:
            return "Fill or Kill"
        }
    }
} 