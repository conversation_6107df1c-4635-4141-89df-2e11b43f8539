import SwiftUI

// MARK: - Trading Setup Guide
struct TradingSetupGuide: View {
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // Header
                VStack(alignment: .leading, spacing: 8) {
                    Text("🚀 Agent Leon Live Trading Setup")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                    
                    Text("Stap-voor-stap gids om echte crypto trading te starten")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                }
                
                Divider()
                
                // Step 1: API Keys
                SetupStep(
                    number: 1,
                    title: "API Keys Configuratie",
                    description: "Jouw exchange API keys zijn al ingesteld",
                    isCompleted: true,
                    details: [
                        "✅ KuCoin API Key: ************************",
                        "✅ KuCoin Secret: c4fab7b9-3120-4ede-a7c9-5f3645b6acec", 
                        "✅ KuCoin Passphrase: Kucoinn",
                        "✅ MEXC API Key: mx0vglmGyrf9LW7tY9",
                        "✅ MEXC Secret: b5bb5493128b483c9ebe199d013042c1"
                    ]
                )
                
                // Step 2: Exchange Permissions
                SetupStep(
                    number: 2,
                    title: "Exchange Permissions Controleren",
                    description: "Zorg ervoor dat je API keys de juiste rechten hebben",
                    isCompleted: false,
                    details: [
                        "🔍 KuCoin: Ga naar API Management",
                        "🔍 Zet 'Spot Trading' aan",
                        "🔍 Zet 'General' aan voor account info",
                        "⚠️ Zet GEEN 'Withdraw' aan (voor veiligheid)",
                        "🔍 MEXC: Zelfde instellingen controleren"
                    ]
                )
                
                // Step 3: Testnet First
                SetupStep(
                    number: 3,
                    title: "Start met Testnet",
                    description: "Test eerst met fake geld voordat je live gaat",
                    isCompleted: false,
                    details: [
                        "🧪 App start automatisch in TESTNET mode",
                        "💰 Testnet gebruikt fake USDT voor veiligheid",
                        "📊 Alle functionaliteit werkt hetzelfde",
                        "✅ Test orders, strategies, en voice commands",
                        "🔄 Switch naar MAINNET als alles werkt"
                    ]
                )
                
                // Step 4: Safety Settings
                SetupStep(
                    number: 4,
                    title: "Veiligheidsinstellingen",
                    description: "Configureer risk management",
                    isCompleted: false,
                    details: [
                        "💵 Max Trade Amount: $100 (start klein)",
                        "🛑 Stop Loss: 5% (automatische verlieslimiet)",
                        "🎯 Take Profit: 10% (automatische winstneming)",
                        "⚠️ Deze limieten beschermen je geld",
                        "📈 Verhoog limieten als je ervaring opdoet"
                    ]
                )
                
                // Step 5: Enable Live Trading
                SetupStep(
                    number: 5,
                    title: "Live Trading Activeren",
                    description: "Start met echte trading",
                    isCompleted: false,
                    details: [
                        "📱 Ga naar 'Live Trading' tab in de app",
                        "🔍 Klik 'Test Connections' om API's te checken",
                        "💰 Check je account balance",
                        "🚀 Klik 'Enable Live Trading'",
                        "⚡ Agents beginnen automatisch met traden"
                    ]
                )
                
                // Voice Commands
                VStack(alignment: .leading, spacing: 12) {
                    Text("🎤 Nederlandse Voice Commands")
                        .font(.headline)
                        .foregroundColor(.cyan)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        VoiceCommandRow(command: "Koop Bitcoin", description: "Koopt Bitcoin met live order")
                        VoiceCommandRow(command: "Verkoop Bitcoin", description: "Verkoopt Bitcoin met live order")
                        VoiceCommandRow(command: "Activeer Trading", description: "Zet live trading aan")
                        VoiceCommandRow(command: "Stop Trading", description: "Zet live trading uit")
                        VoiceCommandRow(command: "Saldo Check", description: "Update account balances")
                        VoiceCommandRow(command: "Agent Status", description: "Check agent status")
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(.ultraThinMaterial)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.cyan.opacity(0.3), lineWidth: 1)
                        )
                )
                
                // Safety Warning
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.red)
                        Text("⚠️ BELANGRIJKE VEILIGHEIDSWAARSCHUWING")
                            .font(.headline)
                            .foregroundColor(.red)
                    }
                    
                    Text("• Start ALTIJD met kleine bedragen ($50-100)")
                        .foregroundColor(.gray)
                    Text("• Gebruik eerst TESTNET om alles te testen")
                        .foregroundColor(.gray)
                    Text("• Monitor je trades actief, vooral in het begin")
                        .foregroundColor(.gray)
                    Text("• Crypto trading is risicovol - handel verantwoordelijk")
                        .foregroundColor(.gray)
                    Text("• Agents maken beslissingen op basis van AI - geen garanties")
                        .foregroundColor(.gray)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.red.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.red.opacity(0.3), lineWidth: 1)
                        )
                )
                
                // Quick Start Button
                Button(action: {
                    // This would navigate to the live trading tab
                }) {
                    HStack {
                        Image(systemName: "rocket.fill")
                        Text("Start Live Trading Setup")
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [.orange, .red]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(12)
                }
            }
            .padding()
        }
        .background(Color.black)
        .preferredColorScheme(.dark)
    }
}

// MARK: - Setup Step Component
struct SetupStep: View {
    let number: Int
    let title: String
    let description: String
    let isCompleted: Bool
    let details: [String]
    
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                // Step Number
                ZStack {
                    Circle()
                        .fill(isCompleted ? .green : .orange)
                        .frame(width: 30, height: 30)
                    
                    if isCompleted {
                        Image(systemName: "checkmark")
                            .foregroundColor(.white)
                            .fontWeight(.bold)
                    } else {
                        Text("\(number)")
                            .foregroundColor(.white)
                            .fontWeight(.bold)
                    }
                }
                
                // Title and Description
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    Text(description)
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                
                Spacer()
                
                // Expand Button
                Button(action: { withAnimation { isExpanded.toggle() } }) {
                    Image(systemName: "chevron.down")
                        .foregroundColor(.gray)
                        .rotationEffect(.degrees(isExpanded ? 180 : 0))
                        .animation(.easeInOut, value: isExpanded)
                }
            }
            
            // Details
            if isExpanded {
                VStack(alignment: .leading, spacing: 6) {
                    ForEach(details, id: \.self) { detail in
                        Text(detail)
                            .font(.caption)
                            .foregroundColor(.gray)
                            .padding(.leading, 40)
                    }
                }
                .transition(.opacity.combined(with: .slide))
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(isCompleted ? Color.green.opacity(0.3) : Color.orange.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - Voice Command Row
struct VoiceCommandRow: View {
    let command: String
    let description: String
    
    var body: some View {
        HStack {
            Image(systemName: "mic.fill")
                .foregroundColor(.cyan)
                .frame(width: 20)
            
            Text("\"\(command)\"")
                .foregroundColor(.white)
                .fontWeight(.medium)
            
            Spacer()
            
            Text(description)
                .font(.caption)
                .foregroundColor(.gray)
        }
    }
}

#Preview {
    TradingSetupGuide()
} 