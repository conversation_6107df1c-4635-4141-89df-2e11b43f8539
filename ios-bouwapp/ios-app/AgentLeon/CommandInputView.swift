import SwiftUI

struct CommandInputView: View {
    @Binding var commandText: String
    let onSubmit: () -> Void
    
    @State private var isRecording = false
    @State private var showingSuggestions = false
    
    private let commandSuggestions = [
        "Status check all agents",
        "Restart agent 1",
        "Analyze data pattern",
        "Optimize performance",
        "Error recovery protocol",
        "Share information between agents",
        "Execute priority tasks",
        "Monitor system health"
    ]
    
    var body: some View {
        VStack(spacing: 12) {
            // Suggestions (when focused)
            if showingSuggestions && !commandSuggestions.isEmpty {
                suggestionsView
            }
            
            // Command Input Row
            HStack(spacing: 12) {
                // Text Input
                HStack {
                    Image(systemName: "terminal")
                        .foregroundColor(.gray)
                        .font(.system(size: 16))
                    
                    TextField("Enter agent command...", text: $commandText)
                        .textFieldStyle(PlainTextFieldStyle())
                        .foregroundColor(.white)
                        .font(.body)
                        .onSubmit {
                            onSubmit()
                            showingSuggestions = false
                        }
                        .onTapGesture {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                showingSuggestions = true
                            }
                        }
                    
                    // Clear button
                    if !commandText.isEmpty {
                        Button(action: {
                            commandText = ""
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.gray)
                                .font(.system(size: 16))
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.black.opacity(0.6))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.gray.opacity(0.5), lineWidth: 1)
                        )
                )
                
                // Voice Input Button
                Button(action: toggleRecording) {
                    Image(systemName: isRecording ? "mic.fill" : "mic")
                        .foregroundColor(isRecording ? .red : .white)
                        .font(.system(size: 20))
                        .frame(width: 48, height: 48)
                        .background(
                            Circle()
                                .fill(isRecording ? Color.red.opacity(0.2) : Color.gray.opacity(0.3))
                                .overlay(
                                    Circle()
                                        .stroke(isRecording ? Color.red : Color.gray.opacity(0.5), lineWidth: 2)
                                )
                        )
                        .scaleEffect(isRecording ? 1.1 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: isRecording)
                }
                
                // Send Button
                Button(action: {
                    onSubmit()
                    showingSuggestions = false
                }) {
                    Image(systemName: "arrow.up.circle.fill")
                        .foregroundColor(commandText.isEmpty ? .gray : .blue)
                        .font(.system(size: 24))
                }
                .disabled(commandText.isEmpty)
            }
        }
        .onTapGesture {
            // Hide suggestions when tapping outside
            withAnimation(.easeInOut(duration: 0.3)) {
                showingSuggestions = false
            }
        }
    }
    
    private var suggestionsView: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header
            HStack {
                Text("Suggested Commands")
                    .font(.caption)
                    .foregroundColor(.gray)
                    .padding(.horizontal, 16)
                    .padding(.top, 12)
                
                Spacer()
                
                Button("Hide") {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        showingSuggestions = false
                    }
                }
                .font(.caption)
                .foregroundColor(.blue)
                .padding(.horizontal, 16)
                .padding(.top, 12)
            }
            
            // Suggestions List
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(filteredSuggestions, id: \.self) { suggestion in
                        suggestionChip(suggestion)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.black.opacity(0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
        )
        .transition(.asymmetric(
            insertion: .opacity.combined(with: .move(edge: .top)),
            removal: .opacity.combined(with: .move(edge: .top))
        ))
    }
    
    private func suggestionChip(_ suggestion: String) -> some View {
        Button(action: {
            commandText = suggestion
            withAnimation(.easeInOut(duration: 0.3)) {
                showingSuggestions = false
            }
        }) {
            Text(suggestion)
                .font(.caption)
                .foregroundColor(.white)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.blue.opacity(0.3))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.blue.opacity(0.6), lineWidth: 1)
                        )
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var filteredSuggestions: [String] {
        if commandText.isEmpty {
            return Array(commandSuggestions.prefix(6))
        } else {
            let filtered = commandSuggestions.filter { 
                $0.localizedCaseInsensitiveContains(commandText) 
            }
            return Array(filtered.prefix(6))
        }
    }
    
    private func toggleRecording() {
        withAnimation(.easeInOut(duration: 0.2)) {
            isRecording.toggle()
        }
        
        // Simulate recording functionality
        if isRecording {
            // Start recording
            print("🎤 Started voice recording...")
            
            // Simulate voice input after 2 seconds
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                withAnimation(.easeInOut(duration: 0.2)) {
                    isRecording = false
                }
                
                // Simulate voice-to-text
                let voiceCommands = [
                    "Status check all agents",
                    "Analyze data patterns",
                    "Restart agent three",
                    "Show system performance"
                ]
                commandText = voiceCommands.randomElement() ?? "Voice command processed"
                print("🎤 Voice recording completed")
            }
        } else {
            print("🎤 Stopped voice recording")
        }
    }
}

#Preview {
    ZStack {
        Color.black.ignoresSafeArea()
        
        VStack {
            Spacer()
            
            CommandInputView(
                commandText: .constant(""),
                onSubmit: {
                    print("Command submitted!")
                }
            )
            .padding()
        }
    }
    .preferredColorScheme(.dark)
} 