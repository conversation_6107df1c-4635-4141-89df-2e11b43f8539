import SwiftUI

struct CommandInputView: View {
    @Binding var commandText: String
    let onSubmit: () -> Void
    
    @State private var isRecording = false
    @State private var showingSuggestions = false
    
    private let commandSuggestions = [
        "Status check all agents",
        "Restart agent 1",
        "Analyze data pattern",
        "Optimize performance",
        "Error recovery protocol",
        "Share information between agents",
        "Execute priority tasks",
        "Monitor system health"
    ]
    
    var body: some View {
        VStack(spacing: 12) {
            // Suggestions (when focused)
            if showingSuggestions && !commandSuggestions.isEmpty {
                suggestionsView
            }
            
            // Command Input Row
            HStack(spacing: 12) {
                // Text Input
                HStack {
                    Image(systemName: "terminal")
                        .foregroundColor(.gray)
                        .font(.system(size: 16))
                    
                    TextField("Enter agent command...", text: $commandText)
                        .textFieldStyle(PlainTextFieldStyle())
                        .foregroundColor(.white)
                        .font(.body)
                        .onSubmit {
                            onSubmit()
                            showingSuggestions = false
                        }
                        .onTapGesture {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                showingSuggestions = true
                            }
                        }
                    
                    // Clear button
                    if !commandText.isEmpty {
                        Button(action: {
                            commandText = ""
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.gray)
                                .font(.system(size: 16))
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.black.opacity(0.6))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.gray.opacity(0.5), lineWidth: 1)
                        )
                )
                
                // Voice Input Button
                Button(action: toggleRecording) {
                    Image(systemName: isRecording ? "mic.fill" : "mic")
                        .foregroundColor(isRecording ? .red : .white)
                        .font(.system(size: 20))
                        .frame(width: 48, height: 48)
                        .background(
                            Circle()
                                .fill(isRecording ? Color.red.opacity(0.2) : Color.gray.opacity(0.3))
                                .overlay(
                                    Circle()
                                        .stroke(isRecording ? Color.red : Color.gray.opacity(0.5), lineWidth: 2)
                                )
                        )
                        .scaleEffect(isRecording ? 1.1 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: isRecording)
                }
                
                // Send Button
                Button(action: {
                    onSubmit()
                    showingSuggestions = false
                }) {
                    Image(systemName: "arrow.up.circle.fill")
                        .foregroundColor(commandText.isEmpty ? .gray : .blue)
                        .font(.system(size: 24))
                }
                .disabled(commandText.isEmpty)
            }
        }
        .onTapGesture {
            // Hide suggestions when tapping outside
            withAnimation(.easeInOut(duration: 0.3)) {
                showingSuggestions = false
            }
        }
    }
    
    private var suggestionsView: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header
            HStack {
                Text("Suggested Commands")
                    .font(.caption)
                    .foregroundColor(.gray)
                    .padding(.horizontal, 16)
                    .padding(.top, 12)
                
                Spacer()
                
                Button("Hide") {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        showingSuggestions = false
                    }
                }
                .font(.caption)
                .foregroundColor(.blue)
                .padding(.horizontal, 16)
                .padding(.top, 12)
            }
            
            // Suggestions List
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(filteredSuggestions, id: \.self) { suggestion in
                        suggestionChip(suggestion)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.black.opacity(0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
        )
        .transition(.asymmetric(
            insertion: .opacity.combined(with: .move(edge: .top)),
            removal: .opacity.combined(with: .move(edge: .top))
        ))
    }
    
    private func suggestionChip(_ suggestion: String) -> some View {
        Button(action: {
            commandText = suggestion
            withAnimation(.easeInOut(duration: 0.3)) {
                showingSuggestions = false
            }
        }) {
            Text(suggestion)
                .font(.caption)
                .foregroundColor(.white)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.blue.opacity(0.3))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.blue.opacity(0.6), lineWidth: 1)
                        )
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var filteredSuggestions: [String] {
        if commandText.isEmpty {
            return Array(commandSuggestions.prefix(6))
        } else {
            let filtered = commandSuggestions.filter { 
                $0.localizedCaseInsensitiveContains(commandText) 
            }
            return Array(filtered.prefix(6))
        }
    }
    
    private func toggleRecording() {
        withAnimation(.easeInOut(duration: 0.2)) {
            isRecording.toggle()
        }
        
        // Simulate recording functionality
        if isRecording {
            // Start recording
            print("🎤 Started voice recording...")
            
            // Simulate voice input after 2 seconds
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                withAnimation(.easeInOut(duration: 0.2)) {
                    isRecording = false
                }
                
                // Simulate voice-to-text
                let voiceCommands = [
                    "Status check all agents",
                    "Analyze data patterns",
                    "Restart agent three",
                    "Show system performance"
                ]
                commandText = voiceCommands.randomElement() ?? "Voice command processed"
                print("🎤 Voice recording completed")
            }
        } else {
            print("🎤 Stopped voice recording")
        }
    }
}

#Preview {
    ZStack {
        Color.black.ignoresSafeArea()
        
        VStack {
            Spacer()
            
            CommandInputView(
                commandText: .constant(""),
                onSubmit: {
                    print("Command submitted!")
                }
            )
            .padding()
        }
    }
    .preferredColorScheme(.dark)
}

// MARK: - Agent Collaboration Dashboard

struct AgentCollaborationDashboard: View {
    @StateObject private var agentManager = AgentManager()
    @State private var commandText = ""
    @State private var showingCommandInput = false

    var body: some View {
        ZStack {
            // Background
            Color.black.ignoresSafeArea()

            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    Text("AI Trading Assistant")
                        .font(.title2)
                        .foregroundColor(.white)
                        .padding(.top)

                    // Agent Collaboration Animation
                    AgentCollaborationView()
                        .frame(height: 200)
                        .padding()

                    // Agent Cards
                    LazyVStack(spacing: 16) {
                        ForEach(Array(agentManager.agents.enumerated()), id: \.element.id) { index, agent in
                            AgentStatusCard(agent: agent, agentNumber: index + 1)
                        }
                    }
                    .padding(.horizontal)

                    // Command Input Section
                    VStack(spacing: 12) {
                        HStack {
                            TextField("Enter agent command...", text: $commandText)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .foregroundColor(.white)
                                .background(Color.gray.opacity(0.2))
                                .cornerRadius(8)

                            Button(action: {
                                // Voice input button
                                showingCommandInput.toggle()
                            }) {
                                Image(systemName: "mic.fill")
                                    .foregroundColor(.white)
                                    .frame(width: 44, height: 44)
                                    .background(Color.red)
                                    .cornerRadius(8)
                            }
                        }
                        .padding(.horizontal)

                        if !commandText.isEmpty {
                            Button("Execute Command") {
                                agentManager.executeGlobalCommand(commandText)
                                commandText = ""
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .cornerRadius(8)
                            .padding(.horizontal)
                        }
                    }
                    .padding(.bottom, 20)
                }
            }
        }
        .navigationTitle("Agent Dashboard")
        .navigationBarTitleDisplayMode(.inline)
    }
}

struct AgentStatusCard: View {
    let agent: Agent
    let agentNumber: Int
    @State private var isAnimating = false

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header with status
            HStack {
                Text("Agent \(agentNumber)")
                    .font(.headline)
                    .foregroundColor(.white)
                    .fontWeight(.bold)

                Spacer()

                StatusBadge(status: agent.status)
            }

            // Current task (if active)
            if agent.status == .active && agent.currentTask != nil {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Current Task:")
                        .font(.caption)
                        .foregroundColor(.yellow)
                        .fontWeight(.medium)

                    Text(agent.currentTask?.name ?? "Analyzing data")
                        .font(.subheadline)
                        .foregroundColor(.white)
                }
            }

            // Performance bar (for active and error states)
            if agent.status == .active || agent.status == .error {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text("Performance")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))

                        Spacer()

                        Text("\(Int(agent.performance))%")
                            .font(.caption)
                            .foregroundColor(.white)
                            .fontWeight(.medium)
                    }

                    ProgressView(value: agent.performance / 100.0)
                        .progressViewStyle(LinearProgressViewStyle(tint: performanceColor))
                        .scaleEffect(x: 1, y: 2, anchor: .center)
                }
            }
        }
        .padding(16)
        .background(cardBackgroundColor)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(borderColor, lineWidth: 2)
        )
        .scaleEffect(isAnimating ? 1.02 : 1.0)
        .onAppear {
            if agent.status == .active || agent.status == .thinking {
                withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
                    isAnimating = true
                }
            }
        }
    }

    private var cardBackgroundColor: Color {
        switch agent.status {
        case .active:
            return Color.orange.opacity(0.1)
        case .thinking:
            return Color.blue.opacity(0.1)
        case .error:
            return Color.red.opacity(0.1)
        case .idle:
            return Color.gray.opacity(0.1)
        }
    }

    private var borderColor: Color {
        switch agent.status {
        case .active:
            return Color.orange
        case .thinking:
            return Color.blue
        case .error:
            return Color.red
        case .idle:
            return Color.gray
        }
    }

    private var performanceColor: Color {
        if agent.performance > 70 {
            return .orange
        } else {
            return .red
        }
    }
}

struct StatusBadge: View {
    let status: AgentStatus

    var body: some View {
        Text(status.rawValue)
            .font(.caption)
            .fontWeight(.bold)
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 4)
            .background(status.color)
            .cornerRadius(12)
    }
}