import Foundation
import Combine

// MARK: - Cryptocurrency Models
struct Cryptocurrency: Identifiable, Codable {
    let id = UUID()
    let symbol: String
    let name: String
    var price: Double
    var change24h: Double
    var volume: Double
    var marketCap: Double
    var lastUpdate: Date
    
    var changePercentage: String {
        return String(format: "%.2f%%", change24h)
    }
    
    var isPositive: Bool {
        return change24h >= 0
    }
    
    enum CodingKeys: String, CodingKey {
        case symbol, name, price, change24h, volume, marketCap, lastUpdate
    }
}

struct TradingPosition: Identifiable, Codable {
    let id = UUID()
    let symbol: String
    let type: PositionType
    let amount: Double
    let entryPrice: Double
    var currentPrice: Double
    let timestamp: Date
    var stopLoss: Double?
    var takeProfit: Double?
    
    enum PositionType: String, CaseIterable, Codable {
        case long = "LONG"
        case short = "SHORT"
    }
    
    var pnl: Double {
        switch type {
        case .long:
            return (currentPrice - entryPrice) * amount
        case .short:
            return (entryPrice - currentPrice) * amount
        }
    }
    
    var pnlPercentage: Double {
        return (pnl / (entryPrice * amount)) * 100
    }
    
    enum CodingKeys: String, CodingKey {
        case symbol, type, amount, entryPrice, currentPrice, timestamp, stopLoss, takeProfit
    }
}

struct TradingStrategy: Identifiable {
    let id = UUID()
    let name: String
    let description: String
    let riskLevel: RiskLevel
    let timeFrame: TimeFrame
    var isActive: Bool
    
    enum RiskLevel: String, CaseIterable {
        case low = "Low Risk"
        case medium = "Medium Risk"
        case high = "High Risk"
        case extreme = "Extreme Risk"
    }
    
    enum TimeFrame: String, CaseIterable {
        case scalping = "1m-5m"
        case dayTrading = "15m-1h"
        case swing = "4h-1d"
        case position = "1d+"
    }
}

// MARK: - Crypto Trading Agent
class CryptoTradingAgent: Agent {
    @Published var watchlist: [Cryptocurrency] = []
    @Published var portfolio: [TradingPosition] = []
    @Published var balance: Double = 10000.0 // Starting balance
    @Published var totalPnL: Double = 0.0
    @Published var activeStrategy: TradingStrategy?
    @Published var tradingSignals: [TradingSignal] = []
    @Published var isAutoTradingEnabled: Bool = false
    
    private var priceUpdateTimer: Timer?
    private var strategyTimer: Timer?
    
    // Trading Strategies
    private let availableStrategies = [
        TradingStrategy(name: "Trend Following", 
                       description: "Volgt markt trends met moving averages", 
                       riskLevel: .medium, 
                       timeFrame: .dayTrading, 
                       isActive: true),
        
        TradingStrategy(name: "Mean Reversion", 
                       description: "Koopt dips en verkoopt peaks", 
                       riskLevel: .low, 
                       timeFrame: .swing, 
                       isActive: false),
        
        TradingStrategy(name: "Breakout Trading", 
                       description: "Handelt op support/resistance doorbraken", 
                       riskLevel: .high, 
                       timeFrame: .scalping, 
                       isActive: false),
        
        TradingStrategy(name: "DCA Strategy", 
                       description: "Dollar Cost Averaging voor lange termijn", 
                       riskLevel: .low, 
                       timeFrame: .position, 
                       isActive: false)
    ]
    
    override init(name: String, status: AgentStatus = .idle, performance: Double = 100.0, capabilities: [String] = [], version: String = "1.0.0") {
        super.init(name: name, status: status, performance: performance, capabilities: capabilities, version: version)
        setupCryptoWatchlist()
        startPriceMonitoring()
        activeStrategy = availableStrategies.first
    }
    
    // MARK: - Setup & Configuration
    private func setupCryptoWatchlist() {
        watchlist = [
            Cryptocurrency(symbol: "BTC", name: "Bitcoin", price: 45000, change24h: 2.5, volume: 28000000000, marketCap: 850000000000, lastUpdate: Date()),
            Cryptocurrency(symbol: "ETH", name: "Ethereum", price: 3200, change24h: -1.8, volume: 15000000000, marketCap: 380000000000, lastUpdate: Date()),
            Cryptocurrency(symbol: "ADA", name: "Cardano", price: 0.65, change24h: 5.2, volume: 2500000000, marketCap: 22000000000, lastUpdate: Date()),
            Cryptocurrency(symbol: "DOT", name: "Polkadot", price: 8.5, change24h: -3.1, volume: 800000000, marketCap: 9500000000, lastUpdate: Date()),
            Cryptocurrency(symbol: "LINK", name: "Chainlink", price: 15.2, change24h: 7.8, volume: 1200000000, marketCap: 8500000000, lastUpdate: Date())
        ]
    }
    
    private func startPriceMonitoring() {
        priceUpdateTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { _ in
            self.updatePrices()
            self.analyzeMarket()
            self.checkTradingSignals()
        }
    }
    
    // MARK: - Price Monitoring
    private func updatePrices() {
        updateStatus(to: .thinking)
        
        for i in 0..<watchlist.count {
            // Simulate realistic price movements
            let volatility = getVolatilityForSymbol(watchlist[i].symbol)
            let randomChange = Double.random(in: -volatility...volatility)
            let newPrice = watchlist[i].price * (1 + randomChange / 100)
            
            watchlist[i].price = max(0.01, newPrice)
            watchlist[i].change24h += randomChange * 0.1
            watchlist[i].lastUpdate = Date()
        }
        
        updatePortfolioValues()
        updateStatus(to: .active)
    }
    
    private func getVolatilityForSymbol(_ symbol: String) -> Double {
        switch symbol {
        case "BTC": return 2.0
        case "ETH": return 3.0
        case "ADA": return 5.0
        case "DOT": return 4.0
        case "LINK": return 6.0
        default: return 3.0
        }
    }
    
    private func updatePortfolioValues() {
        for i in 0..<portfolio.count {
            if let crypto = watchlist.first(where: { $0.symbol == portfolio[i].symbol }) {
                portfolio[i].currentPrice = crypto.price
            }
        }
        
        totalPnL = portfolio.reduce(0) { $0 + $1.pnl }
    }
    
    // MARK: - Market Analysis
    private func analyzeMarket() {
        guard let strategy = activeStrategy else { return }
        
        switch strategy.name {
        case "Trend Following":
            analyzeTrendFollowing()
        case "Mean Reversion":
            analyzeMeanReversion()
        case "Breakout Trading":
            analyzeBreakouts()
        case "DCA Strategy":
            analyzeDCA()
        default:
            break
        }
    }
    
    private func analyzeTrendFollowing() {
        for crypto in watchlist {
            if crypto.change24h > 3.0 && !hasPosition(for: crypto.symbol) {
                generateBuySignal(for: crypto, reason: "Strong upward trend detected")
            } else if crypto.change24h < -5.0 && hasLongPosition(for: crypto.symbol) {
                generateSellSignal(for: crypto, reason: "Trend reversal detected")
            }
        }
    }
    
    private func analyzeMeanReversion() {
        for crypto in watchlist {
            if crypto.change24h < -8.0 && !hasPosition(for: crypto.symbol) {
                generateBuySignal(for: crypto, reason: "Oversold condition - mean reversion expected")
            } else if crypto.change24h > 8.0 && hasLongPosition(for: crypto.symbol) {
                generateSellSignal(for: crypto, reason: "Overbought condition - taking profits")
            }
        }
    }
    
    private func analyzeBreakouts() {
        // Simplified breakout detection
        for crypto in watchlist {
            if crypto.change24h > 5.0 && crypto.volume > 1000000000 {
                generateBuySignal(for: crypto, reason: "Breakout with high volume")
            }
        }
    }
    
    private func analyzeDCA() {
        // Dollar Cost Averaging - regular buys regardless of price
        if Int(Date().timeIntervalSince1970) % 300 == 0 { // Every 5 minutes for demo
            if let btc = watchlist.first(where: { $0.symbol == "BTC" }) {
                generateBuySignal(for: btc, reason: "DCA Schedule", amount: 100)
            }
        }
    }
    
    // MARK: - Trading Signals
    private func generateBuySignal(for crypto: Cryptocurrency, reason: String, amount: Double? = nil) {
        let signal = TradingSignal(
            type: .buy,
            symbol: crypto.symbol,
            price: crypto.price,
            reason: reason,
            confidence: calculateConfidence(for: crypto),
            recommendedAmount: amount ?? calculatePositionSize(for: crypto)
        )
        
        tradingSignals.append(signal)
        
        if isAutoTradingEnabled {
            executeTrade(signal: signal)
        }
        
        print("🟢 BUY SIGNAL: \(crypto.symbol) at $\(crypto.price) - \(reason)")
    }
    
    private func generateSellSignal(for crypto: Cryptocurrency, reason: String) {
        guard let position = portfolio.first(where: { $0.symbol == crypto.symbol }) else { return }
        
        let signal = TradingSignal(
            type: .sell,
            symbol: crypto.symbol,
            price: crypto.price,
            reason: reason,
            confidence: calculateConfidence(for: crypto),
            recommendedAmount: position.amount
        )
        
        tradingSignals.append(signal)
        
        if isAutoTradingEnabled {
            executeTrade(signal: signal)
        }
        
        print("🔴 SELL SIGNAL: \(crypto.symbol) at $\(crypto.price) - \(reason)")
    }
    
    private func calculateConfidence(for crypto: Cryptocurrency) -> Double {
        // Simplified confidence calculation
        let volumeScore = min(crypto.volume / 1000000000, 1.0) * 30
        let momentumScore = min(abs(crypto.change24h) / 10.0, 1.0) * 40
        let marketCapScore = min(crypto.marketCap / 100000000000, 1.0) * 30
        
        return volumeScore + momentumScore + marketCapScore
    }
    
    private func calculatePositionSize(for crypto: Cryptocurrency) -> Double {
        let riskPercentage = 0.02 // 2% of balance per trade
        let riskAmount = balance * riskPercentage
        return riskAmount / crypto.price
    }
    
    // MARK: - Trade Execution
    func executeTrade(signal: TradingSignal) {
        switch signal.type {
        case .buy:
            executeBuyOrder(signal: signal)
        case .sell:
            executeSellOrder(signal: signal)
        }
    }
    
    private func executeBuyOrder(signal: TradingSignal) {
        let cost = signal.price * signal.recommendedAmount
        
        guard balance >= cost else {
            print("❌ Insufficient balance for trade")
            return
        }
        
        let position = TradingPosition(
            symbol: signal.symbol,
            type: .long,
            amount: signal.recommendedAmount,
            entryPrice: signal.price,
            currentPrice: signal.price,
            timestamp: Date(),
            stopLoss: signal.price * 0.95, // 5% stop loss
            takeProfit: signal.price * 1.10 // 10% take profit
        )
        
        portfolio.append(position)
        balance -= cost
        
        print("✅ BOUGHT \(signal.recommendedAmount) \(signal.symbol) at $\(signal.price)")
    }
    
    private func executeSellOrder(signal: TradingSignal) {
        guard let index = portfolio.firstIndex(where: { $0.symbol == signal.symbol }) else { return }
        
        let position = portfolio[index]
        let proceeds = signal.price * position.amount
        
        portfolio.remove(at: index)
        balance += proceeds
        
        print("✅ SOLD \(position.amount) \(signal.symbol) at $\(signal.price) - P&L: $\(position.pnl)")
    }
    
    // MARK: - Helper Methods
    private func hasPosition(for symbol: String) -> Bool {
        return portfolio.contains { $0.symbol == symbol }
    }
    
    private func hasLongPosition(for symbol: String) -> Bool {
        return portfolio.contains { $0.symbol == symbol && $0.type == .long }
    }
    
    private func checkTradingSignals() {
        // Keep only recent signals (last 10)
        if tradingSignals.count > 10 {
            tradingSignals = Array(tradingSignals.suffix(10))
        }
    }
    
    // MARK: - Risk Management
    private func checkStopLossAndTakeProfit() {
        for position in portfolio {
            if let stopLoss = position.stopLoss, position.currentPrice <= stopLoss {
                if let crypto = watchlist.first(where: { $0.symbol == position.symbol }) {
                    generateSellSignal(for: crypto, reason: "Stop Loss Triggered")
                }
            }
            
            if let takeProfit = position.takeProfit, position.currentPrice >= takeProfit {
                if let crypto = watchlist.first(where: { $0.symbol == position.symbol }) {
                    generateSellSignal(for: crypto, reason: "Take Profit Triggered")
                }
            }
        }
    }
    
    // MARK: - Custom Commands
    override func processCommand(_ command: String) {
        let cmd = command.lowercased()
        
        if cmd.contains("buy") && cmd.contains("btc") {
            if let btc = watchlist.first(where: { $0.symbol == "BTC" }) {
                generateBuySignal(for: btc, reason: "Manual buy command")
            }
        } else if cmd.contains("sell") && cmd.contains("btc") {
            if let btc = watchlist.first(where: { $0.symbol == "BTC" }) {
                generateSellSignal(for: btc, reason: "Manual sell command")
            }
        } else if cmd.contains("portfolio") {
            printPortfolioStatus()
        } else if cmd.contains("auto trading on") {
            isAutoTradingEnabled = true
            updateStatus(to: .active)
        } else if cmd.contains("auto trading off") {
            isAutoTradingEnabled = false
            updateStatus(to: .idle)
        } else {
            super.processCommand(command)
        }
    }
    
    private func printPortfolioStatus() {
        print("💰 Portfolio Status:")
        print("Balance: $\(String(format: "%.2f", balance))")
        print("Total P&L: $\(String(format: "%.2f", totalPnL))")
        print("Active Positions: \(portfolio.count)")
        
        for position in portfolio {
            print("  \(position.symbol): \(position.amount) @ $\(position.entryPrice) | P&L: $\(String(format: "%.2f", position.pnl))")
        }
    }
}

// MARK: - Trading Signal
struct TradingSignal: Identifiable {
    let id = UUID()
    let type: OrderSide
    let symbol: String
    let price: Double
    let reason: String
    let confidence: Double
    let recommendedAmount: Double
    let timestamp: Date = Date()
} 