import Foundation
import Combine

// MARK: - Arbitrage Agent
class ArbitrageAgent: Agent {
    @Published var arbitrageOpportunities: [ArbitrageOpportunity] = []
    @Published var activeArbitrages: [ActiveArbitrage] = []
    @Published var profitHistory: [ArbitrageTrade] = []
    @Published var totalProfit: Double = 0.0
    @Published var scanInterval: TimeInterval = 1.0
    @Published var minProfitThreshold: Double = 50.0 // Minimum $50 profit
    
    private var scanTimer: Timer?
    private var exchangeAPIs: MultiExchangeAPIService?
    private let supportedExchanges: [SupportedExchange] = [.binance, .kucoin, .bybit, .mexc]
    
    override init(name: String = "Arbitrage Hunter", status: AgentStatus = .idle, performance: Double = 100.0, capabilities: [String] = [], version: String = "1.0.0") {
        super.init(
            name: name,
            status: status,
            performance: performance,
            capabilities: ["Cross-Exchange Arbitrage", "Triangle Arbitrage", "Statistical Arbitrage", "Flash Trading", "Latency Optimization"],
            version: version
        )
        
        startArbitrageScanning()
    }
    
    // MARK: - Arbitrage Scanning
    private func startArbitrageScanning() {
        scanTimer = Timer.scheduledTimer(withTimeInterval: scanInterval, repeats: true) { _ in
            Task {
                await self.scanForArbitrageOpportunities()
            }
        }
    }
    
    @MainActor
    func scanForArbitrageOpportunities() async {
        updateStatus(to: .thinking)
        
        // Clear old opportunities
        arbitrageOpportunities.removeAll { opportunity in
            Date().timeIntervalSince(opportunity.timestamp) > 60 // Remove opportunities older than 1 minute
        }
        
        // Scan for different types of arbitrage
        await findCrossExchangeArbitrage()
        await findTriangleArbitrage()
        await findStatisticalArbitrage()
        
        // Execute profitable opportunities
        for opportunity in arbitrageOpportunities {
            if opportunity.estimatedProfit > minProfitThreshold && opportunity.riskScore < 0.3 {
                await executeArbitrage(opportunity)
            }
        }
        
        updateStatus(to: .active)
    }
    
    // MARK: - Cross-Exchange Arbitrage
    private func findCrossExchangeArbitrage() async {
        let commonPairs = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "DOTUSDT", "LINKUSDT"]
        
        for pair in commonPairs {
            var pricesByExchange: [(exchange: SupportedExchange, price: Double, volume: Double)] = []
            
            // Simulate fetching prices from different exchanges
            for exchange in supportedExchanges {
                let price = await fetchPrice(for: pair, on: exchange)
                let volume = await fetchVolume(for: pair, on: exchange)
                pricesByExchange.append((exchange, price, volume))
            }
            
            // Find arbitrage opportunities
            for i in 0..<pricesByExchange.count {
                for j in (i+1)..<pricesByExchange.count {
                    let buyExchange = pricesByExchange[i]
                    let sellExchange = pricesByExchange[j]
                    
                    // Check if we can buy on one exchange and sell on another for profit
                    if buyExchange.price < sellExchange.price {
                        let priceDiff = sellExchange.price - buyExchange.price
                        let percentDiff = (priceDiff / buyExchange.price) * 100
                        
                        if percentDiff > 0.3 { // More than 0.3% difference
                            createCrossExchangeOpportunity(
                                pair: pair,
                                buyExchange: buyExchange.exchange,
                                sellExchange: sellExchange.exchange,
                                buyPrice: buyExchange.price,
                                sellPrice: sellExchange.price,
                                maxVolume: min(buyExchange.volume, sellExchange.volume)
                            )
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - Triangle Arbitrage
    private func findTriangleArbitrage() async {
        // Triangle arbitrage: BTC -> ETH -> USDT -> BTC
        let triangles = [
            ["BTCUSDT", "ETHUSDT", "ETHBTC"],
            ["BTCUSDT", "ADAUSDT", "ADABTC"],
            ["ETHUSDT", "LINKUSDT", "LINKETH"]
        ]
        
        for triangle in triangles {
            for exchange in supportedExchanges {
                let prices = await fetchTrianglePrices(pairs: triangle, exchange: exchange)
                
                if let opportunity = calculateTriangleArbitrage(prices: prices, pairs: triangle, exchange: exchange) {
                    arbitrageOpportunities.append(opportunity)
                }
            }
        }
    }
    
    // MARK: - Statistical Arbitrage
    private func findStatisticalArbitrage() async {
        // Find pairs that historically move together but are currently diverged
        let correlatedPairs = [
            ("BTCUSDT", "ETHUSDT", 0.85), // 85% correlation
            ("LINKUSDT", "DOTUSDT", 0.75),
            ("ADAUSDT", "SOLUSDT", 0.70)
        ]
        
        for (pair1, pair2, expectedCorrelation) in correlatedPairs {
            let divergence = await calculatePairDivergence(pair1: pair1, pair2: pair2)
            
            if abs(divergence) > 2.0 { // 2 standard deviations
                let opportunity = ArbitrageOpportunity(
                    type: .statistical,
                    exchanges: supportedExchanges,
                    pairs: [pair1, pair2],
                    estimatedProfit: abs(divergence) * 100,
                    riskScore: 0.4,
                    executionTime: 180, // 3 minutes for mean reversion
                    timestamp: Date()
                )
                
                arbitrageOpportunities.append(opportunity)
            }
        }
    }
    
    // MARK: - Opportunity Creation
    private func createCrossExchangeOpportunity(
        pair: String,
        buyExchange: SupportedExchange,
        sellExchange: SupportedExchange,
        buyPrice: Double,
        sellPrice: Double,
        maxVolume: Double
    ) {
        let tradingFees = 0.002 // 0.2% per trade
        let transferTime = estimateTransferTime(from: buyExchange, to: sellExchange)
        let slippage = 0.001 // 0.1% slippage
        
        let buyAmount = min(10000 / buyPrice, maxVolume * 0.1) // Max $10k or 10% of volume
        let grossProfit = (sellPrice - buyPrice) * buyAmount
        let fees = (buyPrice * buyAmount * tradingFees) + (sellPrice * buyAmount * tradingFees)
        let estimatedSlippage = sellPrice * buyAmount * slippage
        let netProfit = grossProfit - fees - estimatedSlippage
        
        if netProfit > minProfitThreshold {
            let opportunity = ArbitrageOpportunity(
                type: .crossExchange,
                exchanges: [buyExchange, sellExchange],
                pairs: [pair],
                estimatedProfit: netProfit,
                riskScore: calculateRiskScore(transferTime: transferTime, volatility: 0.02),
                executionTime: transferTime,
                timestamp: Date(),
                details: ArbitrageDetails(
                    buyExchange: buyExchange,
                    sellExchange: sellExchange,
                    buyPrice: buyPrice,
                    sellPrice: sellPrice,
                    amount: buyAmount
                )
            )
            
            arbitrageOpportunities.append(opportunity)
            
            print("💰 Arbitrage Found: Buy \(pair) on \(buyExchange) @ $\(buyPrice), Sell on \(sellExchange) @ $\(sellPrice). Profit: $\(netProfit)")
        }
    }
    
    // MARK: - Execution
    private func executeArbitrage(_ opportunity: ArbitrageOpportunity) async {
        updateStatus(to: .active)
        
        let execution = ActiveArbitrage(
            opportunity: opportunity,
            status: .executing,
            startTime: Date()
        )
        
        activeArbitrages.append(execution)
        
        // Simulate execution based on type
        switch opportunity.type {
        case .crossExchange:
            await executeCrossExchangeArbitrage(opportunity)
        case .triangle:
            await executeTriangleArbitrage(opportunity)
        case .statistical:
            await executeStatisticalArbitrage(opportunity)
        }
    }
    
    private func executeCrossExchangeArbitrage(_ opportunity: ArbitrageOpportunity) async {
        guard let details = opportunity.details else { return }
        
        print("🚀 Executing Cross-Exchange Arbitrage...")
        
        // Step 1: Buy on cheaper exchange
        print("1️⃣ Buying on \(details.buyExchange)...")
        await simulateDelay(0.5)
        
        // Step 2: Transfer to expensive exchange (if needed)
        if opportunity.executionTime > 60 {
            print("2️⃣ Transferring between exchanges...")
            await simulateDelay(2.0)
        }
        
        // Step 3: Sell on expensive exchange
        print("3️⃣ Selling on \(details.sellExchange)...")
        await simulateDelay(0.5)
        
        // Record profit
        let actualProfit = opportunity.estimatedProfit * Double.random(in: 0.8...1.1) // 80-110% of estimate
        recordProfit(actualProfit, from: opportunity)
        
        print("✅ Arbitrage Complete! Actual Profit: $\(actualProfit)")
    }
    
    private func executeTriangleArbitrage(_ opportunity: ArbitrageOpportunity) async {
        print("🔺 Executing Triangle Arbitrage...")
        
        for (index, pair) in opportunity.pairs.enumerated() {
            print("\(index + 1)️⃣ Trading \(pair)...")
            await simulateDelay(0.3)
        }
        
        let actualProfit = opportunity.estimatedProfit * Double.random(in: 0.7...0.9)
        recordProfit(actualProfit, from: opportunity)
    }
    
    private func executeStatisticalArbitrage(_ opportunity: ArbitrageOpportunity) async {
        print("📊 Executing Statistical Arbitrage...")
        print("Waiting for mean reversion...")
        
        await simulateDelay(5.0) // Longer wait for statistical arb
        
        let actualProfit = opportunity.estimatedProfit * Double.random(in: 0.5...0.8)
        recordProfit(actualProfit, from: opportunity)
    }
    
    // MARK: - Helper Methods
    private func fetchPrice(for pair: String, on exchange: SupportedExchange) async -> Double {
        // Simulate price fetching with realistic variations
        let basePrice: Double
        switch pair {
        case "BTCUSDT": basePrice = 45000
        case "ETHUSDT": basePrice = 3200
        case "ADAUSDT": basePrice = 0.65
        case "DOTUSDT": basePrice = 8.5
        case "LINKUSDT": basePrice = 15.2
        default: basePrice = 100
        }
        
        // Add exchange-specific variation
        let variation = Double.random(in: -0.005...0.005) // ±0.5%
        return basePrice * (1 + variation)
    }
    
    private func fetchVolume(for pair: String, on exchange: SupportedExchange) async -> Double {
        // Simulate volume data
        return Double.random(in: 100000...10000000)
    }
    
    private func fetchTrianglePrices(pairs: [String], exchange: SupportedExchange) async -> [String: Double] {
        var prices: [String: Double] = [:]
        for pair in pairs {
            prices[pair] = await fetchPrice(for: pair, on: exchange)
        }
        return prices
    }
    
    private func calculateTriangleArbitrage(prices: [String: Double], pairs: [String], exchange: SupportedExchange) -> ArbitrageOpportunity? {
        guard pairs.count == 3,
              let price1 = prices[pairs[0]],
              let price2 = prices[pairs[1]],
              let price3 = prices[pairs[2]] else { return nil }
        
        // Calculate if profitable after going through all three trades
        let startAmount = 1.0
        let step1 = startAmount / price1
        let step2 = step1 / price2
        let finalAmount = step2 * price3
        
        let profitPercent = (finalAmount - startAmount) * 100
        
        if profitPercent > 0.3 { // More than 0.3% profit
            return ArbitrageOpportunity(
                type: .triangle,
                exchanges: [exchange],
                pairs: pairs,
                estimatedProfit: profitPercent * 100, // $100 per 1% on $10k
                riskScore: 0.2,
                executionTime: 3,
                timestamp: Date()
            )
        }
        
        return nil
    }
    
    private func calculatePairDivergence(pair1: String, pair2: String) async -> Double {
        // Calculate z-score of price ratio divergence
        return Double.random(in: -3...3)
    }
    
    private func estimateTransferTime(from: SupportedExchange, to: SupportedExchange) -> TimeInterval {
        if from == to { return 0 }
        
        // Estimate transfer times in seconds
        switch (from, to) {
        case (.binance, .kucoin), (.kucoin, .binance):
            return 300 // 5 minutes
        case (.binance, .bybit), (.bybit, .binance):
            return 600 // 10 minutes
        default:
            return 900 // 15 minutes
        }
    }
    
    private func calculateRiskScore(transferTime: TimeInterval, volatility: Double) -> Double {
        let timeRisk = min(transferTime / 3600, 1.0) // Risk increases with time
        let volatilityRisk = min(volatility * 10, 1.0)
        return (timeRisk + volatilityRisk) / 2
    }
    
    private func recordProfit(_ profit: Double, from opportunity: ArbitrageOpportunity) {
        totalProfit += profit
        
        let trade = ArbitrageTrade(
            opportunity: opportunity,
            actualProfit: profit,
            executionTime: Date(),
            success: profit > 0
        )
        
        profitHistory.append(trade)
        
        // Update performance
        performance = min(100, performance + (profit > 0 ? 1 : -2))
    }
    
    private func simulateDelay(_ seconds: Double) async {
        try? await Task.sleep(nanoseconds: UInt64(seconds * 1_000_000_000))
    }
}

// MARK: - Supporting Types
struct ArbitrageOpportunity: Identifiable {
    let id = UUID()
    let type: ArbitrageType
    let exchanges: [SupportedExchange]
    let pairs: [String]
    let estimatedProfit: Double
    let riskScore: Double // 0-1, lower is better
    let executionTime: TimeInterval // seconds
    let timestamp: Date
    var details: ArbitrageDetails?
}

enum ArbitrageType {
    case crossExchange
    case triangle
    case statistical
}

struct ArbitrageDetails {
    let buyExchange: SupportedExchange
    let sellExchange: SupportedExchange
    let buyPrice: Double
    let sellPrice: Double
    let amount: Double
}

struct ActiveArbitrage: Identifiable {
    let id = UUID()
    let opportunity: ArbitrageOpportunity
    var status: ArbitrageStatus
    let startTime: Date
    var endTime: Date?
}

enum ArbitrageStatus {
    case executing
    case completed
    case failed
    case cancelled
}

struct ArbitrageTrade: Identifiable {
    let id = UUID()
    let opportunity: ArbitrageOpportunity
    let actualProfit: Double
    let executionTime: Date
    let success: Bool
} 