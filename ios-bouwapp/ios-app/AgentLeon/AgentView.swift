import SwiftUI

struct AgentView: View {
    @ObservedObject var agent: Agent
    @State private var isExpanded = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Main Agent Card
            <PERSON>(action: { 
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    isExpanded.toggle()
                }
            }) {
                agentCardContent
            }
            .buttonStyle(PlainButtonStyle())
            
            // Expanded Details
            if isExpanded {
                expandedContent
                    .transition(.asymmetric(
                        insertion: .opacity.combined(with: .move(edge: .top)),
                        removal: .opacity.combined(with: .move(edge: .top))
                    ))
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black.opacity(0.4))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(borderColor, lineWidth: 2)
                )
        )
        .shadow(color: borderColor.opacity(0.3), radius: 8, x: 0, y: 4)
    }
    
    private var agentCardContent: some View {
        VStack(spacing: 16) {
            // Header Row
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(agent.name)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    if let task = agent.currentTask {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Current Task:")
                                .font(.caption)
                                .foregroundColor(.yellow)
                            
                            Text(task.name)
                                .font(.body)
                                .foregroundColor(.yellow)
                                .fontWeight(.medium)
                        }
                    }
                }
                
                Spacer()
                
                statusBadge
            }
            
            // Performance Section (only show if agent is active or has task)
            if agent.status == .active || agent.currentTask != nil {
                performanceSection
            }
        }
        .padding(20)
    }
    
    private var expandedContent: some View {
        VStack(spacing: 16) {
            Divider()
                .background(borderColor.opacity(0.5))
            
            // Agent Details
            VStack(alignment: .leading, spacing: 12) {
                // Capabilities
                detailSection(title: "Capabilities", items: agent.capabilities)
                
                // Version & Status
                HStack {
                    VStack(alignment: .leading) {
                        Text("Version")
                            .font(.caption)
                            .foregroundColor(.gray)
                        Text(agent.version)
                            .font(.body)
                            .foregroundColor(.white)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing) {
                        Text("Last Activity")
                            .font(.caption)
                            .foregroundColor(.gray)
                        Text(timeAgoString(from: agent.lastActivity))
                            .font(.body)
                            .foregroundColor(.white)
                    }
                }
                
                // Active Rules Count
                HStack {
                    Text("Active Rules")
                        .font(.caption)
                        .foregroundColor(.gray)
                    
                    Spacer()
                    
                    Text("\(agent.rules.filter { $0.isActive }.count)/\(agent.rules.count)")
                        .font(.body)
                        .foregroundColor(.white)
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 20)
        }
    }
    
    private var statusBadge: some View {
        Text(agent.status.rawValue)
            .font(.caption)
            .fontWeight(.bold)
            .foregroundColor(statusTextColor)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(statusBackgroundColor)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(borderColor, lineWidth: 1)
            )
    }
    
    private var performanceSection: some View {
        VStack(spacing: 8) {
            HStack {
                Text("Performance")
                    .font(.caption)
                    .foregroundColor(.gray)
                
                Spacer()
                
                Text("\(Int(agent.performance))%")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            
            // Performance Bar
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // Background
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color.gray.opacity(0.3))
                        .frame(height: 8)
                    
                    // Progress
                    RoundedRectangle(cornerRadius: 4)
                        .fill(performanceGradient)
                        .frame(width: geometry.size.width * (agent.performance / 100), height: 8)
                        .animation(.easeInOut(duration: 0.5), value: agent.performance)
                }
            }
            .frame(height: 8)
        }
    }
    
    private func detailSection(title: String, items: [String]) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.caption)
                .foregroundColor(.gray)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                ForEach(items, id: \.self) { item in
                    Text(item)
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.blue.opacity(0.2))
                        .cornerRadius(8)
                }
            }
        }
    }
    
    private func timeAgoString(from date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }
    
    // MARK: - Computed Properties
    private var borderColor: Color {
        switch agent.status {
        case .active:
            return .orange
        case .thinking:
            return .blue
        case .error:
            return .red
        case .idle:
            return .gray
        }
    }
    
    private var statusBackgroundColor: Color {
        switch agent.status {
        case .active:
            return .orange.opacity(0.2)
        case .thinking:
            return .blue.opacity(0.2)
        case .error:
            return .red.opacity(0.2)
        case .idle:
            return .gray.opacity(0.2)
        }
    }
    
    private var statusTextColor: Color {
        switch agent.status {
        case .active:
            return .orange
        case .thinking:
            return .blue
        case .error:
            return .red
        case .idle:
            return .gray
        }
    }
    
    private var performanceGradient: LinearGradient {
        if agent.performance > 80 {
            return LinearGradient(colors: [.green, .mint], startPoint: .leading, endPoint: .trailing)
        } else if agent.performance > 60 {
            return LinearGradient(colors: [.yellow, .orange], startPoint: .leading, endPoint: .trailing)
        } else {
            return LinearGradient(colors: [.red, .pink], startPoint: .leading, endPoint: .trailing)
        }
    }
}

#Preview {
    ZStack {
        Color.black.ignoresSafeArea()
        
        VStack(spacing: 20) {
            AgentView(agent: Agent(name: "Agent 1", status: .active, performance: 75))
            AgentView(agent: Agent(name: "Agent 2", status: .thinking, performance: 90))
            AgentView(agent: Agent(name: "Agent 3", status: .error, performance: 45))
        }
        .padding()
    }
    .preferredColorScheme(.dark)
} 