import SwiftUI

struct AgentDashboardView: View {
    @StateObject private var agentManager = AgentManager()
    @State private var commandText = ""
    @State private var showingCommandInput = false
    
    var body: some View {
        ZStack {
            // Background
            Color.black.ignoresSafeArea()
            
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    Text("AI Trading Assistant")
                        .font(.title2)
                        .foregroundColor(.white)
                        .padding(.top)
                    
                    // Agent Cards
                    LazyVStack(spacing: 16) {
                        ForEach(Array(agentManager.agents.enumerated()), id: \.element.id) { index, agent in
                            AgentCardView(agent: agent, agentNumber: index + 1)
                        }
                    }
                    .padding(.horizontal)
                    
                    // Command Input Section
                    VStack(spacing: 12) {
                        HStack {
                            TextField("Enter agent command...", text: $commandText)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .foregroundColor(.white)
                                .background(Color.gray.opacity(0.2))
                                .cornerRadius(8)
                            
                            Button(action: {
                                // Voice input button
                                showingCommandInput.toggle()
                            }) {
                                Image(systemName: "mic.fill")
                                    .foregroundColor(.white)
                                    .frame(width: 44, height: 44)
                                    .background(Color.red)
                                    .cornerRadius(8)
                            }
                        }
                        .padding(.horizontal)
                        
                        if !commandText.isEmpty {
                            Button("Execute Command") {
                                agentManager.executeGlobalCommand(commandText)
                                commandText = ""
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .cornerRadius(8)
                            .padding(.horizontal)
                        }
                    }
                    .padding(.bottom, 20)
                }
            }
        }
        .navigationTitle("Agent Dashboard")
        .navigationBarTitleDisplayMode(.inline)
    }
}

struct AgentCardView: View {
    let agent: Agent
    let agentNumber: Int
    @State private var isAnimating = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header with status
            HStack {
                Text("Agent \(agentNumber)")
                    .font(.headline)
                    .foregroundColor(.white)
                    .fontWeight(.bold)
                
                Spacer()
                
                StatusBadge(status: agent.status)
            }
            
            // Current task (if active)
            if agent.status == .active && agent.currentTask != nil {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Current Task:")
                        .font(.caption)
                        .foregroundColor(.yellow)
                        .fontWeight(.medium)
                    
                    Text(agent.currentTask?.title ?? "Analyzing data")
                        .font(.subheadline)
                        .foregroundColor(.white)
                }
            }
            
            // Performance bar (for active and error states)
            if agent.status == .active || agent.status == .error {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text("Performance")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                        
                        Spacer()
                        
                        Text("\(Int(agent.performance))%")
                            .font(.caption)
                            .foregroundColor(.white)
                            .fontWeight(.medium)
                    }
                    
                    ProgressView(value: agent.performance / 100.0)
                        .progressViewStyle(LinearProgressViewStyle(tint: performanceColor))
                        .scaleEffect(x: 1, y: 2, anchor: .center)
                }
            }
        }
        .padding(16)
        .background(cardBackgroundColor)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(borderColor, lineWidth: 2)
        )
        .scaleEffect(isAnimating ? 1.02 : 1.0)
        .onAppear {
            if agent.status == .active || agent.status == .thinking {
                withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
                    isAnimating = true
                }
            }
        }
    }
    
    private var cardBackgroundColor: Color {
        switch agent.status {
        case .active:
            return Color.orange.opacity(0.1)
        case .thinking:
            return Color.blue.opacity(0.1)
        case .error:
            return Color.red.opacity(0.1)
        case .idle:
            return Color.gray.opacity(0.1)
        }
    }
    
    private var borderColor: Color {
        switch agent.status {
        case .active:
            return Color.orange
        case .thinking:
            return Color.blue
        case .error:
            return Color.red
        case .idle:
            return Color.gray
        }
    }
    
    private var performanceColor: Color {
        if agent.performance > 70 {
            return .orange
        } else {
            return .red
        }
    }
}

struct StatusBadge: View {
    let status: AgentStatus
    
    var body: some View {
        Text(status.rawValue)
            .font(.caption)
            .fontWeight(.bold)
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 4)
            .background(status.color)
            .cornerRadius(12)
    }
}

#Preview {
    NavigationView {
        AgentDashboardView()
    }
}
