import Foundation

// MARK: - App Configuration
struct AppConfig {
    static let appName = "QuantumAgents"
    static let version = "1.0.0"
    static let bundleIdentifier = "com.roustam.quantumagents"
    
    // MARK: - Feature Flags
    struct Features {
        static let agentCollaboration = true
        static let realTimeAnalysis = true
        static let advancedML = false // Disabled for performance
        static let blockchainAnalysis = false // Disabled for performance
        static let multiExchangeTrading = false // Disabled for demo
    }
    
    // MARK: - UI Configuration
    struct UI {
        static let animationDuration: Double = 2.0
        static let maxAgentNodes = 4
        static let refreshInterval: TimeInterval = 1.0
    }
    
    // MARK: - Performance Settings
    struct Performance {
        static let maxConcurrentOperations = 3
        static let cacheSize = 100
        static let backgroundTaskTimeout: TimeInterval = 30.0
    }
    
    // MARK: - Demo Data
    struct Demo {
        static let sampleAgentNames = ["Agent 1", "Agent 2", "Agent 3", "Agent 4"]
        static let sampleColors: [String] = ["orange", "blue", "green", "purple"]
        static let samplePerformance: [Double] = [85.5, 92.3, 78.9, 88.1]
    }
}

// MARK: - Environment Configuration
enum Environment {
    case development
    case staging
    case production
    
    static var current: Environment {
        #if DEBUG
        return .development
        #else
        return .production
        #endif
    }
    
    var baseURL: String {
        switch self {
        case .development:
            return "https://api-dev.quantumagents.com"
        case .staging:
            return "https://api-staging.quantumagents.com"
        case .production:
            return "https://api.quantumagents.com"
        }
    }
    
    var enableLogging: Bool {
        switch self {
        case .development, .staging:
            return true
        case .production:
            return false
        }
    }
}
