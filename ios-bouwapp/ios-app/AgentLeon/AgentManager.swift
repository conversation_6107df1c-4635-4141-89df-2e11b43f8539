import Foundation
import Combine

class AgentManager: ObservableObject {
    @Published var agents: [Agent] = []
    @Published var globalPerformance: Double = 0.0
    @Published var systemStatus: String = "Online"
    @Published var commandHistory: [String] = []
    
    // Crypto Trading Agent Reference
    var cryptoAgent: CryptoTradingAgent? {
        return agents.compactMap { $0 as? CryptoTradingAgent }.first
    }
    
    // New Agent References - Temporarily commented out for build
    /*
    var riskManagerAgent: RiskManagerAgent? {
        return agents.compactMap { $0 as? RiskManagerAgent }.first
    }

    var arbitrageAgent: ArbitrageAgent? {
        return agents.compactMap { $0 as? ArbitrageAgent }.first
    }

    var mlAgent: MachineLearningAgent? {
        return agents.compactMap { $0 as? MachineLearningAgent }.first
    }

    var voiceAgent: VoiceCommandAgent? {
        return agents.compactMap { $0 as? VoiceCommandAgent }.first
    }

    var realTimeService: RealTimeDataService?
    var smartAutomation: SmartAutomationAgent?

    var complianceAgent: NederlandseComplianceAgent? {
        return agents.compactMap { $0 as? NederlandseComplianceAgent }.first
    }
    */
    
    private var cancellables = Set<AnyCancellable>()
    private var performanceTimer: Timer?
    
    init() {
        setupDefaultAgents()
        startPerformanceMonitoring()
    }
    
    deinit {
        performanceTimer?.invalidate()
    }
    
    // MARK: - Setup
    private func setupDefaultAgents() {
        let agent1 = Agent(
            name: "Agent 1",
            status: .active,
            performance: 75.0,
            capabilities: ["Data Analysis", "Pattern Recognition", "Machine Learning"],
            version: "2.1.0"
        )
        agent1.currentTask = AgentTask.sampleTasks[0]
        
        let agent2 = Agent(
            name: "Agent 2",
            status: .thinking,
            performance: 90.0,
            capabilities: ["Natural Language Processing", "Text Analysis"],
            version: "1.8.5"
        )
        
        let agent3 = Agent(
            name: "Agent 3",
            status: .error,
            performance: 45.0,
            capabilities: ["Image Processing", "Computer Vision"],
            version: "1.5.2"
        )
        
        // Agent 4 is now a Crypto Trading Agent
        let cryptoAgent = CryptoTradingAgent(
            name: "Agent 4 - Crypto Trader",
            status: .idle,
            performance: 100.0,
            capabilities: ["Crypto Trading", "Market Analysis", "Risk Management"],
            version: "3.0.1"
        )
        
        // Agent 5 - Risk Manager
        let riskManager = RiskManagerAgent(
            name: "Agent 5 - Risk Manager",
            status: .idle,
            performance: 100.0,
            version: "1.0.0"
        )
        
        // Agent 6 - Arbitrage Hunter
        let arbitrageHunter = ArbitrageAgent(
            name: "Agent 6 - Arbitrage Hunter",
            status: .idle,
            performance: 100.0,
            version: "1.0.0"
        )
        
        // Agent 7 - ML Predictor
        let mlAgent = MachineLearningAgent(
            name: "Agent 7 - ML Predictor",
            status: .idle,
            performance: 100.0,
            version: "1.0.0"
        )
        
        // Agent 8 - Voice Commander
        let voiceAgent = VoiceCommandAgent(
            name: "Agent 8 - Voice Commander",
            status: .idle,
            performance: 100.0,
            version: "1.0.0"
        )
        voiceAgent.setAgentManager(self)
        
        // Agent 9 - Smart Automation
        let automationAgent = SmartAutomationAgent(
            name: "Agent 9 - Smart Automation",
            status: .idle,
            performance: 100.0,
            version: "1.0.0"
        )
        
        // Agent 10 - Nederlandse Compliance
        let complianceAgent = NederlandseComplianceAgent(
            name: "Agent 10 - Nederlandse Compliance",
            status: .idle,
            performance: 100.0,
            version: "1.0.0"
        )
        
        agents = [agent1, agent2, agent3, cryptoAgent, riskManager, arbitrageHunter, mlAgent, voiceAgent, automationAgent, complianceAgent]
        
        // Initialize services
        realTimeService = RealTimeDataService()
        smartAutomation = automationAgent
        
        updateGlobalPerformance()
    }
    
    private func startPerformanceMonitoring() {
        performanceTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { _ in
            self.updateAgentPerformances()
            self.updateGlobalPerformance()
        }
    }
    
    // MARK: - Agent Management
    func addAgent(name: String) {
        let newAgent = Agent(name: name)
        agents.append(newAgent)
        updateGlobalPerformance()
    }
    
    func removeAgent(_ agent: Agent) {
        agents.removeAll { $0.id == agent.id }
        updateGlobalPerformance()
    }
    
    func getAgent(by name: String) -> Agent? {
        return agents.first { $0.name == name }
    }
    
    // MARK: - Performance Management
    private func updateAgentPerformances() {
        for agent in agents {
            agent.updatePerformance()
        }
    }
    
    private func updateGlobalPerformance() {
        guard !agents.isEmpty else {
            globalPerformance = 0.0
            return
        }
        
        let total = agents.reduce(0) { $0 + $1.performance }
        globalPerformance = total / Double(agents.count)
        
        // Update system status based on performance
        if globalPerformance > 80 {
            systemStatus = "Optimal"
        } else if globalPerformance > 60 {
            systemStatus = "Good"
        } else if globalPerformance > 40 {
            systemStatus = "Warning"
        } else {
            systemStatus = "Critical"
        }
    }
    
    // MARK: - Command Processing
    func executeGlobalCommand(_ command: String) {
        commandHistory.append(command)
        
        // Keep only last 10 commands
        if commandHistory.count > 10 {
            commandHistory.removeFirst()
        }
        
        let lowercaseCommand = command.lowercased()
        
        switch lowercaseCommand {
        case let cmd where cmd.contains("all agents"):
            executeCommandOnAllAgents(command)
        case let cmd where cmd.contains("agent"):
            executeCommandOnSpecificAgent(command)
        case "status":
            printSystemStatus()
        case "restart all":
            restartAllAgents()
        case "optimize":
            optimizeAgentPerformance()
        default:
            executeCommandOnAvailableAgent(command)
        }
    }
    
    private func executeCommandOnAllAgents(_ command: String) {
        for agent in agents {
            agent.processCommand(command)
        }
    }
    
    private func executeCommandOnSpecificAgent(_ command: String) {
        // Extract agent name from command
        let components = command.split(separator: " ")
        if let agentIndex = components.firstIndex(of: "agent"),
           agentIndex + 1 < components.count,
           let agentNumber = Int(components[agentIndex + 1]),
           agentNumber > 0 && agentNumber <= agents.count {
            agents[agentNumber - 1].processCommand(command)
        }
    }
    
    private func executeCommandOnAvailableAgent(_ command: String) {
        // Find the best available agent for the command
        let availableAgents = agents.filter { $0.status == .idle || $0.status == .thinking }
        
        if let bestAgent = availableAgents.max(by: { $0.performance < $1.performance }) {
            bestAgent.processCommand(command)
        } else if let anyAgent = agents.first {
            anyAgent.processCommand(command)
        }
    }
    
    // MARK: - System Operations
    private func printSystemStatus() {
        print("=== AGENT LEON SYSTEM STATUS ===")
        print("Global Performance: \(Int(globalPerformance))%")
        print("System Status: \(systemStatus)")
        print("Active Agents: \(agents.count)")
        
        for (index, agent) in agents.enumerated() {
            print("Agent \(index + 1): \(agent.status.rawValue) - \(Int(agent.performance))%")
        }
    }
    
    private func restartAllAgents() {
        for agent in agents {
            agent.updateStatus(to: .idle)
            agent.performance = 100.0
            agent.currentTask = nil
        }
        updateGlobalPerformance()
    }
    
    private func optimizeAgentPerformance() {
        for agent in agents {
            agent.performance = min(100.0, agent.performance + 10.0)
        }
        updateGlobalPerformance()
    }
    
    // MARK: - Agent Coordination
    func coordinateAgents(for task: AgentTask) {
        // Find the best agent for the task based on capabilities and performance
        let suitableAgents = agents.filter { agent in
            agent.status == .idle && 
            agent.capabilities.contains { capability in
                task.type.lowercased().contains(capability.lowercased().replacingOccurrences(of: " ", with: "_"))
            }
        }
        
        if let bestAgent = suitableAgents.max(by: { $0.performance < $1.performance }) {
            bestAgent.assignTask(task)
        } else if let anyIdleAgent = agents.first(where: { $0.status == .idle }) {
            anyIdleAgent.assignTask(task)
        }
    }
    
    func shareInformationBetweenAgents() {
        // Implement information sharing logic
        for agent in agents {
            if let collaborationRule = agent.rules.first(where: { $0.name == "Collaboration Protocol" && $0.isActive }) {
                agent.executeRule(collaborationRule)
            }
        }
    }
} 