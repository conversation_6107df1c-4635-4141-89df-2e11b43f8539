import Foundation

// MARK: - Market Data Service
class MarketDataService: ObservableObject {
    @Published var apiConfigurations: [MarketDataAPI] = []
    @Published var isRealTimeEnabled = false
    @Published var lastUpdateTime: Date?
    @Published var marketSentiment: MarketSentiment = MarketSentiment()
    
    private let urlSession = URLSession.shared
    
    init() {
        setupDefaultAPIs()
    }
    
    // MARK: - API Configurations
    private func setupDefaultAPIs() {
        apiConfigurations = [
            // Free APIs
            MarketDataAPI(
                name: "CoinGecko",
                baseURL: "https://api.coingecko.com/api/v3",
                apiKey: "", // Free tier - no key needed
                isEnabled: true,
                features: [.prices, .marketCap, .volume, .trending],
                rateLimitPerMinute: 30
            ),
            
            MarketDataAPI(
                name: "CoinCap",
                baseURL: "https://api.coincap.io/v2",
                apiKey: "", // Free tier
                isEnabled: true,
                features: [.prices, .marketCap, .volume],
                rateLimitPerMinute: 200
            ),
            
            MarketDataAPI(
                name: "Binance Public",
                baseURL: "https://api.binance.com/api/v3",
                apiKey: "", // Public endpoints
                isEnabled: true,
                features: [.prices, .volume, .orderBook, .klines],
                rateLimitPerMinute: 1200
            ),
            
            // Premium APIs (require API keys)
            MarketDataAPI(
                name: "CryptoCompare",
                baseURL: "https://min-api.cryptocompare.com/data",
                apiKey: "", // Free tier available
                isEnabled: false,
                features: [.prices, .volume, .news, .social],
                rateLimitPerMinute: 100
            ),
            
            MarketDataAPI(
                name: "Alpha Vantage",
                baseURL: "https://www.alphavantage.co/query",
                apiKey: "", // Free tier: 5 calls/minute
                isEnabled: false,
                features: [.technicalIndicators, .fundamentals],
                rateLimitPerMinute: 5
            ),
            
            MarketDataAPI(
                name: "TradingView",
                baseURL: "https://scanner.tradingview.com",
                apiKey: "", // Some endpoints are free
                isEnabled: false,
                features: [.technicalIndicators, .screening],
                rateLimitPerMinute: 60
            ),
            
            MarketDataAPI(
                name: "Fear & Greed Index",
                baseURL: "https://api.alternative.me/fng",
                apiKey: "", // Free
                isEnabled: true,
                features: [.sentiment],
                rateLimitPerMinute: 10
            ),
            
            MarketDataAPI(
                name: "NewsAPI",
                baseURL: "https://newsapi.org/v2",
                apiKey: "", // Free tier: 1000 requests/month
                isEnabled: false,
                features: [.news, .sentiment],
                rateLimitPerMinute: 500
            )
        ]
    }
    
    // MARK: - Comprehensive Market Analysis
    func getComprehensiveMarketData(symbol: String) async throws -> MarketAnalysisData {
        var marketData = MarketAnalysisData(
            symbol: symbol,
            currentPrice: 0,
            priceChange24h: 0,
            volume24h: 0,
            marketCap: nil,
            rsi: nil,
            macd: nil,
            sma20: nil,
            sma50: nil,
            technicalIndicators: [:],
            newsSentiment: nil,
            fearGreedIndex: nil
        )
        
        // Parallel API calls for efficiency
        async let priceData = fetchPriceData(symbol: symbol)
        async let technicalData = fetchTechnicalIndicators(symbol: symbol)
        async let sentimentData = fetchSentimentData(symbol: symbol)
        async let newsData = fetchNewsData(symbol: symbol)
        async let fearGreedData = fetchFearGreedIndex()
        
        // Combine all data
        do {
            let price = try await priceData
            let technical = try await technicalData
            let sentiment = try await sentimentData
            let news = try await newsData
            let fearGreed = try await fearGreedData
            
            marketData = MarketAnalysisData(
                symbol: symbol,
                currentPrice: price.price,
                priceChange24h: price.change24h,
                volume24h: price.volume24h,
                marketCap: price.marketCap,
                rsi: technical.rsi,
                macd: technical.macd,
                sma20: technical.sma20,
                sma50: technical.sma50,
                technicalIndicators: technical.indicators,
                newsSentiment: sentiment.overall,
                fearGreedIndex: fearGreed
            )
            
        } catch {
            print("Error fetching market data: \(error)")
            // Return mock data for development
            marketData = generateMockMarketData(symbol: symbol)
        }
        
        await MainActor.run {
            lastUpdateTime = Date()
        }
        
        return marketData
    }
    
    // MARK: - Price Data (CoinGecko)
    private func fetchPriceData(symbol: String) async throws -> PriceData {
        let api = apiConfigurations.first { $0.name == "CoinGecko" && $0.isEnabled }
        guard let api = api else { throw MarketDataError.apiNotConfigured }
        
        let coinId = symbol.lowercased().replacingOccurrences(of: "usdt", with: "")
        let url = "\(api.baseURL)/simple/price?ids=\(coinId)&vs_currencies=usd&include_24hr_change=true&include_24hr_vol=true&include_market_cap=true"
        
        guard let requestURL = URL(string: url) else {
            throw MarketDataError.invalidURL
        }
        
        let (data, _) = try await urlSession.data(from: requestURL)
        
        // Parse CoinGecko response
        if let json = try JSONSerialization.jsonObject(with: data) as? [String: [String: Double]],
           let coinData = json[coinId] {
            
            return PriceData(
                price: coinData["usd"] ?? 0,
                change24h: coinData["usd_24h_change"] ?? 0,
                volume24h: coinData["usd_24h_vol"] ?? 0,
                marketCap: coinData["usd_market_cap"]
            )
        }
        
        throw MarketDataError.invalidResponse
    }
    
    // MARK: - Technical Indicators (TradingView Scanner)
    private func fetchTechnicalIndicators(symbol: String) async throws -> TechnicalData {
        // TradingView Scanner API call
        let api = apiConfigurations.first { $0.name == "TradingView" }
        guard let api = api else {
            // Return mock technical data
            return TechnicalData(
                rsi: Double.random(in: 20...80),
                macd: Double.random(in: -50...50),
                sma20: Double.random(in: 40000...50000),
                sma50: Double.random(in: 35000...55000),
                indicators: [
                    "BB_UPPER": Double.random(in: 45000...55000),
                    "BB_LOWER": Double.random(in: 35000...45000),
                    "STOCH_K": Double.random(in: 0...100),
                    "STOCH_D": Double.random(in: 0...100)
                ]
            )
        }
        
        // Implement TradingView API call
        // For now, return mock data
        return TechnicalData(
            rsi: Double.random(in: 20...80),
            macd: Double.random(in: -50...50),
            sma20: Double.random(in: 40000...50000),
            sma50: Double.random(in: 35000...55000),
            indicators: [:]
        )
    }
    
    // MARK: - Sentiment Analysis
    private func fetchSentimentData(symbol: String) async throws -> SentimentData {
        // Combine multiple sentiment sources
        async let fearGreed = fetchFearGreedIndex()
        async let news = fetchNewsData(symbol: symbol)
        
        let fearGreedValue = try await fearGreed
        let newsData = try await news
        
        // Calculate overall sentiment
        var overall = "Neutral"
        if fearGreedValue > 75 { overall = "Extremely Greedy" }
        else if fearGreedValue > 55 { overall = "Greedy" }
        else if fearGreedValue < 25 { overall = "Extremely Fearful" }
        else if fearGreedValue < 45 { overall = "Fearful" }
        
        return SentimentData(
            overall: overall,
            fearGreed: fearGreedValue,
            newsScore: newsData.sentimentScore,
            socialScore: 0.5 // Would integrate social media APIs
        )
    }
    
    // MARK: - Fear & Greed Index
    private func fetchFearGreedIndex() async throws -> Int {
        guard let url = URL(string: "https://api.alternative.me/fng/") else {
            throw MarketDataError.invalidURL
        }
        
        let (data, _) = try await urlSession.data(from: url)
        
        struct FearGreedResponse: Codable {
            let data: [FearGreedData]
            
            struct FearGreedData: Codable {
                let value: String
            }
        }
        
        let decoder = JSONDecoder()
        let response = try decoder.decode(FearGreedResponse.self, from: data)
        
        return Int(response.data.first?.value ?? "50") ?? 50
    }
    
    // MARK: - News Data
    private func fetchNewsData(symbol: String) async throws -> NewsData {
        // Would integrate with NewsAPI or CryptoCompare news
        // For now, return mock data
        return NewsData(
            headlines: [
                "Bitcoin shows strong momentum",
                "Market analysts predict bullish trend",
                "Institutional adoption continues"
            ],
            sentimentScore: Double.random(in: -1...1),
            totalArticles: Int.random(in: 50...200)
        )
    }
    
    // MARK: - Mock Data Generation
    private func generateMockMarketData(symbol: String) -> MarketAnalysisData {
        return MarketAnalysisData(
            symbol: symbol,
            currentPrice: Double.random(in: 40000...60000),
            priceChange24h: Double.random(in: -10...10),
            volume24h: Double.random(in: 1000000...5000000),
            marketCap: Double.random(in: 800000000000...1200000000000),
            rsi: Double.random(in: 20...80),
            macd: Double.random(in: -500...500),
            sma20: Double.random(in: 45000...55000),
            sma50: Double.random(in: 40000...60000),
            technicalIndicators: [
                "bollinger_upper": Double.random(in: 50000...60000),
                "bollinger_lower": Double.random(in: 40000...50000),
                "stochastic": Double.random(in: 0...100)
            ],
            newsSentiment: ["Bullish", "Bearish", "Neutral"].randomElement(),
            fearGreedIndex: Int.random(in: 0...100)
        )
    }
    
    // MARK: - API Management
    func updateAPIConfiguration(_ api: MarketDataAPI) {
        if let index = apiConfigurations.firstIndex(where: { $0.id == api.id }) {
            apiConfigurations[index] = api
        }
    }
    
    func testAPIConnection(_ api: MarketDataAPI) async -> Bool {
        // Test connection to the API
        guard let url = URL(string: api.baseURL) else { return false }
        
        do {
            let (_, response) = try await urlSession.data(from: url)
            if let httpResponse = response as? HTTPURLResponse {
                return httpResponse.statusCode == 200
            }
        } catch {
            print("API test failed for \(api.name): \(error)")
        }
        
        return false
    }
}

// MARK: - Supporting Models
struct MarketDataAPI: Identifiable {
    let id = UUID()
    let name: String
    let baseURL: String
    var apiKey: String
    var isEnabled: Bool
    let features: [APIFeature]
    let rateLimitPerMinute: Int
    
    enum APIFeature {
        case prices, volume, marketCap, orderBook, klines
        case technicalIndicators, fundamentals, screening
        case news, social, sentiment, trending
    }
}

struct PriceData {
    let price: Double
    let change24h: Double
    let volume24h: Double
    let marketCap: Double?
}

struct TechnicalData {
    let rsi: Double
    let macd: Double
    let sma20: Double
    let sma50: Double
    let indicators: [String: Double]
}

struct SentimentData {
    let overall: String
    let fearGreed: Int
    let newsScore: Double
    let socialScore: Double
}

struct NewsData {
    let headlines: [String]
    let sentimentScore: Double
    let totalArticles: Int
}

struct MarketSentiment {
    var fearGreedIndex: Int = 50
    var newsScore: Double = 0.0
    var socialScore: Double = 0.0
    var overallSentiment: String = "Neutral"
}

// MARK: - Errors
enum MarketDataError: Error, LocalizedError {
    case apiNotConfigured
    case invalidURL
    case invalidResponse
    case rateLimitExceeded
    case networkError(Error)
    
    var errorDescription: String? {
        switch self {
        case .apiNotConfigured:
            return "API not configured or disabled"
        case .invalidURL:
            return "Invalid API URL"
        case .invalidResponse:
            return "Invalid API response format"
        case .rateLimitExceeded:
            return "API rate limit exceeded"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        }
    }
} 