import SwiftUI

// MARK: - Professional Trading Interface
struct ProfessionalTradingInterface: View {
    @StateObject private var tradingManager = ProfessionalTradingManager()
    @State private var selectedTab = 0
    @State private var showRiskWarning = true
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // Live Trading Tab
            LiveTradingView(tradingManager: tradingManager)
                .tabItem {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                    Text("Live Trading")
                }
                .tag(0)
            
            // Portfolio Management
            PortfolioManagementView(tradingManager: tradingManager)
                .tabItem {
                    Image(systemName: "briefcase.fill")
                    Text("Portfolio")
                }
                .tag(1)
            
            // Risk Management
            RiskManagementView(tradingManager: tradingManager)
                .tabItem {
                    Image(systemName: "shield.fill")
                    Text("Risk")
                }
                .tag(2)
            
            // Analytics
            AdvancedTradingAnalytics()
                .tabItem {
                    Image(systemName: "chart.bar.fill")
                    Text("Analytics")
                }
                .tag(3)
        }
        .accentColor(.orange)
        .background(Color.black)
        .alert("⚠️ Live Trading Warning", isPresented: $showRiskWarning) {
            Button("I Understand") { showRiskWarning = false }
            But<PERSON>("Demo Mode") { 
                showRiskWarning = false
                tradingManager.setDemoMode(true)
            }
        } message: {
            Text("You are about to trade with real money. All trades are final and can result in significant losses. Only trade with money you can afford to lose.")
        }
    }
}

// MARK: - Live Trading View
struct LiveTradingView: View {
    @ObservedObject var tradingManager: ProfessionalTradingManager
    @State private var selectedSymbol = "BTCUSDT"
    @State private var selectedExchange = "binance"
    @State private var orderType = "LIMIT"
    @State private var orderSide = "BUY"
    @State private var quantity = ""
    @State private var price = ""
    @State private var stopLoss = ""
    @State private var takeProfit = ""
    @State private var showAdvancedOrders = false
    
    let orderTypes = ["MARKET", "LIMIT", "STOP_LOSS_LIMIT", "OCO"]
    let exchanges = ["binance", "kucoin", "mexc", "bybit"]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Quick Stats Header
                quickStatsHeader
                
                ScrollView {
                    VStack(spacing: 20) {
                        // Exchange & Symbol Selection
                        exchangeSymbolSection
                        
                        // Current Market Data
                        marketDataSection
                        
                        // Order Placement
                        orderPlacementSection
                        
                        // Open Orders
                        openOrdersSection
                        
                        // Recent Trades
                        recentTradesSection
                    }
                    .padding()
                }
            }
            .background(Color.black)
            .navigationTitle("Live Trading")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { tradingManager.toggleLiveMode() }) {
                        Text(tradingManager.isLiveMode ? "LIVE" : "DEMO")
                            .fontWeight(.bold)
                            .foregroundColor(tradingManager.isLiveMode ? .red : .orange)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(tradingManager.isLiveMode ? .red : .orange, lineWidth: 2)
                            )
                    }
                }
            }
        }
    }
    
    // MARK: - Quick Stats Header
    private var quickStatsHeader: some View {
        HStack {
            VStack(alignment: .leading) {
                Text("Portfolio Value")
                    .font(.caption)
                    .foregroundColor(.gray)
                Text("$\(tradingManager.totalPortfolioValue, specifier: "%.2f")")
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            
            Spacer()
            
            VStack(alignment: .center) {
                Text("24h P&L")
                    .font(.caption)
                    .foregroundColor(.gray)
                Text("\(tradingManager.dailyPnL >= 0 ? "+" : "")$\(tradingManager.dailyPnL, specifier: "%.2f")")
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundColor(tradingManager.dailyPnL >= 0 ? .green : .red)
            }
            
            Spacer()
            
            VStack(alignment: .trailing) {
                Text("Open Positions")
                    .font(.caption)
                    .foregroundColor(.gray)
                Text("\(tradingManager.openPositions.count)")
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundColor(.orange)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
    }
    
    // MARK: - Exchange & Symbol Section
    private var exchangeSymbolSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Market Selection")
                .font(.headline)
                .foregroundColor(.white)
            
            HStack {
                // Exchange Picker
                Picker("Exchange", selection: $selectedExchange) {
                    ForEach(exchanges, id: \.self) { exchange in
                        Text(exchange.capitalized).tag(exchange)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                
                Spacer()
                
                // Symbol Input
                TextField("Symbol", text: $selectedSymbol)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .frame(width: 120)
            }
        }
    }
    
    // MARK: - Market Data Section
    private var marketDataSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Market Data")
                .font(.headline)
                .foregroundColor(.white)
            
            HStack {
                VStack(alignment: .leading) {
                    Text("Price")
                        .font(.caption)
                        .foregroundColor(.gray)
                    Text("$\(tradingManager.currentPrice, specifier: "%.2f")")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                }
                
                Spacer()
                
                VStack(alignment: .center) {
                    Text("24h Change")
                        .font(.caption)
                        .foregroundColor(.gray)
                    Text("\(tradingManager.priceChange24h >= 0 ? "+" : "")\(tradingManager.priceChange24h, specifier: "%.2f")%")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(tradingManager.priceChange24h >= 0 ? .green : .red)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("Volume")
                        .font(.caption)
                        .foregroundColor(.gray)
                    Text("\(formatVolume(tradingManager.volume24h))")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
        }
    }
    
    // MARK: - Order Placement Section
    private var orderPlacementSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Text("Place Order")
                    .font(.headline)
                    .foregroundColor(.white)
                
                Spacer()
                
                Button(action: { showAdvancedOrders.toggle() }) {
                    Text("Advanced")
                        .foregroundColor(.orange)
                }
            }
            
            // Order Type & Side
            HStack {
                Picker("Type", selection: $orderType) {
                    ForEach(orderTypes, id: \.self) { type in
                        Text(type).tag(type)
                    }
                }
                .pickerStyle(MenuPickerStyle())
                .frame(maxWidth: .infinity)
                
                Picker("Side", selection: $orderSide) {
                    Text("BUY").tag("BUY")
                    Text("SELL").tag("SELL")
                }
                .pickerStyle(SegmentedPickerStyle())
                .frame(maxWidth: .infinity)
            }
            
            // Quantity & Price
            HStack {
                VStack(alignment: .leading) {
                    Text("Quantity")
                        .font(.caption)
                        .foregroundColor(.gray)
                    TextField("0.00", text: $quantity)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .keyboardType(.decimalPad)
                }
                
                if orderType != "MARKET" {
                    VStack(alignment: .leading) {
                        Text("Price")
                            .font(.caption)
                            .foregroundColor(.gray)
                        TextField("0.00", text: $price)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .keyboardType(.decimalPad)
                    }
                }
            }
            
            // Advanced Options
            if showAdvancedOrders {
                VStack(spacing: 10) {
                    HStack {
                        VStack(alignment: .leading) {
                            Text("Stop Loss")
                                .font(.caption)
                                .foregroundColor(.gray)
                            TextField("Optional", text: $stopLoss)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .keyboardType(.decimalPad)
                        }
                        
                        VStack(alignment: .leading) {
                            Text("Take Profit")
                                .font(.caption)
                                .foregroundColor(.gray)
                            TextField("Optional", text: $takeProfit)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .keyboardType(.decimalPad)
                        }
                    }
                    
                    // Risk Calculation
                    if !quantity.isEmpty && !price.isEmpty {
                        let orderValue = (Double(quantity) ?? 0) * (Double(price) ?? tradingManager.currentPrice)
                        let riskPercent = (orderValue / tradingManager.totalPortfolioValue) * 100
                        
                        HStack {
                            Text("Order Value: $\(orderValue, specifier: "%.2f")")
                                .foregroundColor(.white)
                            Spacer()
                            Text("Risk: \(riskPercent, specifier: "%.1f")%")
                                .foregroundColor(riskPercent > 5 ? .red : .green)
                        }
                        .font(.caption)
                    }
                }
            }
            
            // Place Order Button
            Button(action: placeOrder) {
                HStack {
                    if tradingManager.isPlacingOrder {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    }
                    
                    Text(tradingManager.isPlacingOrder ? "Placing Order..." : "Place \(orderSide) Order")
                        .fontWeight(.bold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    LinearGradient(
                        colors: orderSide == "BUY" ? [.green, .green.opacity(0.8)] : [.red, .red.opacity(0.8)],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(12)
            }
            .disabled(tradingManager.isPlacingOrder || quantity.isEmpty)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    // MARK: - Open Orders Section
    private var openOrdersSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Open Orders (\(tradingManager.openOrders.count))")
                .font(.headline)
                .foregroundColor(.white)
            
            if tradingManager.openOrders.isEmpty {
                Text("No open orders")
                    .foregroundColor(.gray)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                ForEach(tradingManager.openOrders, id: \.id) { order in
                    orderRow(order)
                }
            }
        }
    }
    
    // MARK: - Recent Trades Section
    private var recentTradesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recent Trades")
                .font(.headline)
                .foregroundColor(.white)
            
            ForEach(tradingManager.recentTrades.prefix(5), id: \.id) { trade in
                tradeRow(trade)
            }
        }
    }
    
    // MARK: - Helper Views
    private func orderRow(_ order: TradingOrder) -> some View {
        HStack {
            VStack(alignment: .leading) {
                Text("\(order.symbol) \(order.side)")
                    .font(.caption)
                    .foregroundColor(order.side == "BUY" ? .green : .red)
                Text("\(order.type) • \(order.quantity, specifier: "%.4f")")
                    .font(.caption2)
                    .foregroundColor(.gray)
            }
            
            Spacer()
            
            VStack(alignment: .trailing) {
                Text("$\(order.price, specifier: "%.2f")")
                    .font(.caption)
                    .foregroundColor(.white)
                Text(order.status)
                    .font(.caption2)
                    .foregroundColor(.orange)
            }
            
            Button(action: { cancelOrder(order) }) {
                Image(systemName: "xmark.circle.fill")
                    .foregroundColor(.red)
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
    
    private func tradeRow(_ trade: CompletedTrade) -> some View {
        HStack {
            VStack(alignment: .leading) {
                Text("\(trade.symbol) \(trade.side)")
                    .font(.caption)
                    .foregroundColor(trade.side == "BUY" ? .green : .red)
                Text(timeAgo(trade.timestamp))
                    .font(.caption2)
                    .foregroundColor(.gray)
            }
            
            Spacer()
            
            VStack(alignment: .trailing) {
                Text("\(trade.quantity, specifier: "%.4f")")
                    .font(.caption)
                    .foregroundColor(.white)
                Text("$\(trade.price, specifier: "%.2f")")
                    .font(.caption2)
                    .foregroundColor(.gray)
            }
            
            VStack(alignment: .trailing) {
                Text("\(trade.pnl >= 0 ? "+" : "")$\(trade.pnl, specifier: "%.2f")")
                    .font(.caption)
                    .foregroundColor(trade.pnl >= 0 ? .green : .red)
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
    
    // MARK: - Helper Functions
    private func placeOrder() {
        let orderData = OrderData(
            symbol: selectedSymbol,
            side: orderSide,
            type: orderType,
            quantity: quantity,
            price: orderType == "MARKET" ? nil : price,
            stopPrice: stopLoss.isEmpty ? nil : stopLoss,
            exchange: selectedExchange
        )
        
        tradingManager.placeOrder(orderData)
    }
    
    private func cancelOrder(_ order: TradingOrder) {
        tradingManager.cancelOrder(order)
    }
    
    private func formatVolume(_ volume: Double) -> String {
        if volume > 1_000_000 {
            return String(format: "%.1fM", volume / 1_000_000)
        } else if volume > 1_000 {
            return String(format: "%.1fK", volume / 1_000)
        } else {
            return String(format: "%.0f", volume)
        }
    }
    
    private func timeAgo(_ timestamp: Date) -> String {
        let interval = Date().timeIntervalSince(timestamp)
        
        if interval < 60 {
            return "\(Int(interval))s ago"
        } else if interval < 3600 {
            return "\(Int(interval / 60))m ago"
        } else {
            return "\(Int(interval / 3600))h ago"
        }
    }
}

// MARK: - Portfolio Management View
struct PortfolioManagementView: View {
    @ObservedObject var tradingManager: ProfessionalTradingManager
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Portfolio Overview
                    portfolioOverview
                    
                    // Asset Allocation
                    assetAllocation
                    
                    // Holdings Detail
                    holdingsDetail
                }
                .padding()
            }
            .background(Color.black)
            .navigationTitle("Portfolio")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    private var portfolioOverview: some View {
        VStack(spacing: 15) {
            Text("Portfolio Overview")
                .font(.headline)
                .foregroundColor(.white)
            
            VStack(spacing: 10) {
                HStack {
                    Text("Total Value")
                        .foregroundColor(.gray)
                    Spacer()
                    Text("$\(tradingManager.totalPortfolioValue, specifier: "%.2f")")
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                }
                
                HStack {
                    Text("Available Balance")
                        .foregroundColor(.gray)
                    Spacer()
                    Text("$\(tradingManager.availableBalance, specifier: "%.2f")")
                        .foregroundColor(.orange)
                }
                
                HStack {
                    Text("Total P&L")
                        .foregroundColor(.gray)
                    Spacer()
                    Text("\(tradingManager.totalPnL >= 0 ? "+" : "")$\(tradingManager.totalPnL, specifier: "%.2f")")
                        .foregroundColor(tradingManager.totalPnL >= 0 ? .green : .red)
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
        }
    }
    
    private var assetAllocation: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Asset Allocation")
                .font(.headline)
                .foregroundColor(.white)
            
            ForEach(tradingManager.holdings, id: \.symbol) { holding in
                HStack {
                    VStack(alignment: .leading) {
                        Text(holding.symbol)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        Text("\(holding.quantity, specifier: "%.6f")")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing) {
                        Text("$\(holding.value, specifier: "%.2f")")
                            .foregroundColor(.white)
                        Text("\(holding.percentage, specifier: "%.1f")%")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }
                .padding(.vertical, 8)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var holdingsDetail: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Holdings Detail")
                .font(.headline)
                .foregroundColor(.white)
            
            ForEach(tradingManager.holdings, id: \.symbol) { holding in
                VStack(spacing: 10) {
                    HStack {
                        Text(holding.symbol)
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        Spacer()
                        
                        Text("$\(holding.currentPrice, specifier: "%.2f")")
                            .foregroundColor(.orange)
                    }
                    
                    HStack {
                        VStack(alignment: .leading) {
                            Text("Quantity")
                                .font(.caption)
                                .foregroundColor(.gray)
                            Text("\(holding.quantity, specifier: "%.6f")")
                                .foregroundColor(.white)
                        }
                        
                        Spacer()
                        
                        VStack(alignment: .center) {
                            Text("Avg. Cost")
                                .font(.caption)
                                .foregroundColor(.gray)
                            Text("$\(holding.averageCost, specifier: "%.2f")")
                                .foregroundColor(.white)
                        }
                        
                        Spacer()
                        
                        VStack(alignment: .trailing) {
                            Text("P&L")
                                .font(.caption)
                                .foregroundColor(.gray)
                            Text("\(holding.pnl >= 0 ? "+" : "")$\(holding.pnl, specifier: "%.2f")")
                                .foregroundColor(holding.pnl >= 0 ? .green : .red)
                        }
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
            }
        }
    }
}

// MARK: - Risk Management View
struct RiskManagementView: View {
    @ObservedObject var tradingManager: ProfessionalTradingManager
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Risk Overview
                    riskOverview
                    
                    // Position Sizing
                    positionSizing
                    
                    // Stop Loss Management
                    stopLossManagement
                    
                    // Risk Limits
                    riskLimits
                }
                .padding()
            }
            .background(Color.black)
            .navigationTitle("Risk Management")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    private var riskOverview: some View {
        VStack(spacing: 15) {
            Text("Risk Overview")
                .font(.headline)
                .foregroundColor(.white)
            
            VStack(spacing: 10) {
                HStack {
                    Text("Portfolio Risk")
                        .foregroundColor(.gray)
                    Spacer()
                    Text("\(tradingManager.portfolioRisk, specifier: "%.1f")%")
                        .foregroundColor(tradingManager.portfolioRisk > 20 ? .red : .orange)
                }
                
                HStack {
                    Text("Max Drawdown")
                        .foregroundColor(.gray)
                    Spacer()
                    Text("\(tradingManager.maxDrawdown, specifier: "%.1f")%")
                        .foregroundColor(.red)
                }
                
                HStack {
                    Text("Sharpe Ratio")
                        .foregroundColor(.gray)
                    Spacer()
                    Text("\(tradingManager.sharpeRatio, specifier: "%.2f")")
                        .foregroundColor(.green)
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
        }
    }
    
    private var positionSizing: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Position Sizing Rules")
                .font(.headline)
                .foregroundColor(.white)
            
            VStack(spacing: 12) {
                HStack {
                    Text("Max Position Size")
                        .foregroundColor(.white)
                    Spacer()
                    Text("\(tradingManager.maxPositionSize, specifier: "%.1f")%")
                        .foregroundColor(.orange)
                }
                
                HStack {
                    Text("Risk per Trade")
                        .foregroundColor(.white)
                    Spacer()
                    Text("\(tradingManager.riskPerTrade, specifier: "%.1f")%")
                        .foregroundColor(.orange)
                }
                
                HStack {
                    Text("Max Open Positions")
                        .foregroundColor(.white)
                    Spacer()
                    Text("\(tradingManager.maxOpenPositions)")
                        .foregroundColor(.orange)
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
        }
    }
    
    private var stopLossManagement: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Stop Loss Management")
                .font(.headline)
                .foregroundColor(.white)
            
            ForEach(tradingManager.openPositions, id: \.id) { position in
                VStack(spacing: 8) {
                    HStack {
                        Text("\(position.symbol) \(position.side)")
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        Spacer()
                        
                        Text("$\(position.currentPrice, specifier: "%.2f")")
                            .foregroundColor(.orange)
                    }
                    
                    HStack {
                        VStack(alignment: .leading) {
                            Text("Stop Loss")
                                .font(.caption)
                                .foregroundColor(.gray)
                            Text("$\(position.stopLoss, specifier: "%.2f")")
                                .foregroundColor(.red)
                        }
                        
                        Spacer()
                        
                        VStack(alignment: .center) {
                            Text("Take Profit")
                                .font(.caption)
                                .foregroundColor(.gray)
                            Text("$\(position.takeProfit, specifier: "%.2f")")
                                .foregroundColor(.green)
                        }
                        
                        Spacer()
                        
                        VStack(alignment: .trailing) {
                            Text("Risk")
                                .font(.caption)
                                .foregroundColor(.gray)
                            Text("\(position.risk, specifier: "%.1f")%")
                                .foregroundColor(position.risk > 5 ? .red : .orange)
                        }
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
            }
        }
    }
    
    private var riskLimits: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Risk Limits")
                .font(.headline)
                .foregroundColor(.white)
            
            VStack(spacing: 12) {
                HStack {
                    Text("Daily Loss Limit")
                        .foregroundColor(.white)
                    Spacer()
                    Text("$\(tradingManager.dailyLossLimit, specifier: "%.2f")")
                        .foregroundColor(.red)
                }
                
                HStack {
                    Text("Weekly Loss Limit")
                        .foregroundColor(.white)
                    Spacer()
                    Text("$\(tradingManager.weeklyLossLimit, specifier: "%.2f")")
                        .foregroundColor(.red)
                }
                
                HStack {
                    Text("Current Daily Loss")
                        .foregroundColor(.gray)
                    Spacer()
                    Text("$\(tradingManager.currentDailyLoss, specifier: "%.2f")")
                        .foregroundColor(tradingManager.currentDailyLoss > tradingManager.dailyLossLimit ? .red : .orange)
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
        }
    }
}

// MARK: - Supporting Data Models
struct OrderData {
    let symbol: String
    let side: String
    let type: String
    let quantity: String
    let price: String?
    let stopPrice: String?
    let exchange: String
}

struct TradingOrder {
    let id: String
    let symbol: String
    let side: String
    let type: String
    let quantity: Double
    let price: Double
    let status: String
    let timestamp: Date
}

struct CompletedTrade {
    let id: String
    let symbol: String
    let side: String
    let quantity: Double
    let price: Double
    let pnl: Double
    let timestamp: Date
}

struct Position {
    let id: String
    let symbol: String
    let side: String
    let quantity: Double
    let entryPrice: Double
    let currentPrice: Double
    let stopLoss: Double
    let takeProfit: Double
    let risk: Double
    let pnl: Double
}

struct Holding {
    let symbol: String
    let quantity: Double
    let averageCost: Double
    let currentPrice: Double
    let value: Double
    let percentage: Double
    let pnl: Double
}

// MARK: - Professional Trading Manager
class ProfessionalTradingManager: ObservableObject {
    @Published var isLiveMode = false
    @Published var isPlacingOrder = false
    @Published var totalPortfolioValue: Double = 125_450.0
    @Published var availableBalance: Double = 45_230.0
    @Published var dailyPnL: Double = 2_850.0
    @Published var totalPnL: Double = 15_670.0
    @Published var currentPrice: Double = 45_250.0
    @Published var priceChange24h: Double = 2.35
    @Published var volume24h: Double = 1_250_000
    @Published var openOrders: [TradingOrder] = []
    @Published var recentTrades: [CompletedTrade] = []
    @Published var openPositions: [Position] = []
    @Published var holdings: [Holding] = []
    
    // Risk Management
    @Published var portfolioRisk: Double = 15.5
    @Published var maxDrawdown: Double = 8.2
    @Published var sharpeRatio: Double = 1.85
    @Published var maxPositionSize: Double = 20.0
    @Published var riskPerTrade: Double = 2.0
    @Published var maxOpenPositions: Int = 5
    @Published var dailyLossLimit: Double = 1000.0
    @Published var weeklyLossLimit: Double = 5000.0
    @Published var currentDailyLoss: Double = 125.0
    
    init() {
        generateMockData()
    }
    
    func setDemoMode(_ demo: Bool) {
        isLiveMode = !demo
    }
    
    func toggleLiveMode() {
        isLiveMode.toggle()
    }
    
    func placeOrder(_ orderData: OrderData) {
        isPlacingOrder = true
        
        // Simulate order placement delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.isPlacingOrder = false
            
            let order = TradingOrder(
                id: UUID().uuidString,
                symbol: orderData.symbol,
                side: orderData.side,
                type: orderData.type,
                quantity: Double(orderData.quantity) ?? 0,
                price: Double(orderData.price ?? "\(self.currentPrice)") ?? self.currentPrice,
                status: "FILLED",
                timestamp: Date()
            )
            
            self.openOrders.append(order)
            
            // Add to recent trades
            let trade = CompletedTrade(
                id: UUID().uuidString,
                symbol: orderData.symbol,
                side: orderData.side,
                quantity: order.quantity,
                price: order.price,
                pnl: Double.random(in: -100...200),
                timestamp: Date()
            )
            
            self.recentTrades.insert(trade, at: 0)
        }
    }
    
    func cancelOrder(_ order: TradingOrder) {
        openOrders.removeAll { $0.id == order.id }
    }
    
    private func generateMockData() {
        // Generate mock holdings
        holdings = [
            Holding(symbol: "BTC", quantity: 0.5, averageCost: 42000, currentPrice: 45250, value: 22625, percentage: 18.0, pnl: 1625),
            Holding(symbol: "ETH", quantity: 15.0, averageCost: 2800, currentPrice: 3150, value: 47250, percentage: 37.7, pnl: 5250),
            Holding(symbol: "ADA", quantity: 5000, averageCost: 0.45, currentPrice: 0.52, value: 2600, percentage: 2.1, pnl: 350),
            Holding(symbol: "USDT", quantity: 52975, averageCost: 1.0, currentPrice: 1.0, value: 52975, percentage: 42.2, pnl: 0)
        ]
        
        // Generate mock positions
        openPositions = [
            Position(id: UUID().uuidString, symbol: "BTCUSDT", side: "LONG", quantity: 0.1, entryPrice: 44800, currentPrice: 45250, stopLoss: 43200, takeProfit: 47000, risk: 3.5, pnl: 45),
            Position(id: UUID().uuidString, symbol: "ETHUSDT", side: "SHORT", quantity: 2.0, entryPrice: 3200, currentPrice: 3150, stopLoss: 3350, takeProfit: 2950, risk: 4.2, pnl: 100)
        ]
        
        // Generate mock recent trades
        recentTrades = (0..<10).map { i in
            CompletedTrade(
                id: UUID().uuidString,
                symbol: ["BTCUSDT", "ETHUSDT", "ADAUSDT"].randomElement()!,
                side: ["BUY", "SELL"].randomElement()!,
                quantity: Double.random(in: 0.01...2.0),
                price: Double.random(in: 2000...50000),
                pnl: Double.random(in: -200...300),
                timestamp: Date().addingTimeInterval(-TimeInterval(i * 3600))
            )
        }
    }
} 