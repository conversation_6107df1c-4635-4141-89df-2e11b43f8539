import SwiftUI

struct SimpleAITradingDashboard: View {
    @State private var isAnalyzing = false
    @State private var selectedTab = 0
    @State private var showSettings = false
    @State private var analysisResults: [SimpleAnalysisResult] = []
    @State private var activeTrades: [SimpleAITrade] = []
    @State private var tradeHistory: [SimpleTradeExecution] = []
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header with AI Status
                aiStatusHeader
                
                // Tab Selection
                tabSelector
                
                // Content based on selected tab
                TabView(selection: $selectedTab) {
                    // AI Analysis Tab
                    aiAnalysisView
                        .tag(0)
                    
                    // Active Trades Tab
                    activeTradesView
                        .tag(1)
                    
                    // Trade History Tab
                    tradeHistoryView
                        .tag(2)
                    
                    // Settings Tab
                    settingsView
                        .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("🤖 AI Trading")
            .navigationBarTitleDisplayMode(.inline)
            .onAppear {
                startMockAnalysis()
            }
        }
    }
    
    // MARK: - AI Status Header
    private var aiStatusHeader: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("AI Analysis Engine")
                    .font(.headline)
                    .foregroundColor(.white)
                
                HStack {
                    Circle()
                        .fill(isAnalyzing ? Color.orange : Color.green)
                        .frame(width: 8, height: 8)
                    
                    Text(isAnalyzing ? "Analyzing Markets..." : "Ready")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text("Active Trades")
                    .font(.caption)
                    .foregroundColor(.gray)
                
                Text("\(activeTrades.count)")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.orange)
            }
            
            VStack(alignment: .trailing, spacing: 4) {
                Text("Total P&L")
                    .font(.caption)
                    .foregroundColor(.gray)
                
                let totalPnL = tradeHistory.compactMap { $0.pnl }.reduce(0, +)
                Text("$\(String(format: "%.2f", totalPnL))")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(totalPnL >= 0 ? .green : .red)
            }
        }
        .padding()
        .background(Color.black.opacity(0.8))
    }
    
    // MARK: - Tab Selector
    private var tabSelector: some View {
        HStack(spacing: 0) {
            ForEach(0..<4) { index in
                Button(action: { selectedTab = index }) {
                    VStack(spacing: 4) {
                        Image(systemName: tabIcon(for: index))
                            .font(.system(size: 16))
                        
                        Text(tabTitle(for: index))
                            .font(.caption)
                    }
                    .foregroundColor(selectedTab == index ? .orange : .gray)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                }
            }
        }
        .background(Color.gray.opacity(0.1))
    }
    
    private func tabIcon(for index: Int) -> String {
        switch index {
        case 0: return "brain.head.profile"
        case 1: return "chart.line.uptrend.xyaxis"
        case 2: return "clock.arrow.circlepath"
        case 3: return "gear"
        default: return "questionmark"
        }
    }
    
    private func tabTitle(for index: Int) -> String {
        switch index {
        case 0: return "Analysis"
        case 1: return "Active"
        case 2: return "History"
        case 3: return "Settings"
        default: return ""
        }
    }
    
    // MARK: - AI Analysis View
    private var aiAnalysisView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Analysis Results Section
                analysisResultsSection
                
                // Market Overview
                marketOverviewSection
                
                // Trading Signals
                tradingSignalsSection
            }
            .padding()
        }
    }
    
    private var analysisResultsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "cpu")
                    .foregroundColor(.purple)
                Text("AI Analysis Results")
                    .font(.headline)
                    .foregroundColor(.white)
                Spacer()
                Text("\(analysisResults.count) results")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            
            ForEach(analysisResults) { result in
                analysisResultCard(result)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private func analysisResultCard(_ result: SimpleAnalysisResult) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(result.type)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(typeColor(result.type))
                        .cornerRadius(4)
                    
                    Text(result.symbol)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                }
                
                Text(result.description)
                    .font(.caption)
                    .foregroundColor(.gray)
                    .lineLimit(2)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                signalBadge(result.signal)
                
                Text("\(String(format: "%.0f", result.confidence * 100))%")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
        }
        .padding()
        .background(Color.black.opacity(0.3))
        .cornerRadius(8)
    }
    
    private var marketOverviewSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "chart.xyaxis.line")
                    .foregroundColor(.blue)
                Text("Market Overview")
                    .font(.headline)
                    .foregroundColor(.white)
                Spacer()
            }
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                marketMetricCard("BTC/USDT", "$50,247.82", "+2.34%", .green)
                marketMetricCard("ETH/USDT", "$3,127.45", "-1.12%", .red)
                marketMetricCard("ADA/USDT", "$0.4821", "+5.67%", .green)
                marketMetricCard("DOT/USDT", "$7.23", "-0.89%", .red)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private func marketMetricCard(_ symbol: String, _ price: String, _ change: String, _ color: Color) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(symbol)
                .font(.caption)
                .foregroundColor(.gray)
            
            Text(price)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.white)
            
            Text(change)
                .font(.caption)
                .foregroundColor(color)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color.black.opacity(0.3))
        .cornerRadius(8)
    }
    
    private var tradingSignalsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "waveform.path.ecg")
                    .foregroundColor(.orange)
                Text("Trading Signals")
                    .font(.headline)
                    .foregroundColor(.white)
                Spacer()
                Text("Live")
                    .font(.caption)
                    .foregroundColor(.green)
            }
            
            VStack(spacing: 8) {
                tradingSignalRow("BTC/USDT", "Strong Buy", "RSI Oversold + MACD Bullish", .green)
                tradingSignalRow("ETH/USDT", "Hold", "Sideways trend, low volume", .yellow)
                tradingSignalRow("ADA/USDT", "Buy", "Breaking resistance level", .green)
                tradingSignalRow("DOT/USDT", "Sell", "Bearish divergence detected", .red)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private func tradingSignalRow(_ symbol: String, _ signal: String, _ reason: String, _ color: Color) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(symbol)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Text(reason)
                    .font(.caption)
                    .foregroundColor(.gray)
                    .lineLimit(1)
            }
            
            Spacer()
            
            Text(signal)
                .font(.caption)
                .fontWeight(.bold)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(color)
                .cornerRadius(6)
        }
        .padding()
        .background(Color.black.opacity(0.3))
        .cornerRadius(8)
    }
    
    // MARK: - Active Trades View
    private var activeTradesView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if activeTrades.isEmpty {
                    emptyTradesView
                } else {
                    ForEach(activeTrades) { trade in
                        activeTradeCard(trade)
                    }
                }
            }
            .padding()
        }
    }
    
    private var emptyTradesView: some View {
        VStack(spacing: 16) {
            Image(systemName: "chart.line.flattrend.xyaxis")
                .font(.system(size: 48))
                .foregroundColor(.gray)
            
            Text("No Active Trades")
                .font(.headline)
                .foregroundColor(.white)
            
            Text("AI is analyzing markets for trading opportunities")
                .font(.subheadline)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
            
            Button("Start AI Trading") {
                createMockTrade()
            }
            .buttonStyle(.borderedProminent)
            .tint(.orange)
        }
        .padding()
    }
    
    private func activeTradeCard(_ trade: SimpleAITrade) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            // Trade Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(trade.symbol)
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    Text(trade.exchange)
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    tradeBadge(trade.action)
                    
                    Text(trade.status)
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }
            
            // Trade Details
            VStack(spacing: 8) {
                tradeDetailRow("Entry Price", "$\(String(format: "%.2f", trade.entryPrice))")
                tradeDetailRow("Current Price", "$\(String(format: "%.2f", trade.currentPrice))")
                tradeDetailRow("Quantity", "\(String(format: "%.6f", trade.quantity))")
                tradeDetailRow("Stop Loss", "$\(String(format: "%.2f", trade.stopLoss))")
                tradeDetailRow("Take Profit", "$\(String(format: "%.2f", trade.takeProfit))")
                
                let pnl = (trade.currentPrice - trade.entryPrice) * trade.quantity * (trade.action == "Buy" ? 1 : -1)
                tradeDetailRow("P&L", "$\(String(format: "%.2f", pnl))", pnl >= 0 ? .green : .red)
            }
            
            // AI Reasoning
            VStack(alignment: .leading, spacing: 4) {
                Text("AI Reasoning")
                    .font(.caption)
                    .foregroundColor(.gray)
                
                Text(trade.reasoning)
                    .font(.caption)
                    .foregroundColor(.white)
                    .lineLimit(3)
            }
            
            // Trade Actions
            HStack {
                Button("Close Trade") {
                    closeTrade(trade)
                }
                .buttonStyle(ActionButtonStyle(color: .red))
                
                Spacer()
                
                Button("Modify Stop") {
                    modifyStopLoss(trade)
                }
                .buttonStyle(ActionButtonStyle(color: .orange))
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private func tradeBadge(_ action: String) -> some View {
        Text(action.uppercased())
            .font(.caption)
            .fontWeight(.bold)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(action == "Buy" ? Color.green : Color.red)
            .cornerRadius(6)
    }
    
    private func tradeDetailRow(_ label: String, _ value: String, _ color: Color = .white) -> some View {
        HStack {
            Text(label)
                .font(.caption)
                .foregroundColor(.gray)
            
            Spacer()
            
            Text(value)
                .font(.caption)
                .foregroundColor(color)
        }
    }
    
    // MARK: - Trade History View
    private var tradeHistoryView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(tradeHistory.sorted { $0.timestamp > $1.timestamp }) { execution in
                    tradeHistoryCard(execution)
                }
            }
            .padding()
        }
    }
    
    private func tradeHistoryCard(_ execution: SimpleTradeExecution) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(execution.symbol)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Text(execution.type)
                    .font(.caption)
                    .foregroundColor(.gray)
                
                Text(timeAgo(execution.timestamp))
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text("$\(String(format: "%.2f", execution.price))")
                    .font(.subheadline)
                    .foregroundColor(.white)
                
                if let pnl = execution.pnl {
                    Text("P&L: $\(String(format: "%.2f", pnl))")
                        .font(.caption)
                        .foregroundColor(pnl >= 0 ? .green : .red)
                }
            }
        }
        .padding()
        .background(Color.black.opacity(0.3))
        .cornerRadius(8)
    }
    
    // MARK: - Settings View
    private var settingsView: some View {
        ScrollView {
            VStack(spacing: 16) {
                aiSettingsCard
                riskSettingsCard
                tradingSettingsCard
            }
            .padding()
        }
    }
    
    private var aiSettingsCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("AI Settings")
                .font(.headline)
                .foregroundColor(.white)
            
            VStack(spacing: 12) {
                Toggle("Auto Trading", isOn: .constant(false))
                    .toggleStyle(SwitchToggleStyle(tint: .orange))
                
                Toggle("News Analysis", isOn: .constant(true))
                    .toggleStyle(SwitchToggleStyle(tint: .orange))
                
                Toggle("Technical Analysis", isOn: .constant(true))
                    .toggleStyle(SwitchToggleStyle(tint: .orange))
                
                Toggle("Sentiment Analysis", isOn: .constant(true))
                    .toggleStyle(SwitchToggleStyle(tint: .orange))
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var riskSettingsCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Risk Management")
                .font(.headline)
                .foregroundColor(.white)
            
            VStack(spacing: 8) {
                settingRow("Risk per Trade", "2.0%")
                settingRow("Stop Loss", "5.0%")
                settingRow("Take Profit", "10.0%")
                settingRow("Trailing Stop", "3.0%")
                settingRow("Max Concurrent Trades", "5")
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var tradingSettingsCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Trading Settings")
                .font(.headline)
                .foregroundColor(.white)
            
            VStack(spacing: 8) {
                settingRow("Decision Threshold", "70%")
                settingRow("Minimum Confidence", "75%")
                settingRow("Analysis Interval", "30 seconds")
                settingRow("Active Exchanges", "4")
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private func settingRow(_ label: String, _ value: String) -> some View {
        HStack {
            Text(label)
                .font(.subheadline)
                .foregroundColor(.gray)
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.white)
        }
    }
    
    // MARK: - Helper Functions
    private func typeColor(_ type: String) -> Color {
        switch type {
        case "Technical": return .orange
        case "News": return .blue
        case "Sentiment": return .purple
        case "Volume": return .green
        default: return .gray
        }
    }
    
    private func signalBadge(_ signal: String) -> some View {
        Text(signal)
            .font(.caption)
            .fontWeight(.bold)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(signalColor(signal))
            .cornerRadius(4)
    }
    
    private func signalColor(_ signal: String) -> Color {
        switch signal {
        case "Bullish": return .green
        case "Bearish": return .red
        case "Neutral": return .yellow
        default: return .gray
        }
    }
    
    private func timeAgo(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }
    
    // MARK: - Mock Data Functions
    private func startMockAnalysis() {
        isAnalyzing = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            self.isAnalyzing = false
            self.generateMockAnalysisResults()
            self.generateMockTradeHistory()
        }
        
        // Repeat every 30 seconds
        Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { _ in
            self.isAnalyzing = true
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                self.isAnalyzing = false
                self.generateMockAnalysisResults()
            }
        }
    }
    
    private func generateMockAnalysisResults() {
        analysisResults = [
            SimpleAnalysisResult(
                type: "Technical",
                symbol: "BTC/USDT",
                signal: "Bullish",
                confidence: 0.85,
                description: "RSI oversold, MACD bullish crossover"
            ),
            SimpleAnalysisResult(
                type: "News",
                symbol: "ETH/USDT",
                signal: "Neutral",
                confidence: 0.65,
                description: "Mixed sentiment in recent news"
            ),
            SimpleAnalysisResult(
                type: "Sentiment",
                symbol: "ADA/USDT",
                signal: "Bullish",
                confidence: 0.78,
                description: "Positive social media sentiment"
            ),
            SimpleAnalysisResult(
                type: "Volume",
                symbol: "DOT/USDT",
                signal: "Bearish",
                confidence: 0.72,
                description: "Declining volume pattern"
            )
        ]
    }
    
    private func generateMockTradeHistory() {
        tradeHistory = [
            SimpleTradeExecution(
                symbol: "BTC/USDT",
                type: "Market Buy",
                price: 49800.0,
                timestamp: Date().addingTimeInterval(-3600),
                pnl: 447.82
            ),
            SimpleTradeExecution(
                symbol: "ETH/USDT",
                type: "Stop Loss",
                price: 3150.0,
                timestamp: Date().addingTimeInterval(-7200),
                pnl: -127.45
            ),
            SimpleTradeExecution(
                symbol: "ADA/USDT",
                type: "Take Profit",
                price: 0.4621,
                timestamp: Date().addingTimeInterval(-10800),
                pnl: 89.32
            )
        ]
    }
    
    private func createMockTrade() {
        let mockTrade = SimpleAITrade(
            symbol: "BTC/USDT",
            exchange: "Binance",
            action: "Buy",
            entryPrice: 50247.82,
            currentPrice: 50347.82,
            quantity: 0.001,
            stopLoss: 47735.43,
            takeProfit: 55272.60,
            status: "Active",
            reasoning: "AI detected strong bullish signals: RSI oversold recovery, MACD bullish crossover, and positive news sentiment. High confidence trade with 85% probability of success."
        )
        
        activeTrades.append(mockTrade)
    }
    
    private func closeTrade(_ trade: SimpleAITrade) {
        activeTrades.removeAll { $0.id == trade.id }
        
        let execution = SimpleTradeExecution(
            symbol: trade.symbol,
            type: "Manual Close",
            price: trade.currentPrice,
            timestamp: Date(),
            pnl: (trade.currentPrice - trade.entryPrice) * trade.quantity * (trade.action == "Buy" ? 1 : -1)
        )
        
        tradeHistory.append(execution)
    }
    
    private func modifyStopLoss(_ trade: SimpleAITrade) {
        // Mock modification
        if let index = activeTrades.firstIndex(where: { $0.id == trade.id }) {
            activeTrades[index].stopLoss = trade.currentPrice * 0.95
        }
    }
}

// MARK: - Supporting Models
struct SimpleAnalysisResult: Identifiable {
    let id = UUID()
    let type: String
    let symbol: String
    let signal: String
    let confidence: Double
    let description: String
}

struct SimpleAITrade: Identifiable {
    let id = UUID()
    let symbol: String
    let exchange: String
    let action: String
    let entryPrice: Double
    let currentPrice: Double
    let quantity: Double
    var stopLoss: Double
    let takeProfit: Double
    let status: String
    let reasoning: String
}

struct SimpleTradeExecution: Identifiable {
    let id = UUID()
    let symbol: String
    let type: String
    let price: Double
    let timestamp: Date
    let pnl: Double?
}

// MARK: - Action Button Style
struct ActionButtonStyle: ButtonStyle {
    let color: Color
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.caption)
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(color.opacity(configuration.isPressed ? 0.7 : 1.0))
            .cornerRadius(6)
    }
}