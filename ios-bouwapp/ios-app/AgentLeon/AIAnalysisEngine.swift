import Foundation
import SwiftUI

// MARK: - AI Analysis Engine
class AIAnalysisEngine: ObservableObject {
    @Published var isAnalyzing = false
    @Published var analysisResults: [AnalysisResult] = []
    @Published var activeTrades: [AITrade] = []
    @Published var tradeExecutions: [TradeExecution] = []
    @Published var newsAnalysis: [NewsAnalysis] = []
    @Published var technicalSignals: [TechnicalSignal] = []
    
    private let apiService: MultiExchangeAPIService
    private let newsService = NewsAnalysisService()
    private let technicalAnalyzer = TechnicalAnalysisService()
    private let tradeManager = AITradeManager()
    
    // AI Configuration
    @Published var aiSettings = AISettings()
    
    init(apiService: MultiExchangeAPIService) {
        self.apiService = apiService
        setupRealTimeAnalysis()
    }
    
    // MARK: - Main Analysis Functions
    func startComprehensiveAnalysis() async {
        await MainActor.run {
            isAnalyzing = true
        }
        
        async let newsResults = analyzeMarketNews()
        async let technicalResults = performTechnicalAnalysis()
        async let sentimentResults = analyzeSentiment()
        async let volumeResults = analyzeVolumePatterns()
        
        let allResults = await [newsResults, technicalResults, sentimentResults, volumeResults].flatMap { $0 }
        
        await MainActor.run {
            self.analysisResults = allResults
            self.processAnalysisForTrading(allResults)
            self.isAnalyzing = false
        }
    }
    
    // MARK: - News Analysis
    private func analyzeMarketNews() async -> [AnalysisResult] {
        let newsData = await newsService.fetchLatestNews()
        var results: [AnalysisResult] = []
        
        for news in newsData {
            let sentiment = await newsService.analyzeSentiment(news.content)
            let impact = await newsService.calculateMarketImpact(news)
            
            let analysis = NewsAnalysis(
                id: UUID(),
                headline: news.headline,
                content: news.content,
                sentiment: sentiment,
                impactScore: impact,
                relevantCoins: extractRelevantCoins(from: news.content),
                timestamp: news.timestamp,
                source: news.source
            )
            
            await MainActor.run {
                self.newsAnalysis.append(analysis)
            }
            
            results.append(AnalysisResult(
                type: .news,
                symbol: "MARKET",
                signal: sentiment > 0.6 ? .bullish : sentiment < 0.4 ? .bearish : .neutral,
                confidence: abs(sentiment - 0.5) * 2,
                description: "News: \(news.headline)",
                timestamp: Date(),
                data: ["sentiment": sentiment, "impact": impact]
            ))
        }
        
        return results
    }
    
    // MARK: - Technical Analysis
    private func performTechnicalAnalysis() async -> [AnalysisResult] {
        var results: [AnalysisResult] = []
        let symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "DOTUSDT", "LINKUSDT"]
        
        for symbol in symbols {
            for exchange in SupportedExchange.allCases {
                if let marketData = await apiService.getMarketData(symbol: symbol, exchange: exchange) {
                    let technicalData = await technicalAnalyzer.analyze(marketData)
                    
                    let signal = TechnicalSignal(
                        id: UUID(),
                        symbol: symbol,
                        exchange: exchange,
                        rsi: technicalData.rsi,
                        macd: technicalData.macd,
                        bollinger: technicalData.bollingerBands,
                        support: technicalData.supportLevel,
                        resistance: technicalData.resistanceLevel,
                        trend: technicalData.trend,
                        volume: technicalData.volumeAnalysis,
                        timestamp: Date()
                    )
                    
                    await MainActor.run {
                        self.technicalSignals.append(signal)
                    }
                    
                    let overallSignal = calculateOverallSignal(technicalData)
                    
                    results.append(AnalysisResult(
                        type: .technical,
                        symbol: symbol,
                        signal: overallSignal.signal,
                        confidence: overallSignal.confidence,
                        description: "Technical: \(overallSignal.description)",
                        timestamp: Date(),
                        data: [
                            "rsi": technicalData.rsi,
                            "macd": technicalData.macd.macd,
                            "trend": Double(technicalData.trend.rawValue.hashValue)
                        ]
                    ))
                }
            }
        }
        
        return results
    }
    
    // MARK: - Sentiment Analysis
    private func analyzeSentiment() async -> [AnalysisResult] {
        let socialData = await newsService.fetchSocialSentiment()
        var results: [AnalysisResult] = []
        
        for sentiment in socialData {
            results.append(AnalysisResult(
                type: .sentiment,
                symbol: sentiment.symbol,
                signal: sentiment.score > 0.6 ? .bullish : sentiment.score < 0.4 ? .bearish : .neutral,
                confidence: abs(sentiment.score - 0.5) * 2,
                description: "Social Sentiment: \(sentiment.platform)",
                timestamp: Date(),
                data: ["score": sentiment.score, "volume": Double(sentiment.mentionVolume)]
            ))
        }
        
        return results
    }
    
    // MARK: - Volume Analysis
    private func analyzeVolumePatterns() async -> [AnalysisResult] {
        var results: [AnalysisResult] = []
        let symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
        
        for symbol in symbols {
            let volumeData = await technicalAnalyzer.analyzeVolumePatterns(symbol: symbol)
            
            results.append(AnalysisResult(
                type: .volume,
                symbol: symbol,
                signal: volumeData.signal,
                confidence: volumeData.confidence,
                description: "Volume: \(volumeData.pattern)",
                timestamp: Date(),
                data: ["pattern": Double(volumeData.pattern.hashValue), "strength": volumeData.strength]
            ))
        }
        
        return results
    }
    
    // MARK: - Trading Decision Engine
    private func processAnalysisForTrading(_ results: [AnalysisResult]) {
        let groupedResults = Dictionary(grouping: results) { $0.symbol }
        
        for (symbol, symbolResults) in groupedResults {
            let tradingDecision = makeTradingDecision(for: symbol, results: symbolResults)
            
            if tradingDecision.shouldTrade {
                Task {
                    await executeTrade(decision: tradingDecision)
                }
            }
        }
    }
    
    private func makeTradingDecision(for symbol: String, results: [AnalysisResult]) -> TradingDecision {
        var bullishScore = 0.0
        var bearishScore = 0.0
        var totalConfidence = 0.0
        
        for result in results {
            let weightedConfidence = result.confidence * getAnalysisWeight(result.type)
            totalConfidence += weightedConfidence
            
            switch result.signal {
            case .bullish:
                bullishScore += weightedConfidence
            case .bearish:
                bearishScore += weightedConfidence
            case .neutral:
                break
            }
        }
        
        let netScore = bullishScore - bearishScore
        let threshold = aiSettings.decisionThreshold
        
        var action: TradeAction = .hold
        if netScore > threshold {
            action = .buy
        } else if netScore < -threshold {
            action = .sell
        }
        
        return TradingDecision(
            symbol: symbol,
            action: action,
            confidence: totalConfidence / Double(results.count),
            reasoning: generateReasoning(results: results, netScore: netScore),
            shouldTrade: action != .hold && totalConfidence > aiSettings.minimumConfidence
        )
    }
    
    // MARK: - Trade Execution
    private func executeTrade(decision: TradingDecision) async {
        guard decision.shouldTrade else { return }
        
        let bestExchange = await findBestExchange(for: decision.symbol)
        let position = calculatePositionSize(decision: decision)
        
        let trade = AITrade(
            id: UUID(),
            symbol: decision.symbol,
            exchange: bestExchange,
            action: decision.action,
            quantity: position.size,
            entryPrice: position.price,
            stopLoss: position.stopLoss,
            takeProfit: position.takeProfit,
            trailingStopPercent: aiSettings.trailingStopPercent,
            trailingStopPrice: nil,
            status: .pending,
            reasoning: decision.reasoning,
            timestamp: Date()
        )
        
        do {
            let executionResult = try await tradeManager.executeTrade(trade, apiService: apiService)
            
            await MainActor.run {
                self.activeTrades.append(trade)
                self.tradeExecutions.append(executionResult)
            }
            
        } catch {
            print("Trade execution failed: \(error)")
        }
    }
    
    // MARK: - Real-time Analysis Setup
    private func setupRealTimeAnalysis() {
        Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { _ in
            Task {
                await self.startComprehensiveAnalysis()
            }
        }
    }
    
    // MARK: - Helper Functions
    private func getAnalysisWeight(_ type: AnalysisType) -> Double {
        switch type {
        case .technical: return 1.0
        case .news: return 0.8
        case .sentiment: return 0.6
        case .volume: return 0.7
        }
    }
    
    private func extractRelevantCoins(from content: String) -> [String] {
        let coinKeywords = ["BTC", "ETH", "ADA", "DOT", "LINK", "Bitcoin", "Ethereum"]
        return coinKeywords.filter { content.contains($0) }
    }
    
    private func calculateOverallSignal(_ data: TechnicalAnalysisData) -> (signal: AnalysisSignal, confidence: Double, description: String) {
        var signals: [AnalysisSignal] = []
        
        // RSI Analysis
        if data.rsi > 70 {
            signals.append(.bearish)
        } else if data.rsi < 30 {
            signals.append(.bullish)
        }
        
        // MACD Analysis
        if data.macd.macd > data.macd.signal {
            signals.append(.bullish)
        } else {
            signals.append(.bearish)
        }
        
        // Trend Analysis
        switch data.trend {
        case .uptrend:
            signals.append(.bullish)
        case .downtrend:
            signals.append(.bearish)
        case .sideways:
            signals.append(.neutral)
        }
        
        let bullishCount = signals.filter { $0 == .bullish }.count
        let bearishCount = signals.filter { $0 == .bearish }.count
        
        let overallSignal: AnalysisSignal
        if bullishCount > bearishCount {
            overallSignal = .bullish
        } else if bearishCount > bullishCount {
            overallSignal = .bearish
        } else {
            overallSignal = .neutral
        }
        
        let confidence = Double(max(bullishCount, bearishCount)) / Double(signals.count)
        let description = "RSI: \(String(format: "%.1f", data.rsi)), MACD: \(data.macd.macd > data.macd.signal ? "↑" : "↓"), Trend: \(data.trend.rawValue)"
        
        return (overallSignal, confidence, description)
    }
    
    private func generateReasoning(results: [AnalysisResult], netScore: Double) -> String {
        var reasoning = "AI Analysis: "
        
        let technicalResults = results.filter { $0.type == .technical }
        let newsResults = results.filter { $0.type == .news }
        let sentimentResults = results.filter { $0.type == .sentiment }
        
        if !technicalResults.isEmpty {
            reasoning += "Technical indicators show \(netScore > 0 ? "bullish" : "bearish") signals. "
        }
        
        if !newsResults.isEmpty {
            reasoning += "News sentiment is \(newsResults.first?.signal.rawValue ?? "neutral"). "
        }
        
        if !sentimentResults.isEmpty {
            reasoning += "Social sentiment is \(sentimentResults.first?.signal.rawValue ?? "neutral"). "
        }
        
        reasoning += "Confidence: \(String(format: "%.1f", abs(netScore) * 100))%"
        
        return reasoning
    }
    
    private func findBestExchange(for symbol: String) async -> SupportedExchange {
        // Find exchange with best liquidity and lowest fees
        var bestExchange = SupportedExchange.binance
        var bestScore = 0.0
        
        for exchange in SupportedExchange.allCases {
            if let orderBook = await apiService.getOrderBook(symbol: symbol, exchange: exchange) {
                let liquidityScore = calculateLiquidityScore(orderBook)
                let feeScore = getFeeScore(exchange)
                let totalScore = liquidityScore * 0.7 + feeScore * 0.3
                
                if totalScore > bestScore {
                    bestScore = totalScore
                    bestExchange = exchange
                }
            }
        }
        
        return bestExchange
    }
    
    private func calculateLiquidityScore(_ orderBook: OrderBook) -> Double {
        let bidVolume = orderBook.bids.prefix(5).reduce(0.0) { $0 + (Double($1.size) ?? 0.0) }
        let askVolume = orderBook.asks.prefix(5).reduce(0.0) { $0 + (Double($1.size) ?? 0.0) }
        return (bidVolume + askVolume) / 2.0
    }
    
    private func getFeeScore(_ exchange: SupportedExchange) -> Double {
        switch exchange {
        case .binance: return 0.9
        case .kucoin: return 0.8
        case .bybit: return 0.85
        case .mexc: return 0.75
        }
    }
    
    private func calculatePositionSize(decision: TradingDecision) -> (size: Double, price: Double, stopLoss: Double, takeProfit: Double) {
        let riskPercentage = aiSettings.riskPercentage
        let accountBalance = 10000.0 // This should come from actual account balance
        let riskAmount = accountBalance * (riskPercentage / 100.0)
        
        let currentPrice = 50000.0 // This should be actual current price
        let stopLossPercent = aiSettings.stopLossPercent
        let takeProfitPercent = aiSettings.takeProfitPercent
        
        let stopLossPrice: Double
        let takeProfitPrice: Double
        
        if decision.action == .buy {
            stopLossPrice = currentPrice * (1 - stopLossPercent / 100.0)
            takeProfitPrice = currentPrice * (1 + takeProfitPercent / 100.0)
        } else {
            stopLossPrice = currentPrice * (1 + stopLossPercent / 100.0)
            takeProfitPrice = currentPrice * (1 - takeProfitPercent / 100.0)
        }
        
        let riskPerUnit = abs(currentPrice - stopLossPrice)
        let positionSize = riskAmount / riskPerUnit
        
        return (positionSize, currentPrice, stopLossPrice, takeProfitPrice)
    }
}

// MARK: - Supporting Models
struct AnalysisResult: Identifiable, Codable {
    var id = UUID()
    let type: AnalysisType
    let symbol: String
    let signal: AnalysisSignal
    let confidence: Double
    let description: String
    let timestamp: Date
    let data: [String: Double]
}

enum AnalysisType: String, CaseIterable, Codable {
    case technical = "Technical"
    case news = "News"
    case sentiment = "Sentiment"
    case volume = "Volume"
}

enum AnalysisSignal: String, CaseIterable, Codable {
    case bullish = "Bullish"
    case bearish = "Bearish"
    case neutral = "Neutral"
}

struct TradingDecision {
    let symbol: String
    let action: TradeAction
    let confidence: Double
    let reasoning: String
    let shouldTrade: Bool
}

enum TradeAction: String, CaseIterable, Codable {
    case buy = "Buy"
    case sell = "Sell"
    case hold = "Hold"
}

struct AITrade: Identifiable, Codable {
    let id: UUID
    let symbol: String
    let exchange: SupportedExchange
    let action: TradeAction
    let quantity: Double
    let entryPrice: Double
    var stopLoss: Double
    let takeProfit: Double
    let trailingStopPercent: Double
    var trailingStopPrice: Double?
    var status: TradeStatus
    let reasoning: String
    let timestamp: Date
}

enum TradeStatus: String, CaseIterable, Codable {
    case pending = "Pending"
    case active = "Active"
    case closed = "Closed"
    case cancelled = "Cancelled"
}

struct TradeExecution: Identifiable, Codable {
    let id: UUID
    let tradeId: UUID
    let type: ExecutionType
    let symbol: String
    let exchange: SupportedExchange
    let side: TradeSide
    let quantity: Double
    let price: Double
    let status: ExecutionStatus
    let timestamp: Date
    let fees: Double
    let pnl: Double?
}

enum ExecutionType: String, CaseIterable, Codable {
    case market = "Market"
    case limit = "Limit"
    case stopLoss = "Stop Loss"
    case takeProfit = "Take Profit"
    case trailingStop = "Trailing Stop"
}

enum TradeSide: String, CaseIterable, Codable {
    case buy = "Buy"
    case sell = "Sell"
}

enum ExecutionStatus: String, CaseIterable, Codable {
    case pending = "Pending"
    case executed = "Executed"
    case failed = "Failed"
    case cancelled = "Cancelled"
}

struct NewsAnalysis: Identifiable, Codable {
    let id: UUID
    let headline: String
    let content: String
    let sentiment: Double
    let impactScore: Double
    let relevantCoins: [String]
    let timestamp: Date
    let source: String
}

struct TechnicalSignal: Identifiable, Codable {
    let id: UUID
    let symbol: String
    let exchange: SupportedExchange
    let rsi: Double
    let macd: MACDData
    let bollinger: BollingerBands
    let support: Double
    let resistance: Double
    let trend: TrendDirection
    let volume: VolumeAnalysis
    let timestamp: Date
}

struct AISettings: Codable {
    var decisionThreshold: Double = 0.6
    var minimumConfidence: Double = 0.7
    var riskPercentage: Double = 2.0
    var stopLossPercent: Double = 5.0
    var takeProfitPercent: Double = 10.0
    var trailingStopPercent: Double = 3.0
    var autoTradingEnabled: Bool = false
    var maxConcurrentTrades: Int = 5
    var enableNewsAnalysis: Bool = true
    var enableSentimentAnalysis: Bool = true
    var enableTechnicalAnalysis: Bool = true
} 
