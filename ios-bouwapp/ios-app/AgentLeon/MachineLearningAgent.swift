import Foundation
import Combine
import CoreML
import CreateML

// MARK: - Machine Learning Agent
class MachineLearningAgent: Agent {
    @Published var models: [MLModel] = []
    @Published var predictions: [PredictionResult] = []
    @Published var backtestResults: [BacktestResult] = []
    @Published var modelAccuracy: [String: Double] = [:]
    @Published var isTraining: Bool = false
    @Published var trainingProgress: Double = 0.0
    @Published var anomalies: [MarketAnomaly] = []
    
    private var realTimeService: RealTimeDataService
    private var historicalData: [String: [HistoricalDataPoint]] = [:]
    private var featureExtractor = FeatureExtractor()
    private var modelTrainer = ModelTrainer()
    private var anomalyDetector = AnomalyDetector()
    
    // ML Configuration
    private let predictionHorizons = [5, 15, 30, 60] // minutes
    private let modelTypes: [ModelType] = [.lstm, .randomForest, .svm, .neuralNetwork]
    private let maxHistoricalDataPoints = 10000
    
    override init(name: String = "ML Predictor", status: AgentStatus = .idle, performance: Double = 100.0, capabilities: [String] = [], version: String = "1.0.0") {
        self.realTimeService = RealTimeDataService()
        
        super.init(
            name: name,
            status: status,
            performance: performance,
            capabilities: ["Price Prediction", "Pattern Recognition", "Anomaly Detection", "Strategy Optimization", "Market Regime Detection"],
            version: version
        )
        
        setupModelTraining()
        startContinuousLearning()
    }
    
    // MARK: - Price Prediction
    func predictPrice(symbol: String, horizon: Int = 15) async -> PredictionResult? {
        updateStatus(to: .thinking)
        
        guard let features = await extractFeatures(for: symbol) else {
            updateStatus(to: .idle)
            return nil
        }
        
        // Use ensemble of models for prediction
        var predictions: [Double] = []
        var confidences: [Double] = []
        
        for modelType in modelTypes {
            if let model = getModel(for: symbol, type: modelType) {
                let (price, confidence) = await makePrediction(model: model, features: features)
                predictions.append(price)
                confidences.append(confidence)
            }
        }
        
        guard !predictions.isEmpty else {
            updateStatus(to: .idle)
            return nil
        }
        
        // Weighted ensemble prediction
        let weightedPrediction = calculateWeightedPrediction(predictions: predictions, confidences: confidences)
        let avgConfidence = confidences.reduce(0, +) / Double(confidences.count)
        
        let result = PredictionResult(
            symbol: symbol,
            predictedPrice: weightedPrediction,
            currentPrice: features.currentPrice,
            horizon: horizon,
            confidence: avgConfidence,
            direction: weightedPrediction > features.currentPrice ? .up : .down,
            timestamp: Date(),
            features: features
        )
        
        await MainActor.run {
            self.predictions.append(result)
            // Keep only last 100 predictions
            if self.predictions.count > 100 {
                self.predictions.removeFirst()
            }
        }
        
        updateStatus(to: .active)
        return result
    }
    
    // MARK: - Pattern Recognition
    func detectPatterns(symbol: String) async -> [PatternDetectionResult] {
        updateStatus(to: .thinking)
        
        guard let historicalPrices = historicalData[symbol] else {
            updateStatus(to: .idle)
            return []
        }
        
        var patterns: [PatternDetectionResult] = []
        
        // Detect various chart patterns
        patterns.append(contentsOf: await detectCandlestickPatterns(prices: historicalPrices))
        patterns.append(contentsOf: await detectTrendPatterns(prices: historicalPrices))
        patterns.append(contentsOf: await detectSupportResistance(prices: historicalPrices))
        patterns.append(contentsOf: await detectVolumePatterns(prices: historicalPrices))
        
        updateStatus(to: .active)
        return patterns
    }
    
    // MARK: - Anomaly Detection
    func detectAnomalies(symbol: String) async -> [MarketAnomaly] {
        updateStatus(to: .thinking)
        
        guard let recentData = historicalData[symbol]?.suffix(100) else {
            updateStatus(to: .idle)
            return []
        }
        
        let anomalies = await anomalyDetector.detectAnomalies(data: Array(recentData))
        
        await MainActor.run {
            self.anomalies.append(contentsOf: anomalies)
            // Keep only recent anomalies
            self.anomalies = self.anomalies.filter { Date().timeIntervalSince($0.timestamp) < 3600 }
        }
        
        updateStatus(to: .active)
        return anomalies
    }
    
    // MARK: - Strategy Backtesting
    func backtestStrategy(_ strategy: TradingStrategy, symbol: String) async -> BacktestResult {
        updateStatus(to: .thinking)
        
        guard let historicalPrices = historicalData[symbol] else {
            updateStatus(to: .idle)
            return BacktestResult.empty()
        }
        
        let result = await performBacktest(strategy: strategy, data: historicalPrices)
        
        await MainActor.run {
            self.backtestResults.append(result)
        }
        
        updateStatus(to: .active)
        return result
    }
    
    // MARK: - Feature Extraction
    private func extractFeatures(for symbol: String) async -> FeatureSet? {
        guard let recentData = historicalData[symbol]?.suffix(100) else { return nil }
        
        return await featureExtractor.extractFeatures(from: Array(recentData))
    }
    
    // MARK: - Model Management
    private func getModel(for symbol: String, type: ModelType) -> MLModel? {
        return models.first { $0.symbol == symbol && $0.type == type }
    }
    
    private func setupModelTraining() {
        // Train initial models for major symbols
        let majorSymbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
        
        Task {
            for symbol in majorSymbols {
                await trainModelsForSymbol(symbol)
            }
        }
    }
    
    private func trainModelsForSymbol(_ symbol: String) async {
        await MainActor.run {
            self.isTraining = true
            self.trainingProgress = 0.0
        }
        
        for (index, modelType) in modelTypes.enumerated() {
            let model = await modelTrainer.trainModel(
                type: modelType,
                symbol: symbol,
                data: historicalData[symbol] ?? []
            )
            
            if let model = model {
                await MainActor.run {
                    self.models.append(model)
                }
            }
            
            await MainActor.run {
                self.trainingProgress = Double(index + 1) / Double(self.modelTypes.count)
            }
        }
        
        await MainActor.run {
            self.isTraining = false
        }
    }
    
    // MARK: - Continuous Learning
    private func startContinuousLearning() {
        Timer.scheduledTimer(withTimeInterval: 3600, repeats: true) { _ in
            Task {
                await self.retrainModels()
                await self.validateModelPerformance()
            }
        }
    }
    
    private func retrainModels() async {
        // Retrain models with new data every hour
        for symbol in Set(historicalData.keys) {
            await trainModelsForSymbol(symbol)
        }
    }
    
    private func validateModelPerformance() async {
        for model in models {
            let accuracy = await calculateModelAccuracy(model)
            await MainActor.run {
                self.modelAccuracy["\(model.symbol)_\(model.type.rawValue)"] = accuracy
            }
        }
    }
    
    // MARK: - Pattern Detection Methods
    private func detectCandlestickPatterns(prices: [HistoricalDataPoint]) async -> [PatternDetectionResult] {
        var patterns: [PatternDetectionResult] = []
        
        // Doji detection
        for i in 1..<prices.count {
            let candle = prices[i]
            let bodySize = abs(candle.close - candle.open)
            let totalRange = candle.high - candle.low
            
            if bodySize / totalRange < 0.1 { // Small body relative to range
                patterns.append(PatternDetectionResult(
                    type: .doji,
                    symbol: "GENERIC",
                    confidence: 0.8,
                    timestamp: candle.timestamp,
                    description: "Doji pattern detected - market indecision"
                ))
            }
        }
        
        // Hammer detection
        for i in 2..<prices.count {
            let current = prices[i]
            let bodySize = abs(current.close - current.open)
            let lowerShadow = min(current.open, current.close) - current.low
            let upperShadow = current.high - max(current.open, current.close)
            
            if lowerShadow > bodySize * 2 && upperShadow < bodySize * 0.5 {
                patterns.append(PatternDetectionResult(
                    type: .hammer,
                    symbol: "GENERIC",
                    confidence: 0.75,
                    timestamp: current.timestamp,
                    description: "Hammer pattern - potential reversal"
                ))
            }
        }
        
        return patterns
    }
    
    private func detectTrendPatterns(prices: [HistoricalDataPoint]) async -> [PatternDetectionResult] {
        guard prices.count >= 20 else { return [] }
        
        var patterns: [PatternDetectionResult] = []
        let closePrices = prices.map { $0.close }
        
        // Moving average crossover
        let sma20 = calculateSMA(prices: closePrices, period: 20)
        let sma50 = calculateSMA(prices: closePrices, period: 50)
        
        if let last20 = sma20.last, let last50 = sma50.last,
           let prev20 = sma20.dropLast().last, let prev50 = sma50.dropLast().last {
            
            // Golden cross
            if last20 > last50 && prev20 <= prev50 {
                patterns.append(PatternDetectionResult(
                    type: .goldenCross,
                    symbol: "GENERIC",
                    confidence: 0.85,
                    timestamp: prices.last?.timestamp ?? Date(),
                    description: "Golden Cross - bullish signal"
                ))
            }
            
            // Death cross
            if last20 < last50 && prev20 >= prev50 {
                patterns.append(PatternDetectionResult(
                    type: .deathCross,
                    symbol: "GENERIC",
                    confidence: 0.85,
                    timestamp: prices.last?.timestamp ?? Date(),
                    description: "Death Cross - bearish signal"
                ))
            }
        }
        
        return patterns
    }
    
    // MARK: - Prediction Helpers
    private func makePrediction(model: MLModel, features: FeatureSet) async -> (price: Double, confidence: Double) {
        // Simulate ML prediction
        let randomFactor = Double.random(in: 0.95...1.05)
        let trendFactor = features.rsi > 70 ? 0.98 : (features.rsi < 30 ? 1.02 : 1.0)
        
        let predictedPrice = features.currentPrice * randomFactor * trendFactor
        let confidence = min(0.95, max(0.3, 1.0 - abs(randomFactor - 1.0) * 10))
        
        return (predictedPrice, confidence)
    }
    
    private func calculateWeightedPrediction(predictions: [Double], confidences: [Double]) -> Double {
        let totalWeight = confidences.reduce(0, +)
        guard totalWeight > 0 else { return predictions.reduce(0, +) / Double(predictions.count) }
        
        var weightedSum = 0.0
        for i in 0..<predictions.count {
            weightedSum += predictions[i] * (confidences[i] / totalWeight)
        }
        
        return weightedSum
    }
    
    // MARK: - Utility Methods
    private func calculateSMA(prices: [Double], period: Int) -> [Double] {
        guard prices.count >= period else { return [] }
        
        var sma: [Double] = []
        for i in (period - 1)..<prices.count {
            let sum = prices[(i - period + 1)...i].reduce(0, +)
            sma.append(sum / Double(period))
        }
        
        return sma
    }
    
    private func calculateModelAccuracy(_ model: MLModel) async -> Double {
        // Simulate accuracy calculation
        return Double.random(in: 0.6...0.95)
    }
    
    private func performBacktest(strategy: TradingStrategy, data: [HistoricalDataPoint]) async -> BacktestResult {
        // Simplified backtesting simulation
        let initialBalance = 10000.0
        var balance = initialBalance
        var position = 0.0
        var trades = 0
        var wins = 0
        
        for i in 1..<data.count {
            let current = data[i]
            let previous = data[i-1]
            
            // Simple strategy: buy on 5% dip, sell on 3% gain
            if current.close < previous.close * 0.95 && position == 0 {
                // Buy
                position = balance / current.close
                balance = 0
                trades += 1
            } else if current.close > previous.close * 1.03 && position > 0 {
                // Sell
                balance = position * current.close
                if balance > 10000 { wins += 1 }
                position = 0
            }
        }
        
        let finalValue = balance + (position * data.last!.close)
        let totalReturn = (finalValue - initialBalance) / initialBalance
        
        return BacktestResult(
            strategy: strategy.name,
            symbol: "GENERIC",
            initialBalance: initialBalance,
            finalBalance: finalValue,
            totalReturn: totalReturn,
            winRate: trades > 0 ? Double(wins) / Double(trades) : 0,
            totalTrades: trades,
            maxDrawdown: 0.1, // Simplified
            sharpeRatio: totalReturn / 0.15, // Simplified
            startDate: data.first?.timestamp ?? Date(),
            endDate: data.last?.timestamp ?? Date()
        )
    }
}

// MARK: - Supporting Classes
class FeatureExtractor {
    func extractFeatures(from data: [HistoricalDataPoint]) async -> FeatureSet? {
        guard let latest = data.last, data.count >= 20 else { return nil }
        
        let closePrices = data.map { $0.close }
        let volumes = data.map { $0.volume }
        
        return FeatureSet(
            currentPrice: latest.close,
            sma20: calculateSMA(closePrices, period: 20).last ?? latest.close,
            sma50: calculateSMA(closePrices, period: 50).last ?? latest.close,
            rsi: calculateRSI(closePrices),
            volume: latest.volume,
            volumeMA: volumes.suffix(20).reduce(0, +) / 20,
            priceChange: data.count > 1 ? (latest.close - data[data.count-2].close) / data[data.count-2].close : 0,
            volatility: calculateVolatility(closePrices)
        )
    }
    
    private func calculateSMA(_ prices: [Double], period: Int) -> [Double] {
        guard prices.count >= period else { return [] }
        
        var sma: [Double] = []
        for i in (period - 1)..<prices.count {
            let sum = prices[(i - period + 1)...i].reduce(0, +)
            sma.append(sum / Double(period))
        }
        return sma
    }
    
    private func calculateRSI(_ prices: [Double]) -> Double {
        guard prices.count >= 15 else { return 50 }
        
        var gains = [Double]()
        var losses = [Double]()
        
        for i in 1..<prices.count {
            let change = prices[i] - prices[i-1]
            gains.append(max(0, change))
            losses.append(max(0, -change))
        }
        
        let avgGain = gains.suffix(14).reduce(0, +) / 14
        let avgLoss = losses.suffix(14).reduce(0, +) / 14
        
        guard avgLoss != 0 else { return 100 }
        let rs = avgGain / avgLoss
        return 100 - (100 / (1 + rs))
    }
    
    private func calculateVolatility(_ prices: [Double]) -> Double {
        guard prices.count >= 2 else { return 0 }
        
        let returns = zip(prices.dropFirst(), prices).map { (current, previous) in
            (current - previous) / previous
        }
        
        let meanReturn = returns.reduce(0, +) / Double(returns.count)
        let variance = returns.map { pow($0 - meanReturn, 2) }.reduce(0, +) / Double(returns.count)
        
        return sqrt(variance)
    }
}

class ModelTrainer {
    func trainModel(type: ModelType, symbol: String, data: [HistoricalDataPoint]) async -> MLModel? {
        // Simulate model training
        try? await Task.sleep(nanoseconds: UInt64.random(in: 1_000_000_000...3_000_000_000))
        
        return MLModel(
            id: UUID(),
            symbol: symbol,
            type: type,
            accuracy: Double.random(in: 0.6...0.9),
            trainedAt: Date(),
            version: "1.0"
        )
    }
}

class AnomalyDetector {
    func detectAnomalies(data: [HistoricalDataPoint]) async -> [MarketAnomaly] {
        var anomalies: [MarketAnomaly] = []
        
        // Detect price spikes
        for i in 1..<data.count {
            let current = data[i]
            let previous = data[i-1]
            let priceChange = abs(current.close - previous.close) / previous.close
            
            if priceChange > 0.1 { // 10% change
                anomalies.append(MarketAnomaly(
                    type: .priceSpike,
                    severity: priceChange > 0.2 ? .high : .medium,
                    description: "Unusual price movement detected",
                    timestamp: current.timestamp,
                    value: priceChange
                ))
            }
        }
        
        return anomalies
    }
}

// MARK: - Supporting Types
struct FeatureSet {
    let currentPrice: Double
    let sma20: Double
    let sma50: Double
    let rsi: Double
    let volume: Double
    let volumeMA: Double
    let priceChange: Double
    let volatility: Double
}

struct PredictionResult: Identifiable {
    let id = UUID()
    let symbol: String
    let predictedPrice: Double
    let currentPrice: Double
    let horizon: Int
    let confidence: Double
    let direction: PredictionDirection
    let timestamp: Date
    let features: FeatureSet
}

enum PredictionDirection {
    case up, down, sideways
}

struct PatternDetectionResult: Identifiable {
    let id = UUID()
    let type: PatternType
    let symbol: String
    let confidence: Double
    let timestamp: Date
    let description: String
}

enum PatternType {
    case doji, hammer, goldenCross, deathCross, trianglePattern, headAndShoulders
}

struct BacktestResult: Identifiable {
    let id = UUID()
    let strategy: String
    let symbol: String
    let initialBalance: Double
    let finalBalance: Double
    let totalReturn: Double
    let winRate: Double
    let totalTrades: Int
    let maxDrawdown: Double
    let sharpeRatio: Double
    let startDate: Date
    let endDate: Date
    
    static func empty() -> BacktestResult {
        return BacktestResult(
            strategy: "", symbol: "", initialBalance: 0, finalBalance: 0,
            totalReturn: 0, winRate: 0, totalTrades: 0, maxDrawdown: 0,
            sharpeRatio: 0, startDate: Date(), endDate: Date()
        )
    }
}

struct MLModel: Identifiable {
    let id: UUID
    let symbol: String
    let type: ModelType
    let accuracy: Double
    let trainedAt: Date
    let version: String
}

enum ModelType: String, CaseIterable {
    case lstm = "LSTM"
    case randomForest = "Random Forest"
    case svm = "SVM"
    case neuralNetwork = "Neural Network"
}

struct MarketAnomaly: Identifiable {
    let id = UUID()
    let type: AnomalyType
    let severity: AnomalySeverity
    let description: String
    let timestamp: Date
    let value: Double
}

enum AnomalyType {
    case priceSpike, volumeSpike, liquidityDrop, orderBookImbalance
}

enum AnomalySeverity {
    case low, medium, high, critical
}

struct HistoricalDataPoint {
    let timestamp: Date
    let open: Double
    let high: Double
    let low: Double
    let close: Double
    let volume: Double
} 