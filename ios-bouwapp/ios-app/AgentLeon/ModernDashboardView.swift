import SwiftUI
import Speech

// MARK: - Modern Dutch Dashboard View
struct ModernDashboardView: View {
    @StateObject private var agentManager = AgentManager()
    @StateObject private var voiceController = VoiceController()
    @State private var realTradingService: RealTradingService?
    @State private var selectedTab = 0
    @State private var showingNotification = false
    @State private var notificationMessage = ""
    
    var body: some View {
        ZStack {
            // Background Gradient
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.black,
                    Color(red: 0.05, green: 0.05, blue: 0.1)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            // Main Content
            TabView(selection: $selectedTab) {
                // Agents Dashboard
                AgentsDashboardTab(agentManager: agentManager)
                    .tabItem {
                        Image(systemName: "person.3.fill")
                        Text("Agents")
                    }
                    .tag(0)
                
                // Trading Dashboard
                TradingDashboardTab(agentManager: agentManager)
                    .tabItem {
                        Image(systemName: "chart.line.uptrend.xyaxis")
                        Text("Trading")
                    }
                    .tag(1)
                
                // Achievements
                AchievementsDashboardTab(agentManager: agentManager)
                    .tabItem {
                        Image(systemName: "trophy.fill")
                        Text("Prestaties")
                    }
                    .tag(2)
                
                // Compliance
                ComplianceDashboardTab(agentManager: agentManager)
                    .tabItem {
                        Image(systemName: "shield.checkered")
                        Text("Compliance")
                    }
                    .tag(3)
                
                // Live Trading
                if let tradingService = realTradingService {
                    LiveTradingDashboardTab(tradingService: tradingService)
                        .tabItem {
                            Image(systemName: "dollarsign.circle.fill")
                            Text("Live Trading")
                        }
                        .tag(4)
                }
            }
            .accentColor(.orange)
            
            // Floating Voice Button
            VStack {
                Spacer()
                HStack {
                    Spacer()
                    VoiceCommandButton(
                        isListening: voiceController.isListening,
                        onTap: handleVoiceCommand
                    )
                    .padding(.trailing, 20)
                    .padding(.bottom, 100)
                }
            }
            
            // Notification Overlay
            if showingNotification {
                NotificationOverlay(message: notificationMessage)
                    .transition(.move(edge: .top))
                    .onAppear {
                        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                            withAnimation {
                                showingNotification = false
                            }
                        }
                    }
            }
        }
        .preferredColorScheme(.dark)
        .onAppear {
            if realTradingService == nil {
                realTradingService = RealTradingService(agentManager: agentManager)
            }
        }
    }
    
    private func handleVoiceCommand() {
        if voiceController.isListening {
            voiceController.stopListening()
        } else {
            voiceController.startListening { command in
                processVoiceCommand(command)
            }
        }
    }
    
    private func processVoiceCommand(_ command: String) {
        let lowercaseCommand = command.lowercased()
        
        // Nederlandse voice commands
        if lowercaseCommand.contains("agent status") {
            showNotification("Agent status: \(agentManager.agents.filter { $0.status == .active }.count) agents actief")
        } else if lowercaseCommand.contains("koop bitcoin") {
            if let tradingService = realTradingService, tradingService.isLiveTrading {
                Task {
                    await tradingService.executeBuyOrder(symbol: "BTCUSDT", amount: 0.001, exchange: .kucoin)
                }
                showNotification("Live Bitcoin kooporder uitgevoerd")
            } else {
                agentManager.cryptoAgent?.processCommand("buy BTCUSDT")
                showNotification("Bitcoin kooporder (simulatie)")
            }
        } else if lowercaseCommand.contains("verkoop bitcoin") {
            if let tradingService = realTradingService, tradingService.isLiveTrading {
                Task {
                    await tradingService.executeSellOrder(symbol: "BTCUSDT", amount: 0.001, exchange: .kucoin)
                }
                showNotification("Live Bitcoin verkooporder uitgevoerd")
            } else {
                agentManager.cryptoAgent?.processCommand("sell BTCUSDT")
                showNotification("Bitcoin verkooporder (simulatie)")
            }
        } else if lowercaseCommand.contains("enable trading") || lowercaseCommand.contains("activeer trading") {
            Task {
                await realTradingService?.enableLiveTrading()
            }
            showNotification("Live trading geactiveerd")
        } else if lowercaseCommand.contains("disable trading") || lowercaseCommand.contains("stop trading") {
            realTradingService?.disableLiveTrading()
            showNotification("Live trading gestopt")
        } else if lowercaseCommand.contains("check balance") || lowercaseCommand.contains("saldo check") {
            Task {
                await realTradingService?.updateAccountBalances()
            }
            showNotification("Account balans wordt bijgewerkt")
        } else {
            showNotification("Commando verwerkt: \(command)")
        }
    }
    
    private func showNotification(_ message: String) {
        notificationMessage = message
        withAnimation(.spring()) {
            showingNotification = true
        }
    }
}

// MARK: - Agents Dashboard Tab
struct AgentsDashboardTab: View {
    @ObservedObject var agentManager: AgentManager
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Global Performance Header
                GlobalPerformanceCard(agentManager: agentManager)
                
                // Agents Grid
                ForEach(agentManager.agents) { agent in
                    AgentCard(agent: agent)
                }
            }
            .padding()
        }
        .background(Color.clear)
    }
}

// MARK: - Agent Card with Glassmorphism
struct AgentCard: View {
    @ObservedObject var agent: Agent
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                // Status Indicator
                Circle()
                    .fill(statusColor)
                    .frame(width: 12, height: 12)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(agent.name)
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    Text(agent.status.rawValue)
                        .font(.caption)
                        .foregroundColor(statusColor)
                }
                
                Spacer()
                
                // Performance Badge
                Text("\(Int(agent.performance))%")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.orange.opacity(0.2))
                    )
            }
            
            // Performance Bar
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color.gray.opacity(0.3))
                        .frame(height: 6)
                    
                    RoundedRectangle(cornerRadius: 4)
                        .fill(LinearGradient(
                            gradient: Gradient(colors: [.orange, .yellow]),
                            startPoint: .leading,
                            endPoint: .trailing
                        ))
                        .frame(width: geometry.size.width * (agent.performance / 100), height: 6)
                }
            }
            .frame(height: 6)
        }
        .padding(16)
        .background(glassmorphismBackground)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.3), radius: 10, x: 0, y: 5)
    }
    
    private var statusColor: Color {
        switch agent.status {
        case .active: return .green
        case .thinking: return .orange
        case .error: return .red
        case .idle: return .gray
        }
    }
    
    private var glassmorphismBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(.ultraThinMaterial)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
            )
    }
}

// MARK: - Supporting Views
struct GlobalPerformanceCard: View {
    @ObservedObject var agentManager: AgentManager
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading) {
                    Text("Systeem Status")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text("\(agentManager.agents.filter { $0.status == .active }.count) van \(agentManager.agents.count) agents actief")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                
                Spacer()
                
                VStack {
                    Text("\(Int(agentManager.globalPerformance))")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                    Text("Performance")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

struct VoiceCommandButton: View {
    let isListening: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                isListening ? .red : .orange,
                                isListening ? .pink : .yellow
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 60, height: 60)
                    .scaleEffect(isListening ? 1.1 : 1.0)
                
                Image(systemName: isListening ? "waveform" : "mic.fill")
                    .foregroundColor(.white)
                    .font(.title2)
                    .fontWeight(.medium)
            }
            .shadow(color: isListening ? .red.opacity(0.5) : .orange.opacity(0.5), radius: 15, x: 0, y: 5)
        }
        .scaleEffect(isListening ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.5).repeatForever(autoreverses: true), value: isListening)
    }
}

struct NotificationOverlay: View {
    let message: String
    
    var body: some View {
        VStack {
            HStack {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                Text(message)
                    .foregroundColor(.white)
                    .font(.caption)
                Spacer()
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.green.opacity(0.3), lineWidth: 1)
                    )
            )
            .padding(.horizontal)
            .padding(.top, 50)
            
            Spacer()
        }
    }
}

// MARK: - Voice Controller
class VoiceController: ObservableObject {
    @Published var isListening = false
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private let audioEngine = AVAudioEngine()
    
    init() {
        setupDutchVoiceRecognition()
    }
    
    func setupDutchVoiceRecognition() {
        speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "nl-NL"))
        
        SFSpeechRecognizer.requestAuthorization { authStatus in
            DispatchQueue.main.async {
                switch authStatus {
                case .authorized:
                    print("✅ Nederlandse spraakherkenning geautoriseerd")
                default:
                    print("❌ Spraakherkenning niet geautoriseerd")
                }
            }
        }
    }
    
    func startListening(completion: @escaping (String) -> Void) {
        guard !isListening else { return }
        
        isListening = true
        
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else { return }
        
        recognitionRequest.shouldReportPartialResults = true
        
        let inputNode = audioEngine.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)
        
        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
            recognitionRequest.append(buffer)
        }
        
        audioEngine.prepare()
        try? audioEngine.start()
        
        recognitionTask = speechRecognizer?.recognitionTask(with: recognitionRequest) { result, error in
            if let result = result, result.isFinal {
                DispatchQueue.main.async {
                    completion(result.bestTranscription.formattedString)
                    self.stopListening()
                }
            }
            
            if error != nil {
                DispatchQueue.main.async {
                    self.stopListening()
                }
            }
        }
    }
    
    func stopListening() {
        audioEngine.stop()
        audioEngine.inputNode.removeTap(onBus: 0)
        recognitionRequest?.endAudio()
        recognitionTask?.cancel()
        
        recognitionRequest = nil
        recognitionTask = nil
        isListening = false
    }
}

// MARK: - Dashboard Tabs
struct TradingDashboardTab: View {
    @ObservedObject var agentManager: AgentManager
    
    var body: some View {
        VStack {
            Text("Trading Dashboard")
                .font(.title)
                .foregroundColor(.white)
            Text("Real-time trading metrics")
                .foregroundColor(.gray)
        }
    }
}

struct AchievementsDashboardTab: View {
    @ObservedObject var agentManager: AgentManager
    
    var body: some View {
        VStack {
            Text("Prestaties")
                .font(.title)
                .foregroundColor(.white)
            Text("Gamification achievements")
                .foregroundColor(.gray)
        }
    }
}

struct ComplianceDashboardTab: View {
    @ObservedObject var agentManager: AgentManager
    
    var body: some View {
        VStack {
            Text("GDPR & AI Act Compliance")
                .font(.title)
                .foregroundColor(.white)
            Text("Compliance dashboard")
                .foregroundColor(.gray)
        }
    }
}

struct LiveTradingDashboardTab: View {
    @ObservedObject var tradingService: RealTradingService
    @State private var showingConfirmation = false
    @State private var pendingAction = ""
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Trading Status Header
                TradingStatusCard(tradingService: tradingService)
                
                // Balance & Position Overview
                BalanceOverviewCard(tradingService: tradingService)
                
                // Trading Controls
                TradingControlsCard(
                    tradingService: tradingService,
                    showingConfirmation: $showingConfirmation,
                    pendingAction: $pendingAction
                )
                
                // Recent Trades
                RecentTradesCard(tradingService: tradingService)
                
                // Exchange Status
                ExchangeStatusCard(tradingService: tradingService)
            }
            .padding()
        }
        .background(Color.clear)
        .confirmationDialog("Confirm Action", isPresented: $showingConfirmation) {
            Button("Confirm \(pendingAction)") {
                handleConfirmation()
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Are you sure you want to \(pendingAction)? This will affect real money.")
        }
    }
    
    private func handleConfirmation() {
        Task {
            switch pendingAction {
            case "Enable Live Trading":
                await tradingService.enableLiveTrading()
            case "Disable Live Trading":
                tradingService.disableLiveTrading()
            case "Switch to Mainnet":
                tradingService.switchToMainnet()
            case "Switch to Testnet":
                tradingService.switchToTestnet()
            default:
                break
            }
        }
    }
}

// MARK: - Trading Dashboard Components

struct TradingStatusCard: View {
    @ObservedObject var tradingService: RealTradingService
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading) {
                    Text("Live Trading Status")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text(tradingService.isLiveTrading ? "ACTIVE" : "INACTIVE")
                        .font(.headline)
                        .foregroundColor(tradingService.isLiveTrading ? .green : .red)
                }
                
                Spacer()
                
                VStack {
                    Circle()
                        .fill(tradingService.isLiveTrading ? .green : .red)
                        .frame(width: 20, height: 20)
                        .scaleEffect(tradingService.isLiveTrading ? 1.2 : 1.0)
                        .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: tradingService.isLiveTrading)
                    
                    Text(tradingService.testnetMode ? "TESTNET" : "MAINNET")
                        .font(.caption)
                        .foregroundColor(tradingService.testnetMode ? .yellow : .orange)
                }
            }
            
            // Quick Stats
            HStack(spacing: 20) {
                QuickStatView(
                    title: "Balance",
                    value: "\(Int(tradingService.tradingBalance)) USDT",
                    color: .green
                )
                
                QuickStatView(
                    title: "Positions",
                    value: "\(tradingService.realPositions.count)",
                    color: .blue
                )
                
                QuickStatView(
                    title: "Trades",
                    value: "\(tradingService.executedTrades.count)",
                    color: .orange
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.green.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

struct BalanceOverviewCard: View {
    @ObservedObject var tradingService: RealTradingService
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Account Overview")
                .font(.headline)
                .foregroundColor(.white)
            
            VStack(spacing: 12) {
                HStack {
                    Text("Total Balance:")
                        .foregroundColor(.gray)
                    Spacer()
                    Text("\(String(format: "%.2f", tradingService.tradingBalance)) USDT")
                        .foregroundColor(.white)
                        .fontWeight(.semibold)
                }
                
                HStack {
                    Text("Max Trade Amount:")
                        .foregroundColor(.gray)
                    Spacer()
                    Text("\(String(format: "%.0f", tradingService.maxTradeAmount)) USDT")
                        .foregroundColor(.orange)
                        .fontWeight(.semibold)
                }
                
                HStack {
                    Text("Stop Loss:")
                        .foregroundColor(.gray)
                    Spacer()
                    Text("\(String(format: "%.1f", tradingService.stopLossPercentage))%")
                        .foregroundColor(.red)
                        .fontWeight(.semibold)
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

struct TradingControlsCard: View {
    @ObservedObject var tradingService: RealTradingService
    @Binding var showingConfirmation: Bool
    @Binding var pendingAction: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Trading Controls")
                .font(.headline)
                .foregroundColor(.white)
            
            VStack(spacing: 12) {
                // Live Trading Toggle
                HStack {
                    Button(tradingService.isLiveTrading ? "🛑 Disable Live Trading" : "🚀 Enable Live Trading") {
                        pendingAction = tradingService.isLiveTrading ? "Disable Live Trading" : "Enable Live Trading"
                        showingConfirmation = true
                    }
                    .foregroundColor(tradingService.isLiveTrading ? .red : .green)
                    .font(.headline)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill((tradingService.isLiveTrading ? Color.red : Color.green).opacity(0.2))
                    )
                }
                
                // Testnet/Mainnet Toggle
                HStack {
                    Button(tradingService.testnetMode ? "⚡ Switch to Mainnet" : "🧪 Switch to Testnet") {
                        pendingAction = tradingService.testnetMode ? "Switch to Mainnet" : "Switch to Testnet"
                        showingConfirmation = true
                    }
                    .foregroundColor(tradingService.testnetMode ? .orange : .yellow)
                    .font(.subheadline)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill((tradingService.testnetMode ? Color.orange : Color.yellow).opacity(0.2))
                    )
                }
                
                // Test Connections Button
                Button("🔍 Test Connections") {
                    Task {
                        await tradingService.testExchangeConnections()
                    }
                }
                .foregroundColor(.cyan)
                .font(.subheadline)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.cyan.opacity(0.2))
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

struct RecentTradesCard: View {
    @ObservedObject var tradingService: RealTradingService
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Recent Trades")
                .font(.headline)
                .foregroundColor(.white)
            
            if tradingService.executedTrades.isEmpty {
                Text("No trades executed yet")
                    .foregroundColor(.gray)
                    .italic()
            } else {
                ForEach(tradingService.executedTrades.suffix(5), id: \.id) { trade in
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(trade.symbol)
                                .foregroundColor(.white)
                                .fontWeight(.semibold)
                            
                            Text(trade.agentReasoning)
                                .font(.caption)
                                .foregroundColor(.gray)
                                .lineLimit(1)
                        }
                        
                        Spacer()
                        
                        VStack(alignment: .trailing, spacing: 4) {
                            Text(trade.side.rawValue.uppercased())
                                .foregroundColor(trade.side == .buy ? .green : .red)
                                .fontWeight(.semibold)
                            
                            Text("$\(String(format: "%.2f", trade.price))")
                                .foregroundColor(.white)
                                .font(.caption)
                        }
                    }
                    .padding(.vertical, 8)
                    .padding(.horizontal, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.gray.opacity(0.1))
                    )
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

struct ExchangeStatusCard: View {
    @ObservedObject var tradingService: RealTradingService
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Exchange Status")
                .font(.headline)
                .foregroundColor(.white)
            
            ForEach(tradingService.allowedExchanges, id: \.self) { exchange in
                HStack {
                    Text(exchange.rawValue.uppercased())
                        .foregroundColor(.white)
                        .fontWeight(.semibold)
                    
                    Spacer()
                    
                    Circle()
                        .fill(.green) // Would need to track actual connection status
                        .frame(width: 10, height: 10)
                    
                    Text("Connected")
                        .font(.caption)
                        .foregroundColor(.green)
                }
                .padding(.vertical, 4)
            }
            
            if let error = tradingService.lastTradingError {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.red)
                    Text(error)
                        .font(.caption)
                        .foregroundColor(.red)
                        .lineLimit(2)
                }
                .padding(.top, 8)
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
} 