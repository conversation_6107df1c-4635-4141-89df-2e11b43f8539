import Foundation

// MARK: - News Analysis Service
class NewsAnalysisService: ObservableObject {
    @Published var latestNews: [NewsItem] = []
    @Published var sentimentData: [NewsSentimentData] = []
    
    private let newsAPIs = [
        "https://api.coindesk.com/v1/news",
        "https://api.cryptonews.com/v1/news",
        "https://newsapi.org/v2/everything?q=crypto"
    ]
    
    // MARK: - News Fetching
    func fetchLatestNews() async -> [NewsItem] {
        var allNews: [NewsItem] = []
        
        // Fetch from multiple sources
        for apiURL in newsAPIs {
            if let news = await fetchNewsFromSource(apiURL) {
                allNews.append(contentsOf: news)
            }
        }
        
        // Add mock news for demo
        allNews.append(contentsOf: generateMockNews())
        
        await MainActor.run {
            self.latestNews = allNews.sorted { $0.timestamp > $1.timestamp }
        }
        
        return allNews
    }
    
    private func fetchNewsFromSource(_ urlString: String) async -> [NewsItem]? {
        // In production, this would make real API calls
        // For now, return mock data
        return generateMockNews()
    }
    
    private func generateMockNews() -> [NewsItem] {
        return [
            NewsItem(
                id: UUID(),
                headline: "Bitcoin Reaches New All-Time High as Institutional Adoption Grows",
                content: "Bitcoin has surged to unprecedented levels as major institutions continue to add cryptocurrency to their portfolios. The recent rally has been driven by increased institutional adoption and positive regulatory developments.",
                timestamp: Date().addingTimeInterval(-3600),
                source: "CryptoNews",
                category: .market,
                sentiment: 0.8
            ),
            NewsItem(
                id: UUID(),
                headline: "Ethereum 2.0 Staking Rewards Attract Record Participation",
                content: "The Ethereum network has seen record-breaking participation in its proof-of-stake consensus mechanism, with over 32 million ETH now staked on the network.",
                timestamp: Date().addingTimeInterval(-7200),
                source: "CoinDesk",
                category: .technology,
                sentiment: 0.7
            ),
            NewsItem(
                id: UUID(),
                headline: "Regulatory Uncertainty Causes Market Volatility",
                content: "Cryptocurrency markets experienced increased volatility following unclear regulatory statements from major economies, causing traders to reassess their positions.",
                timestamp: Date().addingTimeInterval(-10800),
                source: "Financial Times",
                category: .regulation,
                sentiment: 0.3
            ),
            NewsItem(
                id: UUID(),
                headline: "DeFi Protocol Launches Revolutionary Yield Farming Strategy",
                content: "A new decentralized finance protocol has introduced an innovative yield farming mechanism that promises higher returns with reduced impermanent loss risk.",
                timestamp: Date().addingTimeInterval(-14400),
                source: "DeFi Pulse",
                category: .defi,
                sentiment: 0.75
            ),
            NewsItem(
                id: UUID(),
                headline: "Major Exchange Announces Support for New Altcoins",
                content: "Leading cryptocurrency exchange announces listing of several promising altcoin projects, providing increased liquidity and trading opportunities for retail investors.",
                timestamp: Date().addingTimeInterval(-18000),
                source: "Exchange News",
                category: .market,
                sentiment: 0.65
            )
        ]
    }
    
    // MARK: - Sentiment Analysis
    func analyzeSentiment(_ text: String) async -> Double {
        // Advanced NLP sentiment analysis
        let words = text.lowercased().components(separatedBy: .whitespacesAndNewlines)
        
        let positiveWords = ["bullish", "surge", "growth", "positive", "adoption", "breakthrough", "revolutionary", "high", "gains", "profit", "success", "innovative", "promising"]
        let negativeWords = ["bearish", "crash", "decline", "negative", "uncertainty", "volatility", "loss", "risk", "concern", "warning", "regulatory", "ban"]
        
        var positiveScore = 0.0
        var negativeScore = 0.0
        
        for word in words {
            if positiveWords.contains(word) {
                positiveScore += 1.0
            } else if negativeWords.contains(word) {
                negativeScore += 1.0
            }
        }
        
        let totalWords = Double(words.count)
        let netSentiment = (positiveScore - negativeScore) / totalWords
        
        // Normalize to 0-1 scale
        return max(0, min(1, 0.5 + netSentiment * 2))
    }
    
    func calculateMarketImpact(_ news: NewsItem) async -> Double {
        var impact = 0.5 // Base impact
        
        // Category-based impact multiplier
        switch news.category {
        case .regulation:
            impact *= 1.5
        case .market:
            impact *= 1.2
        case .technology:
            impact *= 1.1
        case .defi:
            impact *= 1.0
        }
        
        // Source credibility multiplier
        let sourceMultiplier = getSourceCredibility(news.source)
        impact *= sourceMultiplier
        
        // Recency multiplier
        let hoursOld = abs(news.timestamp.timeIntervalSinceNow) / 3600
        let recencyMultiplier = max(0.1, 1.0 - (hoursOld / 24.0))
        impact *= recencyMultiplier
        
        return min(1.0, impact)
    }
    
    private func getSourceCredibility(_ source: String) -> Double {
        switch source.lowercased() {
        case "coindesk": return 0.9
        case "cointelegraph": return 0.85
        case "cryptonews": return 0.8
        case "financial times": return 0.95
        case "reuters": return 0.95
        case "bloomberg": return 0.9
        default: return 0.7
        }
    }
    
    // MARK: - Social Sentiment
    func fetchSocialSentiment() async -> [SocialSentimentData] {
        // Mock social sentiment data
        let symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "DOTUSDT", "LINKUSDT"]
        let platforms = ["Twitter", "Reddit", "Telegram", "Discord"]
        
        var sentimentData: [SocialSentimentData] = []
        var newsSentimentData: [NewsSentimentData] = []
        
        for symbol in symbols {
            for platform in platforms {
                let sentiment = SocialSentimentData(
                    symbol: symbol,
                    platform: platform,
                    score: Double.random(in: 0.2...0.8),
                    mentionVolume: Int.random(in: 100...5000),
                    timestamp: Date()
                )
                sentimentData.append(sentiment)
            }
            
            // Create aggregated news sentiment data
            let aggregatedSentiment = NewsSentimentData(
                symbol: symbol,
                overallSentiment: Double.random(in: 0.2...0.8),
                newsCount: Int.random(in: 5...50),
                socialMentions: Int.random(in: 100...5000),
                timestamp: Date()
            )
            newsSentimentData.append(aggregatedSentiment)
        }
        
        await MainActor.run {
            self.sentimentData = newsSentimentData
        }
        
        return sentimentData
    }
    
    // MARK: - News Impact Scoring
    func calculateNewsImpactScore(for symbol: String) async -> Double {
        let relevantNews = latestNews.filter { news in
            news.content.lowercased().contains(symbol.lowercased()) ||
            news.headline.lowercased().contains(symbol.lowercased())
        }
        
        guard !relevantNews.isEmpty else { return 0.5 }
        
        let totalImpact = relevantNews.reduce(0.0) { total, news in
            let sentimentWeight = news.sentiment > 0.5 ? news.sentiment : (1.0 - news.sentiment)
            let timeWeight = max(0.1, 1.0 - (abs(news.timestamp.timeIntervalSinceNow) / 86400)) // 24 hours decay
            return total + (sentimentWeight * timeWeight)
        }
        
        return min(1.0, totalImpact / Double(relevantNews.count))
    }
}

// MARK: - Supporting Models
struct NewsItem: Identifiable, Codable {
    let id: UUID
    let headline: String
    let content: String
    let timestamp: Date
    let source: String
    let category: NewsCategory
    let sentiment: Double
}

enum NewsCategory: String, CaseIterable, Codable {
    case market = "Market"
    case regulation = "Regulation"
    case technology = "Technology"
    case defi = "DeFi"
}

struct SocialSentimentData: Identifiable, Codable {
    let id = UUID()
    let symbol: String
    let platform: String
    let score: Double
    let mentionVolume: Int
    let timestamp: Date
}

struct NewsSentimentData: Identifiable, Codable {
    let id = UUID()
    let symbol: String
    let overallSentiment: Double
    let newsCount: Int
    let socialMentions: Int
    let timestamp: Date
} 