import Foundation

// MARK: - AI Trade Manager
class AITradeManager: ObservableObject {
    @Published var activeTrades: [AITrade] = []
    @Published var tradeHistory: [TradeExecution] = []
    @Published var riskMetrics = RiskMetrics()
    
    private var trailingStopTimers: [UUID: Timer] = [:]
    
    // MARK: - Trade Execution
    func executeTrade(_ trade: AITrade, apiService: MultiExchangeAPIService) async throws -> TradeExecution {
        // Validate trade before execution
        try validateTrade(trade)
        
        // Calculate optimal order size and price
        let optimizedTrade = await optimizeTrade(trade, apiService: apiService)
        
        // Execute the trade
        let execution = try await performTradeExecution(optimizedTrade, apiService: apiService)
        
        // Update trade status
        await MainActor.run {
            if let index = self.activeTrades.firstIndex(where: { $0.id == trade.id }) {
                self.activeTrades[index].status = .active
            } else {
                var activeTrade = trade
                activeTrade.status = .active
                self.activeTrades.append(activeTrade)
            }
            
            self.tradeHistory.append(execution)
            self.updateRiskMetrics()
        }
        
        // Start monitoring for trailing stop
        if trade.trailingStopPercent > 0 {
            startTrailingStopMonitoring(for: trade)
        }
        
        return execution
    }
    
    private func validateTrade(_ trade: AITrade) throws {
        // Risk validation
        if trade.quantity <= 0 {
            throw TradeError.invalidQuantity
        }
        
        if trade.entryPrice <= 0 {
            throw TradeError.invalidPrice
        }
        
        // Position size validation (max 5% of portfolio per trade)
        let maxPositionSize = riskMetrics.totalBalance * 0.05
        let tradeValue = trade.quantity * trade.entryPrice
        
        if tradeValue > maxPositionSize {
            throw TradeError.positionTooLarge
        }
        
        // Maximum concurrent trades
        if activeTrades.count >= 10 {
            throw TradeError.tooManyActiveTrades
        }
    }
    
    private func optimizeTrade(_ trade: AITrade, apiService: MultiExchangeAPIService) async -> AITrade {
        var optimizedTrade = trade
        
        // Get current market data
        if let orderBook = await apiService.getOrderBook(symbol: trade.symbol, exchange: trade.exchange) {
            // Optimize entry price based on order book with proper type conversion
            let askPrice = Double(orderBook.asks.first?.price ?? "0") ?? 0.0
            let bidPrice = Double(orderBook.bids.first?.price ?? "0") ?? 0.0
            let spread = askPrice - bidPrice
            let midPrice = (askPrice + bidPrice) / 2.0
            
            // Create new optimized trade with adjusted price
            let optimizedPrice: Double
            if trade.action == .buy {
                optimizedPrice = min(trade.entryPrice, midPrice + spread * 0.1)
            } else {
                optimizedPrice = max(trade.entryPrice, midPrice - spread * 0.1)
            }
            
            // Create new trade with optimized entry price
            optimizedTrade = AITrade(
                id: trade.id,
                symbol: trade.symbol,
                exchange: trade.exchange,
                action: trade.action,
                quantity: trade.quantity,
                entryPrice: optimizedPrice,
                stopLoss: trade.stopLoss,
                takeProfit: trade.takeProfit,
                trailingStopPercent: trade.trailingStopPercent,
                trailingStopPrice: trade.trailingStopPrice,
                status: trade.status,
                reasoning: trade.reasoning,
                timestamp: trade.timestamp
            )
        }
        
        return optimizedTrade
    }
    
    private func performTradeExecution(_ trade: AITrade, apiService: MultiExchangeAPIService) async throws -> TradeExecution {
        // Simulate trade execution - in production this would call real API
        let executionPrice = trade.entryPrice * (1 + Double.random(in: -0.001...0.001)) // Small slippage
        let fees = calculateFees(trade: trade, executionPrice: executionPrice)
        
        let execution = TradeExecution(
            id: UUID(),
            tradeId: trade.id,
            type: .market,
            symbol: trade.symbol,
            exchange: trade.exchange,
            side: trade.action == .buy ? .buy : .sell,
            quantity: trade.quantity,
            price: executionPrice,
            status: .executed,
            timestamp: Date(),
            fees: fees,
            pnl: nil
        )
        
        // Log trade execution
        print("🤖 AI Trade Executed: \(trade.action.rawValue) \(trade.quantity) \(trade.symbol) @ $\(String(format: "%.2f", executionPrice))")
        
        return execution
    }
    
    // MARK: - Trailing Stop Management
    private func startTrailingStopMonitoring(for trade: AITrade) {
        let timer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { [weak self] _ in
            Task {
                await self?.updateTrailingStop(for: trade)
            }
        }
        
        trailingStopTimers[trade.id] = timer
    }
    
    private func updateTrailingStop(for trade: AITrade) async {
        guard let currentPrice = await getCurrentPrice(symbol: trade.symbol, exchange: trade.exchange) else {
            return
        }
        
        await MainActor.run {
            guard let index = self.activeTrades.firstIndex(where: { $0.id == trade.id }) else { return }
            
            var updatedTrade = self.activeTrades[index]
            let trailingDistance = currentPrice * (trade.trailingStopPercent / 100.0)
            
            var shouldExecuteStop = false
            var stopPrice = updatedTrade.stopLoss
            
            if trade.action == .buy {
                // Long position - trailing stop moves up only
                let newStopLoss = currentPrice - trailingDistance
                if newStopLoss > updatedTrade.stopLoss {
                    updatedTrade.stopLoss = newStopLoss
                    updatedTrade.trailingStopPrice = currentPrice
                    print("📈 Trailing Stop Updated: \(trade.symbol) new stop: $\(String(format: "%.2f", newStopLoss))")
                }
                
                // Check if stop should be executed
                if currentPrice <= updatedTrade.stopLoss {
                    shouldExecuteStop = true
                    stopPrice = updatedTrade.stopLoss
                }
                
            } else {
                // Short position - trailing stop moves down only
                let newStopLoss = currentPrice + trailingDistance
                if newStopLoss < updatedTrade.stopLoss {
                    updatedTrade.stopLoss = newStopLoss
                    updatedTrade.trailingStopPrice = currentPrice
                    print("📉 Trailing Stop Updated: \(trade.symbol) new stop: $\(String(format: "%.2f", newStopLoss))")
                }
                
                // Check if stop should be executed
                if currentPrice >= updatedTrade.stopLoss {
                    shouldExecuteStop = true
                    stopPrice = updatedTrade.stopLoss
                }
            }
            
            self.activeTrades[index] = updatedTrade
            
            if shouldExecuteStop {
                Task {
                    await self.executeStopLoss(trade: updatedTrade, stopPrice: stopPrice)
                }
            }
        }
    }
    
    private func executeStopLoss(trade: AITrade, stopPrice: Double) async {
        let fees = calculateFees(trade: trade, executionPrice: stopPrice)
        let pnl = calculatePnL(trade: trade, exitPrice: stopPrice)
        
        let stopExecution = TradeExecution(
            id: UUID(),
            tradeId: trade.id,
            type: .stopLoss,
            symbol: trade.symbol,
            exchange: trade.exchange,
            side: trade.action == .buy ? .sell : .buy,
            quantity: trade.quantity,
            price: stopPrice,
            status: .executed,
            timestamp: Date(),
            fees: fees,
            pnl: pnl
        )
        
        await MainActor.run {
            // Remove from active trades
            self.activeTrades.removeAll { $0.id == trade.id }
            
            // Add to history
            self.tradeHistory.append(stopExecution)
            
            // Stop monitoring
            self.trailingStopTimers[trade.id]?.invalidate()
            self.trailingStopTimers.removeValue(forKey: trade.id)
            
            // Update risk metrics
            self.updateRiskMetrics()
        }
        
        print("🛑 Stop Loss Executed: \(trade.symbol) @ $\(String(format: "%.2f", stopPrice)) | P&L: $\(String(format: "%.2f", pnl))")
    }
    
    // MARK: - Take Profit Management
    func checkTakeProfitLevels() async {
        for trade in activeTrades {
            if let currentPrice = await getCurrentPrice(symbol: trade.symbol, exchange: trade.exchange) {
                let shouldTakeProfit = (trade.action == .buy && currentPrice >= trade.takeProfit) ||
                                     (trade.action == .sell && currentPrice <= trade.takeProfit)
                
                if shouldTakeProfit {
                    await executeTakeProfit(trade: trade, profitPrice: trade.takeProfit)
                }
            }
        }
    }
    
    private func executeTakeProfit(trade: AITrade, profitPrice: Double) async {
        let fees = calculateFees(trade: trade, executionPrice: profitPrice)
        let pnl = calculatePnL(trade: trade, exitPrice: profitPrice)
        
        let profitExecution = TradeExecution(
            id: UUID(),
            tradeId: trade.id,
            type: .takeProfit,
            symbol: trade.symbol,
            exchange: trade.exchange,
            side: trade.action == .buy ? .sell : .buy,
            quantity: trade.quantity,
            price: profitPrice,
            status: .executed,
            timestamp: Date(),
            fees: fees,
            pnl: pnl
        )
        
        await MainActor.run {
            // Remove from active trades
            self.activeTrades.removeAll { $0.id == trade.id }
            
            // Add to history
            self.tradeHistory.append(profitExecution)
            
            // Stop monitoring
            self.trailingStopTimers[trade.id]?.invalidate()
            self.trailingStopTimers.removeValue(forKey: trade.id)
            
            // Update risk metrics
            self.updateRiskMetrics()
        }
        
        print("💰 Take Profit Executed: \(trade.symbol) @ $\(String(format: "%.2f", profitPrice)) | P&L: $\(String(format: "%.2f", pnl))")
    }
    
    // MARK: - Risk Management
    private func updateRiskMetrics() {
        let totalValue = activeTrades.reduce(0) { $0 + ($1.quantity * $1.entryPrice) }
        let totalPnL = tradeHistory.compactMap { $0.pnl }.reduce(0, +)
        let winningTrades = tradeHistory.filter { ($0.pnl ?? 0) > 0 }.count
        let totalTrades = tradeHistory.count
        
        riskMetrics = RiskMetrics(
            totalBalance: 10000 + totalPnL, // Starting balance + P&L
            totalExposure: totalValue,
            totalPnL: totalPnL,
            winRate: totalTrades > 0 ? Double(winningTrades) / Double(totalTrades) : 0.0,
            maxDrawdown: calculateMaxDrawdown(),
            sharpeRatio: calculateSharpeRatio(),
            activeTrades: activeTrades.count
        )
    }
    
    private func calculateMaxDrawdown() -> Double {
        // Simplified max drawdown calculation
        let returns = tradeHistory.compactMap { $0.pnl }
        guard !returns.isEmpty else { return 0.0 }
        
        var maxDrawdown = 0.0
        var peak = 0.0
        var runningTotal = 0.0
        
        for pnl in returns {
            runningTotal += pnl
            peak = max(peak, runningTotal)
            let drawdown = (peak - runningTotal) / max(peak, 1.0)
            maxDrawdown = max(maxDrawdown, drawdown)
        }
        
        return maxDrawdown
    }
    
    private func calculateSharpeRatio() -> Double {
        let returns = tradeHistory.compactMap { $0.pnl }
        guard returns.count > 1 else { return 0.0 }
        
        let meanReturn = returns.reduce(0, +) / Double(returns.count)
        let variance = returns.map { pow($0 - meanReturn, 2) }.reduce(0, +) / Double(returns.count)
        let stdDev = sqrt(variance)
        
        return stdDev > 0 ? meanReturn / stdDev : 0.0
    }
    
    // MARK: - Helper Functions
    private func calculateFees(trade: AITrade, executionPrice: Double) -> Double {
        let feeRate = getFeeRate(exchange: trade.exchange)
        return trade.quantity * executionPrice * feeRate
    }
    
    private func getFeeRate(exchange: SupportedExchange) -> Double {
        switch exchange {
        case .binance: return 0.001   // 0.1%
        case .kucoin: return 0.001    // 0.1%
        case .bybit: return 0.001     // 0.1%
        case .mexc: return 0.002      // 0.2%
        }
    }
    
    private func calculatePnL(trade: AITrade, exitPrice: Double) -> Double {
        let priceDifference = trade.action == .buy ? (exitPrice - trade.entryPrice) : (trade.entryPrice - exitPrice)
        return priceDifference * trade.quantity
    }
    
    private func getCurrentPrice(symbol: String, exchange: SupportedExchange) async -> Double? {
        // Mock current price with realistic volatility
        let basePrices: [String: Double] = [
            "BTCUSDT": 50000,
            "ETHUSDT": 3000,
            "ADAUSDT": 0.5,
            "DOTUSDT": 7.0,
            "LINKUSDT": 15.0
        ]
        
        guard let basePrice = basePrices[symbol] else { return nil }
        
        // Add realistic price movement
        let volatility = 0.02 // 2% volatility
        let randomChange = Double.random(in: -volatility...volatility)
        return basePrice * (1 + randomChange)
    }
    
    // MARK: - Manual Trade Management
    func closeTrade(_ trade: AITrade, reason: String = "Manual Close") async {
        guard let currentPrice = await getCurrentPrice(symbol: trade.symbol, exchange: trade.exchange) else {
            return
        }
        
        let fees = calculateFees(trade: trade, executionPrice: currentPrice)
        let pnl = calculatePnL(trade: trade, exitPrice: currentPrice)
        
        let closeExecution = TradeExecution(
            id: UUID(),
            tradeId: trade.id,
            type: .market,
            symbol: trade.symbol,
            exchange: trade.exchange,
            side: trade.action == .buy ? .sell : .buy,
            quantity: trade.quantity,
            price: currentPrice,
            status: .executed,
            timestamp: Date(),
            fees: fees,
            pnl: pnl
        )
        
        await MainActor.run {
            // Remove from active trades
            self.activeTrades.removeAll { $0.id == trade.id }
            
            // Add to history
            self.tradeHistory.append(closeExecution)
            
            // Stop monitoring
            self.trailingStopTimers[trade.id]?.invalidate()
            self.trailingStopTimers.removeValue(forKey: trade.id)
            
            // Update risk metrics
            self.updateRiskMetrics()
        }
        
        print("✋ Trade Closed Manually: \(trade.symbol) @ $\(String(format: "%.2f", currentPrice)) | P&L: $\(String(format: "%.2f", pnl))")
    }
    
    func updateStopLoss(for trade: AITrade, newStopLoss: Double) async {
        await MainActor.run {
            if let index = self.activeTrades.firstIndex(where: { $0.id == trade.id }) {
                self.activeTrades[index].stopLoss = newStopLoss
                print("🔄 Stop Loss Updated: \(trade.symbol) new stop: $\(String(format: "%.2f", newStopLoss))")
            }
        }
    }
}

// MARK: - Supporting Models
enum TradeError: Error {
    case invalidQuantity
    case invalidPrice
    case positionTooLarge
    case tooManyActiveTrades
    case insufficientBalance
    case marketClosed
    case apiError(String)
    
    var localizedDescription: String {
        switch self {
        case .invalidQuantity:
            return "Invalid trade quantity"
        case .invalidPrice:
            return "Invalid trade price"
        case .positionTooLarge:
            return "Position size exceeds risk limits"
        case .tooManyActiveTrades:
            return "Too many active trades"
        case .insufficientBalance:
            return "Insufficient account balance"
        case .marketClosed:
            return "Market is currently closed"
        case .apiError(let message):
            return "API Error: \(message)"
        }
    }
}

struct RiskMetrics: Codable {
    var totalBalance: Double = 10000.0
    var totalExposure: Double = 0.0
    var totalPnL: Double = 0.0
    var winRate: Double = 0.0
    var maxDrawdown: Double = 0.0
    var sharpeRatio: Double = 0.0
    var activeTrades: Int = 0
    
    var riskPercentage: Double {
        return totalBalance > 0 ? (totalExposure / totalBalance) * 100 : 0.0
    }
    
    var profitFactor: Double {
        let winningTrades = totalPnL > 0 ? totalPnL : 0.0
        let losingTrades = totalPnL < 0 ? abs(totalPnL) : 0.0
        return losingTrades > 0 ? winningTrades / losingTrades : winningTrades > 0 ? 999.0 : 0.0
    }
} 