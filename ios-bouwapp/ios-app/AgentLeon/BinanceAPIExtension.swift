import Foundation
import CryptoKit

// MARK: - Binance API Extension for Real Trading
extension MultiExchangeAPIService {
    
    // MARK: - Binance Market Data
    func fetchBinanceMarketData(symbol: String) async throws -> MarketData {
        let endpoint = "https://api.binance.com/api/v3/ticker/24hr?symbol=\(symbol)"
        let url = URL(string: endpoint)!
        
        let (data, response) = try await urlSession.data(from: url)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.invalidResponse
        }
        
        let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] ?? [:]
        
        // Convert Binance response to MarketData with candlestick data
        let price = Double(json["lastPrice"] as? String ?? "0") ?? 0.0
        let volume = Double(json["volume"] as? String ?? "0") ?? 0.0
        let high = Double(json["highPrice"] as? String ?? "0") ?? 0.0
        let low = Double(json["lowPrice"] as? String ?? "0") ?? 0.0
        let open = Double(json["openPrice"] as? String ?? "0") ?? 0.0
        
        // Create a single candlestick from the 24hr ticker data
        let candlestick = Candlestick(
            open: open,
            high: high,
            low: low,
            close: price,
            volume: volume,
            timestamp: Date()
        )
        
        return MarketData(
            symbol: symbol,
            candlesticks: [candlestick],
            timestamp: Date()
        )
    }
    
    // MARK: - Real Trading Functions
    func placeBinanceOrder(
        symbol: String,
        side: String, // "BUY" or "SELL"
        type: String, // "MARKET", "LIMIT", "STOP_LOSS_LIMIT"
        quantity: String,
        price: String? = nil,
        stopPrice: String? = nil,
        timeInForce: String = "GTC"
    ) async throws -> [String: Any] {
        
        guard let apiKey = getAPIKey(for: .binance),
              let secretKey = getSecretKey(for: .binance) else {
            throw APIError.missingCredentials
        }
        
        let endpoint = "https://api.binance.com/api/v3/order"
        var parameters: [String: String] = [
            "symbol": symbol,
            "side": side,
            "type": type,
            "quantity": quantity,
            "timestamp": String(Int(Date().timeIntervalSince1970 * 1000))
        ]
        
        if let price = price {
            parameters["price"] = price
        }
        
        if let stopPrice = stopPrice {
            parameters["stopPrice"] = stopPrice
        }
        
        if type == "LIMIT" {
            parameters["timeInForce"] = timeInForce
        }
        
        // Generate signature
        let queryString = parameters.sorted { $0.key < $1.key }
            .map { "\($0.key)=\($0.value)" }
            .joined(separator: "&")
        
        let signature = hmacSHA256(data: queryString, key: secretKey)
        parameters["signature"] = signature
        
        // Create request
        var request = URLRequest(url: URL(string: endpoint)!)
        request.httpMethod = "POST"
        request.addValue(apiKey, forHTTPHeaderField: "X-MBX-APIKEY")
        request.addValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        
        let finalQueryString = parameters.map { "\($0.key)=\($0.value)" }.joined(separator: "&")
        request.httpBody = finalQueryString.data(using: .utf8)
        
        let (data, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.invalidResponse
        }
        
        if httpResponse.statusCode != 200 {
            let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any]
            throw APIError.tradingError(errorData?["msg"] as? String ?? "Unknown error")
        }
        
        return try JSONSerialization.jsonObject(with: data) as? [String: Any] ?? [:]
    }
    
    // MARK: - Account Information
    func getBinanceAccountInfo() async throws -> [String: Any] {
        guard let apiKey = getAPIKey(for: .binance),
              let secretKey = getSecretKey(for: .binance) else {
            throw APIError.missingCredentials
        }
        
        let endpoint = "https://api.binance.com/api/v3/account"
        let timestamp = String(Int(Date().timeIntervalSince1970 * 1000))
        let queryString = "timestamp=\(timestamp)"
        let signature = hmacSHA256(data: queryString, key: secretKey)
        
        let url = URL(string: "\(endpoint)?\(queryString)&signature=\(signature)")!
        
        var request = URLRequest(url: url)
        request.addValue(apiKey, forHTTPHeaderField: "X-MBX-APIKEY")
        
        let (data, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.invalidResponse
        }
        
        return try JSONSerialization.jsonObject(with: data) as? [String: Any] ?? [:]
    }
    
    // MARK: - Advanced Analytics
    func getBinanceKlines(
        symbol: String,
        interval: String, // "1m", "5m", "1h", "1d", etc.
        limit: Int = 500
    ) async throws -> [[Any]] {
        let endpoint = "https://api.binance.com/api/v3/klines"
        let url = URL(string: "\(endpoint)?symbol=\(symbol)&interval=\(interval)&limit=\(limit)")!
        
        let (data, response) = try await urlSession.data(from: url)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.invalidResponse
        }
        
        return try JSONSerialization.jsonObject(with: data) as? [[Any]] ?? []
    }
    
    // MARK: - Order Book Depth
    func getBinanceOrderBook(symbol: String, limit: Int = 100) async throws -> [String: Any] {
        let endpoint = "https://api.binance.com/api/v3/depth"
        let url = URL(string: "\(endpoint)?symbol=\(symbol)&limit=\(limit)")!
        
        let (data, response) = try await urlSession.data(from: url)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.invalidResponse
        }
        
        return try JSONSerialization.jsonObject(with: data) as? [String: Any] ?? [:]
    }
    
    // MARK: - Trading History
    func getBinanceTradeHistory(symbol: String, limit: Int = 500) async throws -> [[String: Any]] {
        guard let apiKey = getAPIKey(for: .binance),
              let secretKey = getSecretKey(for: .binance) else {
            throw APIError.missingCredentials
        }
        
        let endpoint = "https://api.binance.com/api/v3/myTrades"
        let timestamp = String(Int(Date().timeIntervalSince1970 * 1000))
        let queryString = "symbol=\(symbol)&limit=\(limit)&timestamp=\(timestamp)"
        let signature = hmacSHA256(data: queryString, key: secretKey)
        
        let url = URL(string: "\(endpoint)?\(queryString)&signature=\(signature)")!
        
        var request = URLRequest(url: url)
        request.addValue(apiKey, forHTTPHeaderField: "X-MBX-APIKEY")
        
        let (data, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.invalidResponse
        }
        
        return try JSONSerialization.jsonObject(with: data) as? [[String: Any]] ?? []
    }
    
    // MARK: - Real-time Price Stream
    func startBinancePriceStream(symbols: [String], onUpdate: @escaping ([String: Any]) -> Void) {
        let streamNames = symbols.map { "\($0.lowercased())@ticker" }.joined(separator: "/")
        let wsURL = URL(string: "wss://stream.binance.com:9443/stream?streams=\(streamNames)")!
        
        let task = URLSession.shared.webSocketTask(with: wsURL)
        task.resume()
        
        func receiveMessage() {
            task.receive { result in
                switch result {
                case .success(let message):
                    switch message {
                    case .string(let text):
                        if let data = text.data(using: .utf8),
                           let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                            DispatchQueue.main.async {
                                onUpdate(json)
                            }
                        }
                    default:
                        break
                    }
                    receiveMessage() // Continue listening
                case .failure(let error):
                    print("WebSocket error: \(error)")
                }
            }
        }
        
        receiveMessage()
    }
    
    // MARK: - Technical Analysis Helpers
    func calculateRSI(prices: [Double], period: Int = 14) -> Double {
        guard prices.count >= period else { return 50.0 }
        
        var gains: [Double] = []
        var losses: [Double] = []
        
        for i in 1..<prices.count {
            let change = prices[i] - prices[i-1]
            if change > 0 {
                gains.append(change)
                losses.append(0)
            } else {
                gains.append(0)
                losses.append(-change)
            }
        }
        
        let avgGain = gains.suffix(period).reduce(0, +) / Double(period)
        let avgLoss = losses.suffix(period).reduce(0, +) / Double(period)
        
        guard avgLoss != 0 else { return 100.0 }
        
        let rs = avgGain / avgLoss
        return 100 - (100 / (1 + rs))
    }
    
    func calculateMACD(prices: [Double]) -> (macd: Double, signal: Double, histogram: Double) {
        let ema12 = calculateEMA(prices: prices, period: 12)
        let ema26 = calculateEMA(prices: prices, period: 26)
        let macd = ema12 - ema26
        
        // Simplified signal line calculation
        let signal = macd * 0.9 // This should be EMA of MACD
        let histogram = macd - signal
        
        return (macd, signal, histogram)
    }
    
    func calculateEMA(prices: [Double], period: Int) -> Double {
        guard !prices.isEmpty else { return 0.0 }
        
        let multiplier = 2.0 / Double(period + 1)
        var ema = prices[0]
        
        for price in prices.dropFirst() {
            ema = (price * multiplier) + (ema * (1 - multiplier))
        }
        
        return ema
    }
    
    // MARK: - Utility Functions
    private func hmacSHA256(data: String, key: String) -> String {
        let keyData = Data(key.utf8)
        let dataData = Data(data.utf8)
        
        let hmac = HMAC<SHA256>.authenticationCode(for: dataData, using: SymmetricKey(data: keyData))
        return Data(hmac).map { String(format: "%02x", $0) }.joined()
    }
    
    private func getAPIKey(for exchange: SupportedExchange) -> String? {
        return KeychainHelper.shared.load(forKey: "\(exchange.rawValue)_api_key") ?? "mock_\(exchange.rawValue.lowercased())_api_key"
    }
    
    private func getSecretKey(for exchange: SupportedExchange) -> String? {
        return KeychainHelper.shared.load(forKey: "\(exchange.rawValue)_secret_key") ?? "mock_\(exchange.rawValue.lowercased())_secret_key"
    }
}

// MARK: - Missing Binance Functions
extension MultiExchangeAPIService {
    
    func testBinanceConnection(config: ExchangeConfiguration) async throws -> Bool {
        let endpoint = "\(config.exchange.baseURL)/api/v3/account"
        guard let url = URL(string: endpoint) else { throw APIError.invalidURL }
        
        let request = try createBinanceAuthenticatedRequest(url: url, method: "GET", body: nil, config: config)
        let (_, response) = try await urlSession.data(for: request)
        
        return (response as? HTTPURLResponse)?.statusCode == 200
    }
    
    func fetchBinanceTradingPairs(config: ExchangeConfiguration, mode: TradingMode) async throws -> [TradingPair] {
        let endpoint = "\(config.exchange.baseURL)/api/v3/exchangeInfo"
        guard let url = URL(string: endpoint) else { throw APIError.invalidURL }
        
        let (data, _) = try await urlSession.data(from: url)
        // Parse response and return trading pairs
        return generateMockTradingPairs(exchange: SupportedExchange.binance, mode: mode)
    }
    
    func placeBinanceOrder(
        config: ExchangeConfiguration,
        symbol: String,
        side: OrderSide,
        type: OrderType,
        amount: Double,
        price: Double?,
        tradingMode: TradingMode,
        stopPrice: Double?,
        leverage: Double?
    ) async throws -> RealOrder {
        // Create mock order for now
        return RealOrder(
            exchange: .binance,
            exchangeOrderId: "binance_\(UUID().uuidString)",
            clientOrderId: "client_\(UUID().uuidString)",
            symbol: symbol,
            side: side,
            type: type,
            amount: amount,
            price: price,
            stopPrice: stopPrice,
            tradingMode: tradingMode,
            status: .filled,
            filledAmount: amount,
            averagePrice: price,
            fees: amount * 0.001,
            feeCurrency: "BNB",
            createdAt: Date(),
            updatedAt: Date(),
            strategy: nil
        )
    }
    
    func fetchBinanceBalance(config: ExchangeConfiguration) async throws -> [String: Double] {
        // Return mock balances for now
        return [
            "BTC": 0.5,
            "ETH": 2.0,
            "BNB": 10.0,
            "USDT": 1000.0
        ]
    }
    
    func cancelBinanceOrder(config: ExchangeConfiguration, orderId: String, symbol: String) async throws -> Bool {
        return true // Mock success
    }
    
    func fetchBinancePositions(config: ExchangeConfiguration, mode: TradingMode) async throws -> [RealPosition] {
        return [] // Return empty for now
    }
    
    func fetchBinanceOrderBook(symbol: String) async throws -> OrderBook {
        let endpoint = "https://api.binance.com/api/v3/depth?symbol=\(symbol)&limit=100"
        guard let url = URL(string: endpoint) else { throw APIError.invalidURL }
        
        let (data, _) = try await urlSession.data(from: url)
        let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] ?? [:]
        
        let bidsArray = json["bids"] as? [[String]] ?? []
        let asksArray = json["asks"] as? [[String]] ?? []
        
        let bids = bidsArray.compactMap { bid -> OrderBookEntry? in
            guard bid.count >= 2,
                  let price = bid[0] as? String,
                  let size = bid[1] as? String else { return nil }
            return OrderBookEntry(price: price, size: size)
        }
        
        let asks = asksArray.compactMap { ask -> OrderBookEntry? in
            guard ask.count >= 2,
                  let price = ask[0] as? String,
                  let size = ask[1] as? String else { return nil }
            return OrderBookEntry(price: price, size: size)
        }
        
        return OrderBook(bids: bids, asks: asks, timestamp: Int64(Date().timeIntervalSince1970 * 1000))
    }
    
    func fetchBinanceTicker(symbol: String) async throws -> Ticker {
        let endpoint = "https://api.binance.com/api/v3/ticker/24hr?symbol=\(symbol)"
        guard let url = URL(string: endpoint) else { throw APIError.invalidURL }
        
        let (data, _) = try await urlSession.data(from: url)
        let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] ?? [:]
        
        return Ticker(
            symbol: json["symbol"] as? String ?? symbol,
            price: json["lastPrice"] as? String ?? "0",
            priceChange: json["priceChange"] as? String ?? "0",
            priceChangePercent: json["priceChangePercent"] as? String ?? "0",
            volume: json["volume"] as? String ?? "0",
            count: json["count"] as? Int ?? 0
        )
    }
    
    private func createBinanceAuthenticatedRequest(
        url: URL,
        method: String,
        body: Data?,
        config: ExchangeConfiguration
    ) throws -> URLRequest {
        var request = URLRequest(url: url)
        request.httpMethod = method
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        if let body = body {
            request.httpBody = body
        }
        
        let timestamp = String(Int64(Date().timeIntervalSince1970 * 1000))
        try addBinanceAuthentication(to: &request, config: config, timestamp: timestamp, body: body)
        
        return request
    }
    
    func addBinanceAuthentication(
        to request: inout URLRequest,
        config: ExchangeConfiguration,
        timestamp: String,
        body: Data?
    ) throws {
        var queryItems: [URLQueryItem] = []
        
        if let url = request.url, let components = URLComponents(url: url, resolvingAgainstBaseURL: false) {
            queryItems = components.queryItems ?? []
        }
        
        queryItems.append(URLQueryItem(name: "timestamp", value: timestamp))
        
        let queryString = queryItems
            .sorted { $0.name < $1.name }
            .map { "\($0.name)=\($0.value ?? "")" }
            .joined(separator: "&")
        
        let signature = try hmacSHA256(key: config.apiSecret, message: queryString)
        queryItems.append(URLQueryItem(name: "signature", value: signature))
        
        if let baseURL = request.url?.absoluteString.components(separatedBy: "?").first {
            let newQueryString = queryItems.map { "\($0.name)=\($0.value ?? "")" }.joined(separator: "&")
            request.url = URL(string: "\(baseURL)?\(newQueryString)")
        }
        
        request.setValue(config.apiKey, forHTTPHeaderField: "X-MBX-APIKEY")
    }
}

 