import SwiftUI
import Charts

// MARK: - Advanced Trading Analytics Dashboard
struct AdvancedTradingAnalytics: View {
    @StateObject private var analyticsManager = TradingAnalyticsManager()
    @State private var selectedSymbol = "BTCUSDT"
    @State private var selectedTimeframe = "1h"
    @State private var selectedExchange = "binance"
    @State private var showOrderBook = false
    @State private var showTechnicalIndicators = true
    
    let timeframes = ["1m", "5m", "15m", "1h", "4h", "1d"]
    let exchanges = ["binance", "kucoin", "mexc", "bybit"]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header Controls
                headerControls
                
                ScrollView {
                    VStack(spacing: 20) {
                        // Price Chart with Technical Indicators
                        priceChartSection
                        
                        // Technical Analysis Panel
                        technicalAnalysisPanel
                        
                        // Order Book & Trade History
                        HStack(spacing: 15) {
                            orderBookSection
                            tradeHistorySection
                        }
                        
                        // Portfolio Performance
                        portfolioPerformanceSection
                        
                        // Risk Management
                        riskManagementSection
                    }
                    .padding()
                }
            }
            .background(Color.black)
            .navigationTitle("Advanced Analytics")
            .navigationBarTitleDisplayMode(.inline)
            .onAppear {
                analyticsManager.startRealTimeData(symbol: selectedSymbol, exchange: selectedExchange)
            }
        }
    }
    
    // MARK: - Header Controls
    private var headerControls: some View {
        VStack(spacing: 12) {
            // Symbol & Exchange Selection
            HStack {
                // Symbol Input
                TextField("Symbol", text: $selectedSymbol)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .frame(width: 120)
                
                // Exchange Picker
                Picker("Exchange", selection: $selectedExchange) {
                    ForEach(exchanges, id: \.self) { exchange in
                        Text(exchange.capitalized).tag(exchange)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                
                Spacer()
                
                // Real-time Price
                VStack(alignment: .trailing) {
                    Text("$\(analyticsManager.currentPrice, specifier: "%.2f")")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                    
                    Text("\(analyticsManager.priceChange24h >= 0 ? "+" : "")\(analyticsManager.priceChange24h, specifier: "%.2f")%")
                        .font(.caption)
                        .foregroundColor(analyticsManager.priceChange24h >= 0 ? .green : .red)
                }
            }
            
            // Timeframe Selection
            HStack {
                Picker("Timeframe", selection: $selectedTimeframe) {
                    ForEach(timeframes, id: \.self) { timeframe in
                        Text(timeframe).tag(timeframe)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                
                Spacer()
                
                Button(action: { showTechnicalIndicators.toggle() }) {
                    Text("Indicators")
                        .foregroundColor(showTechnicalIndicators ? .orange : .gray)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
    }
    
    // MARK: - Price Chart Section
    private var priceChartSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("Price Chart")
                .font(.headline)
                .foregroundColor(.white)
            
            Chart {
                ForEach(analyticsManager.candlestickData.indices, id: \.self) { index in
                    let candle = analyticsManager.candlestickData[index]
                    
                    // Candlestick body
                    RectangleMark(
                        x: .value("Time", index),
                        yStart: .value("Open", min(candle.open, candle.close)),
                        yEnd: .value("Close", max(candle.open, candle.close))
                    )
                    .foregroundStyle(candle.close >= candle.open ? .green : .red)
                    
                    // Candlestick wicks
                    RuleMark(
                        x: .value("Time", index),
                        yStart: .value("Low", candle.low),
                        yEnd: .value("High", candle.high)
                    )
                    .foregroundStyle(.gray)
                    .lineStyle(StrokeStyle(lineWidth: 1))
                }
                
                // Technical Indicators
                if showTechnicalIndicators {
                    // Moving Average
                    ForEach(analyticsManager.movingAverage.indices, id: \.self) { index in
                        LineMark(
                            x: .value("Time", index),
                            y: .value("MA", analyticsManager.movingAverage[index])
                        )
                        .foregroundStyle(.blue)
                    }
                    
                    // Bollinger Bands
                    ForEach(analyticsManager.bollingerBands.indices, id: \.self) { index in
                        let band = analyticsManager.bollingerBands[index]
                        
                        LineMark(
                            x: .value("Time", index),
                            y: .value("Upper", band.upper)
                        )
                        .foregroundStyle(.purple)
                        .opacity(0.6)
                        
                        LineMark(
                            x: .value("Time", index),
                            y: .value("Lower", band.lower)
                        )
                        .foregroundStyle(.purple)
                        .opacity(0.6)
                    }
                }
            }
            .frame(height: 300)
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
        }
    }
    
    // MARK: - Technical Analysis Panel
    private var technicalAnalysisPanel: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Technical Indicators")
                .font(.headline)
                .foregroundColor(.white)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 15) {
                // RSI
                indicatorCard(
                    title: "RSI (14)",
                    value: String(format: "%.1f", analyticsManager.rsi),
                    status: rsiStatus(analyticsManager.rsi),
                    color: rsiColor(analyticsManager.rsi)
                )
                
                // MACD
                indicatorCard(
                    title: "MACD",
                    value: String(format: "%.4f", analyticsManager.macd.macd),
                    status: analyticsManager.macd.macd > analyticsManager.macd.signal ? "Bullish" : "Bearish",
                    color: analyticsManager.macd.macd > analyticsManager.macd.signal ? .green : .red
                )
                
                // Volume
                indicatorCard(
                    title: "Volume (24h)",
                    value: formatVolume(analyticsManager.volume24h),
                    status: "Active",
                    color: .blue
                )
                
                // Support/Resistance
                indicatorCard(
                    title: "Support",
                    value: String(format: "$%.2f", analyticsManager.supportLevel),
                    status: "Strong",
                    color: .green
                )
            }
        }
    }
    
    // MARK: - Order Book Section
    private var orderBookSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("Order Book")
                .font(.headline)
                .foregroundColor(.white)
            
            VStack(spacing: 5) {
                // Asks (Sell orders)
                ForEach(analyticsManager.orderBook.asks.prefix(5), id: \.price) { order in
                    HStack {
                        Text("\(order.price, specifier: "%.2f")")
                            .foregroundColor(.red)
                        Spacer()
                        Text("\(order.quantity, specifier: "%.4f")")
                            .foregroundColor(.white)
                    }
                    .font(.caption)
                }
                
                Divider()
                
                // Current Price
                HStack {
                    Spacer()
                    Text("$\(analyticsManager.currentPrice, specifier: "%.2f")")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                    Spacer()
                }
                
                Divider()
                
                // Bids (Buy orders)
                ForEach(analyticsManager.orderBook.bids.prefix(5), id: \.price) { order in
                    HStack {
                        Text("\(order.price, specifier: "%.2f")")
                            .foregroundColor(.green)
                        Spacer()
                        Text("\(order.quantity, specifier: "%.4f")")
                            .foregroundColor(.white)
                    }
                    .font(.caption)
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
        }
    }
    
    // MARK: - Trade History Section
    private var tradeHistorySection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("Recent Trades")
                .font(.headline)
                .foregroundColor(.white)
            
            VStack(spacing: 5) {
                ForEach(analyticsManager.recentTrades.prefix(10), id: \.id) { trade in
                    HStack {
                        Text("\(trade.price, specifier: "%.2f")")
                            .foregroundColor(trade.side == "buy" ? .green : .red)
                        
                        Spacer()
                        
                        Text("\(trade.quantity, specifier: "%.4f")")
                            .foregroundColor(.white)
                        
                        Text(timeAgo(trade.timestamp))
                            .foregroundColor(.gray)
                    }
                    .font(.caption)
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
        }
    }
    
    // MARK: - Portfolio Performance Section
    private var portfolioPerformanceSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Portfolio Performance")
                .font(.headline)
                .foregroundColor(.white)
            
            HStack {
                VStack(alignment: .leading) {
                    Text("Total Value")
                        .foregroundColor(.gray)
                    Text("$\(analyticsManager.portfolioValue, specifier: "%.2f")")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("24h P&L")
                        .foregroundColor(.gray)
                    Text("\(analyticsManager.dailyPnL >= 0 ? "+" : "")$\(analyticsManager.dailyPnL, specifier: "%.2f")")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(analyticsManager.dailyPnL >= 0 ? .green : .red)
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
        }
    }
    
    // MARK: - Risk Management Section
    private var riskManagementSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Risk Management")
                .font(.headline)
                .foregroundColor(.white)
            
            VStack(spacing: 10) {
                HStack {
                    Text("Position Size")
                    Spacer()
                    Text("\(analyticsManager.positionSize, specifier: "%.2f")%")
                        .foregroundColor(.orange)
                }
                
                HStack {
                    Text("Stop Loss")
                    Spacer()
                    Text("$\(analyticsManager.stopLoss, specifier: "%.2f")")
                        .foregroundColor(.red)
                }
                
                HStack {
                    Text("Take Profit")
                    Spacer()
                    Text("$\(analyticsManager.takeProfit, specifier: "%.2f")")
                        .foregroundColor(.green)
                }
                
                HStack {
                    Text("Risk/Reward")
                    Spacer()
                    Text("1:\(analyticsManager.riskRewardRatio, specifier: "%.1f")")
                        .foregroundColor(.blue)
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
        }
    }
    
    // MARK: - Helper Functions
    private func indicatorCard(title: String, value: String, status: String, color: Color) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.caption)
                .foregroundColor(.gray)
            
            Text(value)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(status)
                .font(.caption)
                .foregroundColor(.white)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private func rsiStatus(_ rsi: Double) -> String {
        if rsi > 70 { return "Overbought" }
        else if rsi < 30 { return "Oversold" }
        else { return "Neutral" }
    }
    
    private func rsiColor(_ rsi: Double) -> Color {
        if rsi > 70 { return .red }
        else if rsi < 30 { return .green }
        else { return .orange }
    }
    
    private func formatVolume(_ volume: Double) -> String {
        if volume > 1_000_000 {
            return String(format: "%.1fM", volume / 1_000_000)
        } else if volume > 1_000 {
            return String(format: "%.1fK", volume / 1_000)
        } else {
            return String(format: "%.0f", volume)
        }
    }
    
    private func timeAgo(_ timestamp: Date) -> String {
        let interval = Date().timeIntervalSince(timestamp)
        
        if interval < 60 {
            return "\(Int(interval))s"
        } else if interval < 3600 {
            return "\(Int(interval / 60))m"
        } else {
            return "\(Int(interval / 3600))h"
        }
    }
}

// MARK: - Supporting Data Models
struct CandlestickData {
    let open: Double
    let high: Double
    let low: Double
    let close: Double
    let volume: Double
    let timestamp: Date
}

struct BollingerBand {
    let upper: Double
    let middle: Double
    let lower: Double
}

struct AnalyticsOrderBookEntry {
    let price: Double
    let quantity: Double
}

struct AnalyticsOrderBook {
    let bids: [AnalyticsOrderBookEntry]
    let asks: [AnalyticsOrderBookEntry]
}

struct Trade {
    let id: String
    let price: Double
    let quantity: Double
    let side: String
    let timestamp: Date
}

struct AnalyticsMACDData {
    let macd: Double
    let signal: Double
    let histogram: Double
}

// MARK: - Trading Analytics Manager
class TradingAnalyticsManager: ObservableObject {
    @Published var currentPrice: Double = 45250.0
    @Published var priceChange24h: Double = 2.35
    @Published var volume24h: Double = 1_250_000
    @Published var candlestickData: [CandlestickData] = []
    @Published var movingAverage: [Double] = []
    @Published var bollingerBands: [BollingerBand] = []
    @Published var rsi: Double = 65.5
    @Published var macd: AnalyticsMACDData = AnalyticsMACDData(macd: 0.0025, signal: 0.0020, histogram: 0.0005)
    @Published var orderBook: AnalyticsOrderBook = AnalyticsOrderBook(bids: [], asks: [])
    @Published var recentTrades: [Trade] = []
    @Published var supportLevel: Double = 44800.0
    @Published var resistanceLevel: Double = 46500.0
    @Published var portfolioValue: Double = 125_450.0
    @Published var dailyPnL: Double = 2_850.0
    @Published var positionSize: Double = 15.5
    @Published var stopLoss: Double = 43200.0
    @Published var takeProfit: Double = 48000.0
    @Published var riskRewardRatio: Double = 2.8
    
    private var timer: Timer?
    
    func startRealTimeData(symbol: String, exchange: String) {
        // Generate initial data
        generateMockData()
        
        // Start real-time updates
        timer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { _ in
            self.updateRealTimeData()
        }
    }
    
    private func generateMockData() {
        // Generate candlestick data
        var data: [CandlestickData] = []
        var price = 45000.0
        
        for i in 0..<100 {
            let change = Double.random(in: -200...200)
            let open = price
            let close = price + change
            let high = max(open, close) + Double.random(in: 0...100)
            let low = min(open, close) - Double.random(in: 0...100)
            
            data.append(CandlestickData(
                open: open,
                high: high,
                low: low,
                close: close,
                volume: Double.random(in: 10...100),
                timestamp: Date().addingTimeInterval(TimeInterval(-i * 3600))
            ))
            
            price = close
        }
        
        candlestickData = data.reversed()
        
        // Generate moving average
        movingAverage = candlestickData.enumerated().map { index, _ in
            let start = max(0, index - 19)
            let subset = Array(candlestickData[start...index])
            return subset.reduce(0) { $0 + $1.close } / Double(subset.count)
        }
        
        // Generate Bollinger Bands
        bollingerBands = movingAverage.enumerated().map { index, ma in
            let start = max(0, index - 19)
            let subset = Array(candlestickData[start...index])
            let variance = subset.reduce(0) { $0 + pow($1.close - ma, 2) } / Double(subset.count)
            let stdDev = sqrt(variance)
            
            return BollingerBand(
                upper: ma + (2 * stdDev),
                middle: ma,
                lower: ma - (2 * stdDev)
            )
        }
        
        // Generate order book
        var bids: [AnalyticsOrderBookEntry] = []
        var asks: [AnalyticsOrderBookEntry] = []
        
        for i in 1...10 {
            bids.append(AnalyticsOrderBookEntry(
                price: currentPrice - Double(i) * 10,
                quantity: Double.random(in: 0.1...5.0)
            ))
            
            asks.append(AnalyticsOrderBookEntry(
                price: currentPrice + Double(i) * 10,
                quantity: Double.random(in: 0.1...5.0)
            ))
        }
        
        orderBook = AnalyticsOrderBook(bids: bids, asks: asks)
        
        // Generate recent trades
        recentTrades = (0..<20).map { i in
            Trade(
                id: UUID().uuidString,
                price: currentPrice + Double.random(in: -50...50),
                quantity: Double.random(in: 0.01...2.0),
                side: Bool.random() ? "buy" : "sell",
                timestamp: Date().addingTimeInterval(-TimeInterval(i * 30))
            )
        }
    }
    
    private func updateRealTimeData() {
        // Update current price with realistic movement
        let change = Double.random(in: -100...100)
        currentPrice += change
        
        // Update 24h change
        priceChange24h += Double.random(in: -0.5...0.5)
        
        // Update RSI
        rsi += Double.random(in: -2...2)
        rsi = max(0, min(100, rsi))
        
        // Update portfolio value
        portfolioValue += Double.random(in: -500...500)
        
        // Update daily P&L
        dailyPnL += Double.random(in: -100...100)
    }
    
    deinit {
        timer?.invalidate()
    }
} 