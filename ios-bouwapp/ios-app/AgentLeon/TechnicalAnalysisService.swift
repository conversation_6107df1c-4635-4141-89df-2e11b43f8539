import Foundation

// MARK: - Technical Analysis Service
class TechnicalAnalysisService: ObservableObject {
    @Published var analysisResults: [String: TechnicalAnalysisData] = [:]
    
    // MARK: - Main Analysis Function
    func analyze(_ marketData: MarketData) async -> TechnicalAnalysisData {
        let prices = marketData.candlesticks.map { $0.close }
        let volumes = marketData.candlesticks.map { $0.volume }
        let highs = marketData.candlesticks.map { $0.high }
        let lows = marketData.candlesticks.map { $0.low }
        
        let rsi = calculateRSI(prices: prices)
        let macd = calculateMACD(prices: prices)
        let bollinger = calculateBollingerBands(prices: prices)
        let ema20 = calculateEMA(prices: prices, period: 20)
        let ema50 = calculateEMA(prices: prices, period: 50)
        let support = calculateSupportLevel(lows: lows)
        let resistance = calculateResistanceLevel(highs: highs)
        let trend = determineTrend(ema20: ema20, ema50: ema50, prices: prices)
        let volumeAnalysis = analyzeVolume(volumes: volumes, prices: prices)
        let patterns = detectPatterns(marketData: marketData)
        
        let analysisData = TechnicalAnalysisData(
            rsi: rsi,
            macd: macd,
            bollingerBands: bollinger,
            ema20: ema20,
            ema50: ema50,
            supportLevel: support,
            resistanceLevel: resistance,
            trend: trend,
            volumeAnalysis: volumeAnalysis,
            patterns: patterns,
            timestamp: Date()
        )
        
        await MainActor.run {
            self.analysisResults[marketData.symbol] = analysisData
        }
        
        return analysisData
    }
    
    // MARK: - RSI Calculation
    private func calculateRSI(prices: [Double], period: Int = 14) -> Double {
        guard prices.count > period else { return 50.0 }
        
        var gains: [Double] = []
        var losses: [Double] = []
        
        for i in 1..<prices.count {
            let change = prices[i] - prices[i-1]
            gains.append(max(change, 0))
            losses.append(max(-change, 0))
        }
        
        let avgGain = gains.suffix(period).reduce(0, +) / Double(period)
        let avgLoss = losses.suffix(period).reduce(0, +) / Double(period)
        
        guard avgLoss != 0 else { return 100.0 }
        
        let rs = avgGain / avgLoss
        return 100 - (100 / (1 + rs))
    }
    
    // MARK: - MACD Calculation
    private func calculateMACD(prices: [Double]) -> MACDData {
        let ema12 = calculateEMA(prices: prices, period: 12)
        let ema26 = calculateEMA(prices: prices, period: 26)
        let macdLine = ema12 - ema26
        
        // Calculate signal line (9-period EMA of MACD)
        let macdHistory = Array(repeating: macdLine, count: 9) // Simplified
        let signalLine = calculateEMA(prices: macdHistory, period: 9)
        
        let histogram = macdLine - signalLine
        
        return MACDData(macd: macdLine, signal: signalLine, histogram: histogram)
    }
    
    // MARK: - EMA Calculation
    private func calculateEMA(prices: [Double], period: Int) -> Double {
        guard !prices.isEmpty else { return 0.0 }
        guard prices.count >= period else { return prices.last ?? 0.0 }
        
        let multiplier = 2.0 / Double(period + 1)
        var ema = prices[0]
        
        for i in 1..<prices.count {
            ema = (prices[i] * multiplier) + (ema * (1 - multiplier))
        }
        
        return ema
    }
    
    // MARK: - Bollinger Bands Calculation
    private func calculateBollingerBands(prices: [Double], period: Int = 20, stdDev: Double = 2.0) -> BollingerBands {
        guard prices.count >= period else {
            let price = prices.last ?? 0.0
            return BollingerBands(upper: price, middle: price, lower: price)
        }
        
        let recentPrices = Array(prices.suffix(period))
        let sma = recentPrices.reduce(0, +) / Double(period)
        
        let variance = recentPrices.map { pow($0 - sma, 2) }.reduce(0, +) / Double(period)
        let standardDeviation = sqrt(variance)
        
        return BollingerBands(
            upper: sma + (standardDeviation * stdDev),
            middle: sma,
            lower: sma - (standardDeviation * stdDev)
        )
    }
    
    // MARK: - Support and Resistance Levels
    private func calculateSupportLevel(lows: [Double]) -> Double {
        guard !lows.isEmpty else { return 0.0 }
        
        let recentLows = Array(lows.suffix(20))
        let sortedLows = recentLows.sorted()
        
        // Support is around the 25th percentile of recent lows
        let index = max(0, sortedLows.count / 4)
        return sortedLows[index]
    }
    
    private func calculateResistanceLevel(highs: [Double]) -> Double {
        guard !highs.isEmpty else { return 0.0 }
        
        let recentHighs = Array(highs.suffix(20))
        let sortedHighs = recentHighs.sorted(by: >)
        
        // Resistance is around the 25th percentile of recent highs
        let index = max(0, sortedHighs.count / 4)
        return sortedHighs[index]
    }
    
    // MARK: - Trend Analysis
    private func determineTrend(ema20: Double, ema50: Double, prices: [Double]) -> TrendDirection {
        guard prices.count >= 2 else { return .sideways }
        
        let currentPrice = prices.last ?? 0.0
        let previousPrice = prices[prices.count - 2]
        
        // Multiple criteria for trend determination
        let emaSignal = ema20 > ema50
        let priceSignal = currentPrice > previousPrice
        let strengthSignal = abs(ema20 - ema50) / ema50 > 0.02 // 2% difference threshold
        
        if emaSignal && priceSignal && strengthSignal {
            return .uptrend
        } else if !emaSignal && !priceSignal && strengthSignal {
            return .downtrend
        } else {
            return .sideways
        }
    }
    
    // MARK: - Volume Analysis
    private func analyzeVolume(volumes: [Double], prices: [Double]) -> VolumeAnalysis {
        guard volumes.count >= 20 && prices.count >= 20 else {
            return VolumeAnalysis(
                averageVolume: volumes.last ?? 0.0,
                volumeTrend: .neutral,
                volumePriceCorrelation: 0.0,
                volumeSpike: false
            )
        }
        
        let recentVolumes = Array(volumes.suffix(20))
        let averageVolume = recentVolumes.reduce(0, +) / Double(recentVolumes.count)
        let currentVolume = volumes.last ?? 0.0
        
        // Volume trend
        let volumeTrend: VolumeTrend
        if currentVolume > averageVolume * 1.5 {
            volumeTrend = .increasing
        } else if currentVolume < averageVolume * 0.5 {
            volumeTrend = .decreasing
        } else {
            volumeTrend = .neutral
        }
        
        // Volume-Price Correlation
        let recentPrices = Array(prices.suffix(20))
        let correlation = calculateCorrelation(recentVolumes, recentPrices)
        
        // Volume Spike Detection
        let volumeSpike = currentVolume > averageVolume * 2.0
        
        return VolumeAnalysis(
            averageVolume: averageVolume,
            volumeTrend: volumeTrend,
            volumePriceCorrelation: correlation,
            volumeSpike: volumeSpike
        )
    }
    
    // MARK: - Pattern Detection
    private func detectPatterns(marketData: MarketData) -> [ChartPattern] {
        var patterns: [ChartPattern] = []
        let candlesticks = marketData.candlesticks
        
        guard candlesticks.count >= 10 else { return patterns }
        
        // Head and Shoulders Pattern
        if let headAndShoulders = detectHeadAndShoulders(candlesticks) {
            patterns.append(headAndShoulders)
        }
        
        // Double Top/Bottom
        if let doublePattern = detectDoubleTopBottom(candlesticks) {
            patterns.append(doublePattern)
        }
        
        // Triangle Patterns
        if let triangle = detectTrianglePattern(candlesticks) {
            patterns.append(triangle)
        }
        
        // Flag and Pennant
        if let flagPattern = detectFlagPattern(candlesticks) {
            patterns.append(flagPattern)
        }
        
        return patterns
    }
    
    private func detectHeadAndShoulders(_ candlesticks: [Candlestick]) -> ChartPattern? {
        // Simplified head and shoulders detection
        guard candlesticks.count >= 7 else { return nil }
        
        let recentCandles = Array(candlesticks.suffix(7))
        let highs = recentCandles.map { $0.high }
        
        // Look for pattern: low, high, low, higher_high, low, high, low
        if highs[1] > highs[0] && highs[1] < highs[3] && highs[3] > highs[5] && highs[5] > highs[6] {
            return ChartPattern(
                type: .headAndShoulders,
                confidence: 0.7,
                description: "Head and Shoulders pattern detected",
                signal: .bearish,
                timestamp: Date()
            )
        }
        
        return nil
    }
    
    private func detectDoubleTopBottom(_ candlesticks: [Candlestick]) -> ChartPattern? {
        guard candlesticks.count >= 10 else { return nil }
        
        let recentCandles = Array(candlesticks.suffix(10))
        let highs = recentCandles.map { $0.high }
        let lows = recentCandles.map { $0.low }
        
        // Double Top Detection
        let maxHigh = highs.max() ?? 0
        let highIndices = highs.enumerated().compactMap { $1 > maxHigh * 0.98 ? $0 : nil }
        
        if highIndices.count >= 2 && highIndices.last! - highIndices.first! >= 5 {
            return ChartPattern(
                type: .doubleTop,
                confidence: 0.6,
                description: "Double Top pattern detected",
                signal: .bearish,
                timestamp: Date()
            )
        }
        
        // Double Bottom Detection
        let minLow = lows.min() ?? 0
        let lowIndices = lows.enumerated().compactMap { $1 < minLow * 1.02 ? $0 : nil }
        
        if lowIndices.count >= 2 && lowIndices.last! - lowIndices.first! >= 5 {
            return ChartPattern(
                type: .doubleBottom,
                confidence: 0.6,
                description: "Double Bottom pattern detected",
                signal: .bullish,
                timestamp: Date()
            )
        }
        
        return nil
    }
    
    private func detectTrianglePattern(_ candlesticks: [Candlestick]) -> ChartPattern? {
        guard candlesticks.count >= 15 else { return nil }
        
        let recentCandles = Array(candlesticks.suffix(15))
        let highs = recentCandles.map { $0.high }
        let lows = recentCandles.map { $0.low }
        
        // Calculate trend lines
        let highTrend = calculateTrendLine(highs)
        let lowTrend = calculateTrendLine(lows)
        
        // Ascending Triangle
        if abs(highTrend) < 0.001 && lowTrend > 0 {
            return ChartPattern(
                type: .ascendingTriangle,
                confidence: 0.65,
                description: "Ascending Triangle pattern detected",
                signal: .bullish,
                timestamp: Date()
            )
        }
        
        // Descending Triangle
        if abs(lowTrend) < 0.001 && highTrend < 0 {
            return ChartPattern(
                type: .descendingTriangle,
                confidence: 0.65,
                description: "Descending Triangle pattern detected",
                signal: .bearish,
                timestamp: Date()
            )
        }
        
        return nil
    }
    
    private func detectFlagPattern(_ candlesticks: [Candlestick]) -> ChartPattern? {
        guard candlesticks.count >= 20 else { return nil }
        
        let recentCandles = Array(candlesticks.suffix(20))
        let prices = recentCandles.map { $0.close }
        
        // Look for strong move followed by consolidation
        let firstHalf = Array(prices.prefix(10))
        let secondHalf = Array(prices.suffix(10))
        
        let firstHalfTrend = calculateTrendLine(firstHalf)
        let secondHalfVolatility = calculateVolatility(secondHalf)
        
        if abs(firstHalfTrend) > 0.05 && secondHalfVolatility < 0.02 {
            return ChartPattern(
                type: .flag,
                confidence: 0.55,
                description: "Flag pattern detected",
                signal: firstHalfTrend > 0 ? .bullish : .bearish,
                timestamp: Date()
            )
        }
        
        return nil
    }
    
    // MARK: - Volume Pattern Analysis
    func analyzeVolumePatterns(symbol: String) async -> VolumePatternResult {
        // Mock volume pattern analysis
        let patterns = ["Accumulation", "Distribution", "Breakout", "Climax"]
        let signals: [AnalysisSignal] = [.bullish, .bearish, .bullish, .bearish]
        
        let randomIndex = Int.random(in: 0..<patterns.count)
        let pattern = patterns[randomIndex]
        let signal = signals[randomIndex]
        
        return VolumePatternResult(
            pattern: pattern,
            signal: signal,
            confidence: Double.random(in: 0.5...0.9),
            strength: Double.random(in: 0.3...1.0)
        )
    }
    
    // MARK: - Helper Functions
    private func calculateCorrelation(_ x: [Double], _ y: [Double]) -> Double {
        guard x.count == y.count && !x.isEmpty else { return 0.0 }
        
        let n = Double(x.count)
        let sumX = x.reduce(0, +)
        let sumY = y.reduce(0, +)
        let sumXY = zip(x, y).map(*).reduce(0, +)
        let sumX2 = x.map { $0 * $0 }.reduce(0, +)
        let sumY2 = y.map { $0 * $0 }.reduce(0, +)
        
        let numerator = n * sumXY - sumX * sumY
        let denominator = sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY))
        
        return denominator != 0 ? numerator / denominator : 0.0
    }
    
    private func calculateTrendLine(_ values: [Double]) -> Double {
        guard values.count > 1 else { return 0.0 }
        
        let n = Double(values.count)
        let x = Array(0..<values.count).map { Double($0) }
        let y = values
        
        let sumX = x.reduce(0, +)
        let sumY = y.reduce(0, +)
        let sumXY = zip(x, y).map(*).reduce(0, +)
        let sumX2 = x.map { $0 * $0 }.reduce(0, +)
        
        let slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX)
        return slope
    }
    
    private func calculateVolatility(_ prices: [Double]) -> Double {
        guard prices.count > 1 else { return 0.0 }
        
        let returns = zip(prices.dropFirst(), prices).map { (current, previous) in
            (current - previous) / previous
        }
        
        let meanReturn = returns.reduce(0, +) / Double(returns.count)
        let variance = returns.map { pow($0 - meanReturn, 2) }.reduce(0, +) / Double(returns.count)
        
        return sqrt(variance)
    }
}

// MARK: - Supporting Models
struct TechnicalAnalysisData: Codable {
    let rsi: Double
    let macd: MACDData
    let bollingerBands: BollingerBands
    let ema20: Double
    let ema50: Double
    let supportLevel: Double
    let resistanceLevel: Double
    let trend: TrendDirection
    let volumeAnalysis: VolumeAnalysis
    let patterns: [ChartPattern]
    let timestamp: Date
}

struct MACDData: Codable {
    let macd: Double
    let signal: Double
    let histogram: Double
}

struct BollingerBands: Codable {
    let upper: Double
    let middle: Double
    let lower: Double
}

enum TrendDirection: String, CaseIterable, Codable {
    case uptrend = "Uptrend"
    case downtrend = "Downtrend"
    case sideways = "Sideways"
}

struct VolumeAnalysis: Codable {
    let averageVolume: Double
    let volumeTrend: VolumeTrend
    let volumePriceCorrelation: Double
    let volumeSpike: Bool
}

enum VolumeTrend: String, CaseIterable, Codable {
    case increasing = "Increasing"
    case decreasing = "Decreasing"
    case neutral = "Neutral"
}

struct ChartPattern: Identifiable, Codable {
    let id = UUID()
    let type: PatternType
    let confidence: Double
    let description: String
    let signal: AnalysisSignal
    let timestamp: Date
}

enum PatternType: String, CaseIterable, Codable {
    case headAndShoulders = "Head and Shoulders"
    case doubleTop = "Double Top"
    case doubleBottom = "Double Bottom"
    case ascendingTriangle = "Ascending Triangle"
    case descendingTriangle = "Descending Triangle"
    case flag = "Flag"
    case pennant = "Pennant"
    case cup = "Cup and Handle"
}

struct VolumePatternResult: Codable {
    let pattern: String
    let signal: AnalysisSignal
    let confidence: Double
    let strength: Double
} 