import Foundation

// MARK: - Exchange Enums
enum SupportedExchange: String, CaseIterable, Codable {
    case binance = "Binance"
    case kucoin = "KuCoin"
    case bybit = "Bybit"
    case mexc = "MEXC"
    
    var iconName: String {
        switch self {
        case .binance: return "b.circle.fill"
        case .kucoin: return "k.circle.fill"
        case .bybit: return "circle.fill"
        case .mexc: return "m.circle.fill"
        }
    }
    
    var baseURL: String {
        switch self {
        case .binance: return "https://api.binance.com"
        case .kucoin: return "https://api.kucoin.com"
        case .bybit: return "https://api.bybit.com"
        case .mexc: return "https://api.mexc.com"
        }
    }
}

enum TradingMode: String, CaseIterable, Codable {
    case spot = "Spot"
    case margin = "Margin"
    case futures = "Futures"
    case leverageToken = "Leverage Token"
    
    var description: String {
        switch self {
        case .spot: return "Spot Trading"
        case .margin: return "Margin Trading"
        case .futures: return "Futures Trading"
        case .leverageToken: return "Leverage Tokens"
        }
    }
}

enum OrderSide: String, CaseIterable, Codable {
    case buy = "buy"
    case sell = "sell"
    
    var displayName: String {
        return rawValue.capitalized
    }
    
    var emoji: String {
        switch self {
        case .buy: return "🟢"
        case .sell: return "🔴"
        }
    }
}

enum OrderType: String, CaseIterable, Codable {
    case market = "market"
    case limit = "limit"
    case stopLoss = "stop_loss"
    case stopLossLimit = "stop_loss_limit"
    case takeProfit = "take_profit"
    case takeProfitLimit = "take_profit_limit"
    
    var displayName: String {
        switch self {
        case .market: return "Market"
        case .limit: return "Limit"
        case .stopLoss: return "Stop Loss"
        case .stopLossLimit: return "Stop Loss Limit"
        case .takeProfit: return "Take Profit"
        case .takeProfitLimit: return "Take Profit Limit"
        }
    }
    
    var isStopOrder: Bool {
        switch self {
        case .stopLoss, .stopLossLimit, .takeProfit, .takeProfitLimit:
            return true
        case .market, .limit:
            return false
        }
    }
}

// MARK: - Exchange Configuration
struct ExchangeConfiguration: Codable, Identifiable {
    let id = UUID()
    let exchange: SupportedExchange
    var apiKey: String
    var apiSecret: String
    var passphrase: String? // For KuCoin
    var testnet: Bool
    var isEnabled: Bool
    var tradingModes: [TradingMode]
    
    init(exchange: SupportedExchange) {
        self.exchange = exchange
        self.apiKey = ""
        self.apiSecret = ""
        self.passphrase = exchange == .kucoin ? "" : nil
        self.testnet = true
        self.isEnabled = false
        self.tradingModes = [.spot] // Default to spot
    }
}

// MARK: - Trading Pair
struct TradingPair: Codable, Identifiable, Hashable {
    let id = UUID()
    let symbol: String
    let baseAsset: String
    let quoteAsset: String
    let exchange: SupportedExchange
    let tradingMode: TradingMode
    var price: Double
    var priceChange24h: Double
    var volume24h: Double
    let minQty: Double
    let maxQty: Double
    let stepSize: Double
    let tickSize: Double
    let isActive: Bool
    
    var displaySymbol: String {
        return "\(baseAsset)/\(quoteAsset)"
    }
    
    var priceChangeFormatted: String {
        let sign = priceChange24h >= 0 ? "+" : ""
        return "\(sign)\(String(format: "%.2f", priceChange24h))%"
    }
}

// MARK: - Advanced Trading Strategy
enum AdvancedTradingStrategy: String, CaseIterable, Codable {
    case trendFollowing = "trend_following"
    case meanReversion = "mean_reversion"
    case breakoutTrading = "breakout_trading"
    case dcaStrategy = "dca_strategy"
    case gridTrading = "grid_trading"
    case arbitrage = "arbitrage"
    case scalping = "scalping"
    case martingale = "martingale"
    
    var displayName: String {
        switch self {
        case .trendFollowing: return "Trend Following"
        case .meanReversion: return "Mean Reversion"
        case .breakoutTrading: return "Breakout Trading"
        case .dcaStrategy: return "DCA Strategy"
        case .gridTrading: return "Grid Trading"
        case .arbitrage: return "Arbitrage"
        case .scalping: return "Scalping"
        case .martingale: return "Martingale"
        }
    }
    
    var description: String {
        switch self {
        case .trendFollowing: return "Follow strong market trends"
        case .meanReversion: return "Buy low, sell high on reversals"
        case .breakoutTrading: return "Trade breakouts from ranges"
        case .dcaStrategy: return "Dollar Cost Averaging"
        case .gridTrading: return "Place grid of buy/sell orders"
        case .arbitrage: return "Exploit price differences"
        case .scalping: return "Quick small profit trades"
        case .martingale: return "Double down on losses"
        }
    }
    
    var riskLevel: RiskLevel {
        switch self {
        case .dcaStrategy, .gridTrading: return .low
        case .trendFollowing, .meanReversion, .arbitrage: return .medium
        case .breakoutTrading, .scalping: return .high
        case .martingale: return .veryHigh
        }
    }
}

enum RiskLevel: String, CaseIterable, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case veryHigh = "very_high"
    
    var displayName: String {
        switch self {
        case .low: return "Low Risk"
        case .medium: return "Medium Risk"
        case .high: return "High Risk"
        case .veryHigh: return "Very High Risk"
        }
    }
    
    var color: String {
        switch self {
        case .low: return "green"
        case .medium: return "yellow"
        case .high: return "orange"
        case .veryHigh: return "red"
        }
    }
}

// MARK: - Strategy Configuration
struct StrategyConfiguration: Codable, Identifiable {
    let id = UUID()
    let strategy: AdvancedTradingStrategy
    var isEnabled: Bool
    var exchanges: [SupportedExchange]
    var tradingPairs: [String]
    var maxPositionSize: Double
    var stopLossPercentage: Double
    var takeProfitPercentage: Double
    var leverage: Double
    var parameters: [String: Double]
    
    init(strategy: AdvancedTradingStrategy) {
        self.strategy = strategy
        self.isEnabled = false
        self.exchanges = [.kucoin]
        self.tradingPairs = ["BTC/USDT"]
        self.maxPositionSize = 1000.0
        self.stopLossPercentage = 5.0
        self.takeProfitPercentage = 10.0
        self.leverage = 1.0
        self.parameters = strategy.defaultParameters
    }
}

// MARK: - Default Strategy Parameters
extension AdvancedTradingStrategy {
    var defaultParameters: [String: Double] {
        switch self {
        case .trendFollowing:
            return ["ema_short": 12, "ema_long": 26, "signal_line": 9, "min_trend_strength": 0.7]
        case .meanReversion:
            return ["rsi_oversold": 30, "rsi_overbought": 70, "bollinger_period": 20, "bollinger_std": 2]
        case .breakoutTrading:
            return ["breakout_period": 20, "volume_multiplier": 1.5, "atr_multiplier": 2]
        case .dcaStrategy:
            return ["interval_hours": 24, "percentage_drop": 5, "max_orders": 10]
        case .gridTrading:
            return ["grid_size": 0.5, "grid_count": 20, "range_percentage": 10]
        case .arbitrage:
            return ["min_profit_percentage": 0.3, "max_exposure": 5000]
        case .scalping:
            return ["profit_target": 0.1, "max_hold_time": 5, "volume_threshold": 1000000]
        case .martingale:
            return ["multiplier": 2, "max_doubling": 5, "reset_profit": 1]
        }
    }
}

// MARK: - Real Order
struct RealOrder: Codable, Identifiable {
    let id = UUID()
    let exchange: SupportedExchange
    let exchangeOrderId: String
    let clientOrderId: String
    let symbol: String
    let side: OrderSide
    let type: OrderType
    let amount: Double
    let price: Double?
    let stopPrice: Double?
    let tradingMode: TradingMode
    let status: OrderStatus
    let filledAmount: Double
    let averagePrice: Double?
    let fees: Double
    let feeCurrency: String
    let createdAt: Date
    let updatedAt: Date
    let strategy: AdvancedTradingStrategy?
    
    var totalValue: Double {
        return amount * (price ?? 0)
    }
    
    var filledPercentage: Double {
        return amount > 0 ? (filledAmount / amount) * 100 : 0
    }
}

enum OrderStatus: String, CaseIterable, Codable {
    case pending = "pending"
    case active = "active"
    case partiallyFilled = "partially_filled"
    case filled = "filled"
    case cancelled = "cancelled"
    case rejected = "rejected"
    case expired = "expired"
    
    var displayName: String {
        switch self {
        case .pending: return "Pending"
        case .active: return "Active"
        case .partiallyFilled: return "Partially Filled"
        case .filled: return "Filled"
        case .cancelled: return "Cancelled"
        case .rejected: return "Rejected"
        case .expired: return "Expired"
        }
    }
    
    var color: String {
        switch self {
        case .pending: return "yellow"
        case .active: return "blue"
        case .partiallyFilled: return "orange"
        case .filled: return "green"
        case .cancelled, .expired: return "gray"
        case .rejected: return "red"
        }
    }
}



// MARK: - Real Position
struct RealPosition: Codable, Identifiable {
    let id = UUID()
    let exchange: SupportedExchange
    let symbol: String
    let side: OrderSide
    let size: Double
    let entryPrice: Double
    let markPrice: Double
    let leverage: Double
    let margin: Double
    let tradingMode: TradingMode
    let unrealizedPnl: Double
    let unrealizedPnlPercentage: Double
    let createdAt: Date
    let strategy: AdvancedTradingStrategy?
    
    var currentValue: Double {
        return size * markPrice
    }
    
    var marginLevel: Double {
        return margin > 0 ? (currentValue / margin) * 100 : 0
    }
}

// MARK: - Exchange API Response Models
struct APIResponse<T: Codable>: Codable {
    let code: String
    let msg: String?
    let data: T?
    let success: Bool
}

struct OrderBookEntry: Codable {
    let price: String
    let size: String
}

struct OrderBook: Codable {
    let bids: [OrderBookEntry]
    let asks: [OrderBookEntry]
    let timestamp: Int64
}

struct Ticker: Codable {
    let symbol: String
    let price: String
    let priceChange: String
    let priceChangePercent: String
    let volume: String
    let count: Int?
} 