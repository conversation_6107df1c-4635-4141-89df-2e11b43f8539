import SwiftUI

struct APIConfigurationView: View {
    @StateObject private var aiService = AITradingService()
    @StateObject private var marketDataService = MarketDataService()
    @StateObject private var apiManager = APIManager()
    
    @State private var selectedTab = 0
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var testResults: [String: Bool] = [:]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerView
                
                // Tab Selector
                tabSelectorView
                
                // Content
                TabView(selection: $selectedTab) {
                    // AI Models Tab
                    aiModelsConfigView
                        .tag(0)
                    
                    // Exchange APIs Tab
                    exchangeAPIsConfigView
                        .tag(1)
                    
                    // Market Data APIs Tab
                    marketDataAPIsConfigView
                        .tag(2)
                    
                    // Social & News APIs Tab
                    socialNewsAPIsConfigView
                        .tag(3)
                    
                    // Advanced APIs Tab
                    advancedAPIsConfigView
                        .tag(4)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                
                // Bottom Actions
                bottomActionsView
            }
            .background(Color.black)
            .navigationBarHidden(true)
        }
        .preferredColorScheme(.dark)
        .onAppear {
            loadAPIKeys()
        }
        .alert("API Test Result", isPresented: $showingAlert) {
            Button("OK") { }
        } message: {
            Text(alertMessage)
        }
    }
    
    // MARK: - Header
    private var headerView: some View {
        HStack {
            Text("🤖 AI Trading Configuration")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.cyan)
            
            Spacer()
            
            Button("Save All") {
                saveAllAPIKeys()
            }
            .foregroundColor(.green)
            .font(.headline)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
    }
    
    // MARK: - Tab Selector
    private var tabSelectorView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 0) {
                TabButton(title: "AI Models", isSelected: selectedTab == 0) {
                    selectedTab = 0
                }
                TabButton(title: "Exchanges", isSelected: selectedTab == 1) {
                    selectedTab = 1
                }
                TabButton(title: "Market Data", isSelected: selectedTab == 2) {
                    selectedTab = 2
                }
                TabButton(title: "Social & News", isSelected: selectedTab == 3) {
                    selectedTab = 3
                }
                TabButton(title: "Advanced", isSelected: selectedTab == 4) {
                    selectedTab = 4
                }
            }
            .padding(.horizontal)
        }
        .background(Color.gray.opacity(0.05))
    }
    
    // MARK: - AI Models Configuration
    private var aiModelsConfigView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // OpenRouter Configuration
                APIConfigCard(
                    title: "🤖 OpenRouter AI",
                    description: "Claude, GPT-4, Gemini, Llama models",
                    isEnabled: !aiService.aiApiKey.isEmpty
                ) {
                    VStack(spacing: 12) {
                        APIKeyField(
                            title: "OpenRouter API Key",
                            value: $aiService.aiApiKey,
                            placeholder: "sk-or-v1-..."
                        )
                        
                        Picker("AI Model", selection: $aiService.selectedModel) {
                            ForEach(aiService.availableModels, id: \.id) { model in
                                Text("\(model.name) (\(model.provider))").tag(model.id)
                            }
                        }
                        .pickerStyle(MenuPickerStyle())
                        
                        Toggle("Enable AI Trading", isOn: $aiService.isAIEnabled)
                    }
                }
                
                // OpenAI Configuration
                APIConfigCard(
                    title: "🧠 OpenAI",
                    description: "GPT-4 Turbo, GPT-3.5 models",
                    isEnabled: !apiManager.openaiAPIKey.isEmpty
                ) {
                    VStack(spacing: 12) {
                        APIKeyField(
                            title: "OpenAI API Key",
                            value: $apiManager.openaiAPIKey,
                            placeholder: "sk-proj-..."
                        )
                        
                        TestButton(api: "OpenAI") {
                            testOpenAIConnection()
                        }
                    }
                }
                
                // Gemini Configuration
                APIConfigCard(
                    title: "🔮 Google Gemini",
                    description: "Gemini Pro, Ultra models",
                    isEnabled: !apiManager.geminiAPIKey.isEmpty
                ) {
                    VStack(spacing: 12) {
                        APIKeyField(
                            title: "Gemini API Key",
                            value: $apiManager.geminiAPIKey,
                            placeholder: "AIzaSy..."
                        )
                        
                        TestButton(api: "Gemini") {
                            testGeminiConnection()
                        }
                    }
                }
            }
            .padding()
        }
    }
    
    // MARK: - Exchange APIs Configuration
    private var exchangeAPIsConfigView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // KuCoin Configuration
                APIConfigCard(
                    title: "🟢 KuCoin",
                    description: "Spot, Margin, Futures trading",
                    isEnabled: !apiManager.kucoinAPIKey.isEmpty
                ) {
                    VStack(spacing: 12) {
                        APIKeyField(
                            title: "API Key",
                            value: $apiManager.kucoinAPIKey,
                            placeholder: "************************"
                        )
                        
                        APIKeyField(
                            title: "Secret Key",
                            value: $apiManager.kucoinSecretKey,
                            placeholder: "c4fab7b9-3120-4ede-a7c9-5f3645b6acec",
                            isSecure: true
                        )
                        
                        APIKeyField(
                            title: "Passphrase",
                            value: $apiManager.kucoinPassphrase,
                            placeholder: "Your KuCoin passphrase"
                        )
                        
                        Toggle("Use Testnet", isOn: $apiManager.kucoinTestnet)
                        
                        TestButton(api: "KuCoin") {
                            testKuCoinConnection()
                        }
                    }
                }
                
                // MEXC Configuration
                APIConfigCard(
                    title: "🔴 MEXC",
                    description: "Global crypto exchange",
                    isEnabled: !apiManager.mexcAPIKey.isEmpty
                ) {
                    VStack(spacing: 12) {
                        APIKeyField(
                            title: "API Key",
                            value: $apiManager.mexcAPIKey,
                            placeholder: "mx0vglmGyrf9LW7tY9"
                        )
                        
                        APIKeyField(
                            title: "Secret Key",
                            value: $apiManager.mexcSecretKey,
                            placeholder: "b5bb5493128b483c9ebe199d013042c1",
                            isSecure: true
                        )
                        
                        TestButton(api: "MEXC") {
                            testMEXCConnection()
                        }
                    }
                }
            }
            .padding()
        }
    }
    
    // MARK: - Market Data APIs Configuration
    private var marketDataAPIsConfigView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // NewsAPI Configuration
                APIConfigCard(
                    title: "📰 NewsAPI",
                    description: "Global news sentiment analysis",
                    isEnabled: !apiManager.newsAPIKey.isEmpty
                ) {
                    VStack(spacing: 12) {
                        APIKeyField(
                            title: "API Key",
                            value: $apiManager.newsAPIKey,
                            placeholder: "********************************"
                        )
                        
                        TestButton(api: "NewsAPI") {
                            testNewsAPIConnection()
                        }
                    }
                }
                
                // Alpha Vantage
                APIConfigCard(
                    title: "📊 Alpha Vantage",
                    description: "Technical indicators & economic data",
                    isEnabled: !apiManager.alphaVantageAPIKey.isEmpty
                ) {
                    VStack(spacing: 12) {
                        APIKeyField(
                            title: "API Key",
                            value: $apiManager.alphaVantageAPIKey,
                            placeholder: "c5ac2099739a1cf389ec0cff907f7e2a"
                        )
                        
                        TestButton(api: "Alpha Vantage") {
                            testAlphaVantageConnection()
                        }
                    }
                }
            }
            .padding()
        }
    }
    
    // MARK: - Social & News APIs Configuration
    private var socialNewsAPIsConfigView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Twitter/X API
                APIConfigCard(
                    title: "🐦 Twitter/X",
                    description: "Social sentiment analysis",
                    isEnabled: !apiManager.twitterAPIKey.isEmpty
                ) {
                    VStack(spacing: 12) {
                        APIKeyField(
                            title: "API Key",
                            value: $apiManager.twitterAPIKey,
                            placeholder: "1879278649385394176-..."
                        )
                        
                        APIKeyField(
                            title: "API Secret",
                            value: $apiManager.twitterAPISecret,
                            placeholder: "lm9wRs15DgfvlialmqGeiYp5ydW4LOyQ3vfCZ82Zx",
                            isSecure: true
                        )
                        
                        TestButton(api: "Twitter") {
                            testTwitterConnection()
                        }
                    }
                }
                
                // Telegram Bot
                APIConfigCard(
                    title: "📱 Telegram Bot",
                    description: "Trading notifications & alerts",
                    isEnabled: !apiManager.telegramBotToken.isEmpty
                ) {
                    VStack(spacing: 12) {
                        APIKeyField(
                            title: "Bot Token",
                            value: $apiManager.telegramBotToken,
                            placeholder: "**********************************************"
                        )
                        
                        APIKeyField(
                            title: "Chat ID",
                            value: $apiManager.telegramChatID,
                            placeholder: "7844558981"
                        )
                        
                        TestButton(api: "Telegram") {
                            testTelegramConnection()
                        }
                    }
                }
            }
            .padding()
        }
    }
    
    // MARK: - Advanced APIs Configuration
    private var advancedAPIsConfigView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // AI Trading Settings
                APIConfigCard(
                    title: "🤖 AI Trading Settings",
                    description: "Advanced AI configuration",
                    isEnabled: true
                ) {
                    VStack(spacing: 12) {
                        Toggle("Multi-Model Consensus", isOn: $apiManager.multiModelEnabled)
                        
                        Toggle("Sentiment Analysis", isOn: $apiManager.sentimentAnalysisEnabled)
                        
                        Toggle("Machine Learning Models", isOn: $apiManager.mlModelsEnabled)
                        
                        Toggle("Anomaly Detection", isOn: $apiManager.anomalyDetectionEnabled)
                    }
                }
                
                // Risk Management
                APIConfigCard(
                    title: "⚠️ Risk Management",
                    description: "Safety and risk controls",
                    isEnabled: true
                ) {
                    VStack(spacing: 12) {
                        HStack {
                            Text("Max Position Size:")
                            Spacer()
                            TextField("10000", value: $apiManager.maxPositionSize, format: .number)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .frame(width: 120)
                        }
                        
                        HStack {
                            Text("Sentiment Weight:")
                            Spacer()
                            Text("\(apiManager.sentimentWeight, specifier: "%.1f")")
                        }
                        Slider(value: $apiManager.sentimentWeight, in: 0.0...1.0, step: 0.1)
                    }
                }
            }
            .padding()
        }
    }
    
    // MARK: - Bottom Actions
    private var bottomActionsView: some View {
        HStack(spacing: 20) {
            Button("Load Defaults") {
                loadDefaultAPIKeys()
            }
            .foregroundColor(.cyan)
            .padding()
            .background(Color.cyan.opacity(0.2))
            .cornerRadius(12)
            
            Button("Save All") {
                saveAllAPIKeys()
            }
            .foregroundColor(.green)
            .padding()
            .background(Color.green.opacity(0.2))
            .cornerRadius(12)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
    }
    
    // MARK: - Helper Functions
    private func loadAPIKeys() {
        loadDefaultAPIKeys()
    }
    
    private func loadDefaultAPIKeys() {
        // Load all the API keys from cursor rules
        apiManager.mexcAPIKey = "mx0vglmGyrf9LW7tY9"
        apiManager.mexcSecretKey = "b5bb5493128b483c9ebe199d013042c1"
        
        apiManager.geminiAPIKey = "AIzaSyAhqdsZvQ8_-WT7Z3uE_j0zjUMDeP_9rdk"
        
        apiManager.kucoinAPIKey = "************************"
        apiManager.kucoinSecretKey = "c4fab7b9-3120-4ede-a7c9-5f3645b6acec"
        apiManager.kucoinPassphrase = "Kucoinn"
        
        apiManager.openaiAPIKey = "********************************************************************************************************************************************************************"
        
        apiManager.telegramBotToken = "**********************************************"
        apiManager.telegramChatID = "7844558981"
        
        apiManager.twitterAPIKey = "1879278649385394176-4Wv74MlavLsxNRKE4ViwIlmrKbrAAN"
        apiManager.twitterAPISecret = "lm9wRs15DgfvlialmqGeiYp5ydW4LOyQ3vfCZ82Zx"
        
        apiManager.newsAPIKey = "********************************"
        apiManager.alphaVantageAPIKey = "c5ac2099739a1cf389ec0cff907f7e2a"
        
        // Copy to AI service
        aiService.aiApiKey = apiManager.openaiAPIKey
        
        alertMessage = "✅ Default API keys loaded from configuration!"
        showingAlert = true
    }
    
    private func saveAllAPIKeys() {
        apiManager.saveAllKeysToKeychain()
        alertMessage = "✅ All API keys saved successfully!"
        showingAlert = true
    }
    
    // Test functions
    private func testKuCoinConnection() {
        alertMessage = "Testing KuCoin connection..."
        showingAlert = true
    }
    
    private func testMEXCConnection() {
        alertMessage = "Testing MEXC connection..."
        showingAlert = true
    }
    
    private func testOpenAIConnection() {
        alertMessage = "Testing OpenAI connection..."
        showingAlert = true
    }
    
    private func testGeminiConnection() {
        alertMessage = "Testing Gemini connection..."
        showingAlert = true
    }
    
    private func testAlphaVantageConnection() {
        alertMessage = "Testing Alpha Vantage connection..."
        showingAlert = true
    }
    
    private func testNewsAPIConnection() {
        alertMessage = "Testing NewsAPI connection..."
        showingAlert = true
    }
    
    private func testTwitterConnection() {
        alertMessage = "Testing Twitter connection..."
        showingAlert = true
    }
    
    private func testTelegramConnection() {
        alertMessage = "Testing Telegram connection..."
        showingAlert = true
    }
}

// MARK: - Supporting Views
struct TabButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.caption)
                .fontWeight(isSelected ? .bold : .regular)
                .foregroundColor(isSelected ? .cyan : .gray)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    isSelected ? Color.cyan.opacity(0.2) : Color.clear
                )
                .cornerRadius(8)
        }
    }
}

struct APIConfigCard<Content: View>: View {
    let title: String
    let description: String
    let isEnabled: Bool
    @ViewBuilder let content: Content
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.white)
                
                Spacer()
                
                Circle()
                    .fill(isEnabled ? Color.green : Color.red)
                    .frame(width: 10, height: 10)
            }
            
            Text(description)
                .font(.caption)
                .foregroundColor(.gray)
            
            content
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
}

struct APIKeyField: View {
    let title: String
    @Binding var value: String
    let placeholder: String
    var isSecure: Bool = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(.gray)
            
            if isSecure {
                SecureField(placeholder, text: $value)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            } else {
                TextField(placeholder, text: $value)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
        }
    }
}

struct TestButton: View {
    let api: String
    let action: () -> Void
    
    var body: some View {
        Button("Test \(api) Connection") {
            action()
        }
        .foregroundColor(.cyan)
        .font(.caption)
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(Color.cyan.opacity(0.2))
        .cornerRadius(8)
    }
}

// MARK: - API Manager
class APIManager: ObservableObject {
    // Exchange APIs
    @Published var kucoinAPIKey: String {
        didSet { KeychainHelper.shared.save(kucoinAPIKey, forKey: "kucoinAPIKey") }
    }
    @Published var kucoinSecretKey: String {
        didSet { KeychainHelper.shared.save(kucoinSecretKey, forKey: "kucoinSecretKey") }
    }
    @Published var kucoinPassphrase: String {
        didSet { KeychainHelper.shared.save(kucoinPassphrase, forKey: "kucoinPassphrase") }
    }
    @Published var kucoinTestnet: Bool {
        didSet { UserDefaults.standard.set(kucoinTestnet, forKey: "kucoinTestnet") }
    }

    @Published var mexcAPIKey: String {
        didSet { KeychainHelper.shared.save(mexcAPIKey, forKey: "mexcAPIKey") }
    }
    @Published var mexcSecretKey: String {
        didSet { KeychainHelper.shared.save(mexcSecretKey, forKey: "mexcSecretKey") }
    }

    // AI APIs
    @Published var openaiAPIKey: String {
        didSet { KeychainHelper.shared.save(openaiAPIKey, forKey: "openaiAPIKey") }
    }
    @Published var geminiAPIKey: String {
        didSet { KeychainHelper.shared.save(geminiAPIKey, forKey: "geminiAPIKey") }
    }

    // Market Data APIs
    @Published var alphaVantageAPIKey: String {
        didSet { KeychainHelper.shared.save(alphaVantageAPIKey, forKey: "alphaVantageAPIKey") }
    }
    @Published var newsAPIKey: String {
        didSet { KeychainHelper.shared.save(newsAPIKey, forKey: "newsAPIKey") }
    }

    // Social APIs
    @Published var twitterAPIKey: String {
        didSet { KeychainHelper.shared.save(twitterAPIKey, forKey: "twitterAPIKey") }
    }
    @Published var twitterAPISecret: String {
        didSet { KeychainHelper.shared.save(twitterAPISecret, forKey: "twitterAPISecret") }
    }

    // Notification APIs
    @Published var telegramBotToken: String {
        didSet { KeychainHelper.shared.save(telegramBotToken, forKey: "telegramBotToken") }
    }
    @Published var telegramChatID: String {
        didSet { KeychainHelper.shared.save(telegramChatID, forKey: "telegramChatID") }
    }

    // Advanced Settings
    @Published var multiModelEnabled: Bool {
        didSet { UserDefaults.standard.set(multiModelEnabled, forKey: "multiModelEnabled") }
    }
    @Published var sentimentAnalysisEnabled: Bool {
        didSet { UserDefaults.standard.set(sentimentAnalysisEnabled, forKey: "sentimentAnalysisEnabled") }
    }
    @Published var mlModelsEnabled: Bool {
        didSet { UserDefaults.standard.set(mlModelsEnabled, forKey: "mlModelsEnabled") }
    }
    @Published var anomalyDetectionEnabled: Bool {
        didSet { UserDefaults.standard.set(anomalyDetectionEnabled, forKey: "anomalyDetectionEnabled") }
    }
    @Published var maxPositionSize: Double {
        didSet { UserDefaults.standard.set(maxPositionSize, forKey: "maxPositionSize") }
    }
    @Published var sentimentWeight: Double {
        didSet { UserDefaults.standard.set(sentimentWeight, forKey: "sentimentWeight") }
    }

    init() {
        // Exchange APIs
        self.kucoinAPIKey = KeychainHelper.shared.load(forKey: "kucoinAPIKey") ?? ""
        self.kucoinSecretKey = KeychainHelper.shared.load(forKey: "kucoinSecretKey") ?? ""
        self.kucoinPassphrase = KeychainHelper.shared.load(forKey: "kucoinPassphrase") ?? ""
        self.kucoinTestnet = UserDefaults.standard.bool(forKey: "kucoinTestnet")
        self.mexcAPIKey = KeychainHelper.shared.load(forKey: "mexcAPIKey") ?? ""
        self.mexcSecretKey = KeychainHelper.shared.load(forKey: "mexcSecretKey") ?? ""
        // AI APIs
        self.openaiAPIKey = KeychainHelper.shared.load(forKey: "openaiAPIKey") ?? ""
        self.geminiAPIKey = KeychainHelper.shared.load(forKey: "geminiAPIKey") ?? ""
        // Market Data APIs
        self.alphaVantageAPIKey = KeychainHelper.shared.load(forKey: "alphaVantageAPIKey") ?? ""
        self.newsAPIKey = KeychainHelper.shared.load(forKey: "newsAPIKey") ?? ""
        // Social APIs
        self.twitterAPIKey = KeychainHelper.shared.load(forKey: "twitterAPIKey") ?? ""
        self.twitterAPISecret = KeychainHelper.shared.load(forKey: "twitterAPISecret") ?? ""
        // Notification APIs
        self.telegramBotToken = KeychainHelper.shared.load(forKey: "telegramBotToken") ?? ""
        self.telegramChatID = KeychainHelper.shared.load(forKey: "telegramChatID") ?? ""
        // Advanced Settings
        self.multiModelEnabled = UserDefaults.standard.object(forKey: "multiModelEnabled") as? Bool ?? true
        self.sentimentAnalysisEnabled = UserDefaults.standard.object(forKey: "sentimentAnalysisEnabled") as? Bool ?? true
        self.mlModelsEnabled = UserDefaults.standard.object(forKey: "mlModelsEnabled") as? Bool ?? true
        self.anomalyDetectionEnabled = UserDefaults.standard.object(forKey: "anomalyDetectionEnabled") as? Bool ?? true
        self.maxPositionSize = UserDefaults.standard.object(forKey: "maxPositionSize") as? Double ?? 10000.0
        self.sentimentWeight = UserDefaults.standard.object(forKey: "sentimentWeight") as? Double ?? 0.3
    }

    func saveAllKeysToKeychain() {
        KeychainHelper.shared.save(kucoinAPIKey, forKey: "kucoinAPIKey")
        KeychainHelper.shared.save(kucoinSecretKey, forKey: "kucoinSecretKey")
        KeychainHelper.shared.save(kucoinPassphrase, forKey: "kucoinPassphrase")
        KeychainHelper.shared.save(mexcAPIKey, forKey: "mexcAPIKey")
        KeychainHelper.shared.save(mexcSecretKey, forKey: "mexcSecretKey")
        KeychainHelper.shared.save(openaiAPIKey, forKey: "openaiAPIKey")
        KeychainHelper.shared.save(geminiAPIKey, forKey: "geminiAPIKey")
        KeychainHelper.shared.save(alphaVantageAPIKey, forKey: "alphaVantageAPIKey")
        KeychainHelper.shared.save(newsAPIKey, forKey: "newsAPIKey")
        KeychainHelper.shared.save(twitterAPIKey, forKey: "twitterAPIKey")
        KeychainHelper.shared.save(twitterAPISecret, forKey: "twitterAPISecret")
        KeychainHelper.shared.save(telegramBotToken, forKey: "telegramBotToken")
        KeychainHelper.shared.save(telegramChatID, forKey: "telegramChatID")
    }
} 