import SwiftUI

struct MultiExchangeTradingView: View {
    @StateObject private var apiService = MultiExchangeAPIService()
    @StateObject private var strategyManager = TradingStrategyManager()
    @State private var selectedTab = 0
    @State private var selectedExchange: SupportedExchange = .kucoin
    @State private var selectedTradingMode: TradingMode = .spot
    @State private var showingExchangeConfig = false
    @State private var showingOrderPlacement = false
    @State private var showingStrategyConfig = false
    @State private var tradingPairs: [TradingPair] = []
    @State private var portfolio = MultiExchangePortfolio()
    @State private var isLoading = false
    @State private var alertMessage = ""
    @State private var showingAlert = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Top Header
                headerView
                
                // Exchange Selector
                exchangeSelectorView
                
                // Main Content
                TabView(selection: $selectedTab) {
                    // Dashboard Tab
                    dashboardTab
                        .tabItem {
                            Image(systemName: "chart.bar.fill")
                            Text("Dashboard")
                        }
                        .tag(0)
                    
                    // Trading Tab
                    tradingTab
                        .tabItem {
                            Image(systemName: "arrow.left.arrow.right")
                            Text("Trading")
                        }
                        .tag(1)
                    
                    // Portfolio Tab
                    portfolioTab
                        .tabItem {
                            Image(systemName: "briefcase.fill")
                            Text("Portfolio")
                        }
                        .tag(2)
                    
                    // Strategies Tab
                    strategiesTab
                        .tabItem {
                            Image(systemName: "brain.head.profile")
                            Text("Strategies")
                        }
                        .tag(3)
                    
                    // Settings Tab
                    settingsTab
                        .tabItem {
                            Image(systemName: "gear")
                            Text("Settings")
                        }
                        .tag(4)
                }
                .accentColor(.cyan)
            }
            .background(Color.black)
            .navigationBarHidden(true)
        }
        .preferredColorScheme(.dark)
        .sheet(isPresented: $showingExchangeConfig) {
            ExchangeConfigurationView(apiService: apiService)
        }
        .sheet(isPresented: $showingOrderPlacement) {
            OrderPlacementView(
                apiService: apiService,
                exchange: selectedExchange,
                tradingMode: selectedTradingMode,
                tradingPairs: tradingPairs
            )
        }
        .sheet(isPresented: $showingStrategyConfig) {
            NavigationView {
                VStack {
                    Text("Strategy Configuration")
                        .font(.title)
                        .padding()
                    
                    Text("Strategy configuration interface coming soon...")
                        .foregroundColor(.gray)
                        .padding()
                    
                    Spacer()
                }
                .navigationTitle("Strategies")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Done") {
                            showingStrategyConfig = false
                        }
                    }
                }
            }
            .presentationDetents([.medium])
        }
        .alert("Alert", isPresented: $showingAlert) {
            Button("OK") { }
        } message: {
            Text(alertMessage)
        }
        .onAppear {
            loadInitialData()
        }
    }
    
    // MARK: - Header View
    private var headerView: some View {
        HStack {
            VStack(alignment: .leading) {
                Text("Agent Leon")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.cyan)
                
                Text("Multi-Exchange Trading Platform")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            
            Spacer()
            
            // Connection Status
            HStack(spacing: 8) {
                ForEach(SupportedExchange.allCases, id: \.self) { exchange in
                    VStack(spacing: 2) {
                        Image(systemName: exchange.iconName)
                            .foregroundColor(apiService.isConnected[exchange] == true ? .green : .red)
                            .font(.caption)
                        
                        Text(exchange.rawValue)
                            .font(.system(size: 8))
                            .foregroundColor(.gray)
                    }
                }
            }
            
            Button(action: { showingExchangeConfig = true }) {
                Image(systemName: "gear")
                    .foregroundColor(.cyan)
                    .font(.title2)
            }
        }
        .padding()
        .background(Color.black.opacity(0.9))
    }
    
    // MARK: - Exchange Selector
    private var exchangeSelectorView: some View {
        HStack {
            Text("Exchange:")
                .foregroundColor(.gray)
            
            Picker("Exchange", selection: $selectedExchange) {
                ForEach(SupportedExchange.allCases, id: \.self) { exchange in
                    Text(exchange.rawValue).tag(exchange)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            
            Spacer()
            
            Text("Mode:")
                .foregroundColor(.gray)
            
            Picker("Trading Mode", selection: $selectedTradingMode) {
                ForEach(TradingMode.allCases, id: \.self) { mode in
                    Text(mode.rawValue).tag(mode)
                }
            }
            .pickerStyle(MenuPickerStyle())
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .onChange(of: selectedExchange) { _ in
            loadTradingPairs()
        }
        .onChange(of: selectedTradingMode) { _ in
            loadTradingPairs()
        }
    }
    
    // MARK: - Dashboard Tab
    private var dashboardTab: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Portfolio Overview
                portfolioOverviewCard
                
                // Market Overview
                marketOverviewCard
                
                // Active Strategies
                activeStrategiesCard
                
                // Recent Orders
                recentOrdersCard
            }
            .padding()
        }
        .background(Color.black)
    }
    
    // MARK: - Trading Tab
    private var tradingTab: some View {
        VStack(spacing: 0) {
            // Trading Pairs List
            tradingPairsListView
            
            // Quick Trade Button
            Button(action: { showingOrderPlacement = true }) {
                HStack {
                    Image(systemName: "plus.circle.fill")
                    Text("Place New Order")
                }
                .foregroundColor(.white)
                .font(.headline)
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    LinearGradient(
                        colors: [.cyan, .blue],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(12)
            }
            .padding()
        }
        .background(Color.black)
    }
    
    // MARK: - Portfolio Tab
    private var portfolioTab: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Total Portfolio Value
                portfolioValueCard
                
                // Balances by Exchange
                balancesByExchangeView
                
                // Open Positions
                openPositionsView
                
                // P&L Chart (placeholder)
                pnlChartView
            }
            .padding()
        }
        .background(Color.black)
    }
    
    // MARK: - Strategies Tab
    private var strategiesTab: some View {
        VStack {
            // Strategy Stats
            strategyStatsView
            
            // Strategy List
            List {
                ForEach(AdvancedTradingStrategy.allCases, id: \.self) { strategy in
                    StrategyRowView(
                        strategy: strategy,
                        isEnabled: strategyManager.isStrategyEnabled(strategy),
                        onToggle: { strategyManager.toggleStrategy(strategy) },
                        onConfigure: { 
                            // Configure strategy
                        }
                    )
                }
            }
            .listStyle(PlainListStyle())
            .background(Color.black)
            
            // Add Strategy Button
            Button(action: { showingStrategyConfig = true }) {
                HStack {
                    Image(systemName: "plus")
                    Text("Configure New Strategy")
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.orange)
                .cornerRadius(12)
            }
            .padding()
        }
        .background(Color.black)
    }
    
    // MARK: - Settings Tab
    private var settingsTab: some View {
        List {
            Section("Exchange Configuration") {
                ForEach(apiService.exchangeConfigurations) { config in
                    ExchangeConfigRowView(
                        config: config,
                        isConnected: apiService.isConnected[config.exchange] == true,
                        onTest: { testConnection(for: config.exchange) },
                        onEdit: { showingExchangeConfig = true }
                    )
                }
            }
            
            Section("Risk Management") {
                HStack {
                    Text("Max Daily Loss")
                    Spacer()
                    Text("5%")
                        .foregroundColor(.red)
                }
                
                HStack {
                    Text("Position Size Limit")
                    Spacer()
                    Text("$10,000")
                        .foregroundColor(.orange)
                }
            }
            
            Section("Notifications") {
                Toggle("Order Notifications", isOn: .constant(true))
                Toggle("Price Alerts", isOn: .constant(true))
                Toggle("Strategy Alerts", isOn: .constant(false))
            }
        }
        .listStyle(InsetGroupedListStyle())
        .background(Color.black)
    }
    
    // MARK: - Card Views
    private var portfolioOverviewCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Portfolio Overview")
                .font(.headline)
                .foregroundColor(.cyan)
            
            HStack {
                VStack(alignment: .leading) {
                    Text("Total Value")
                        .font(.caption)
                        .foregroundColor(.gray)
                    Text("$\(portfolio.totalValueUSD, specifier: "%.2f")")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("24h P&L")
                        .font(.caption)
                        .foregroundColor(.gray)
                    Text("\(portfolio.profitLoss24h >= 0 ? "+" : "")$\(portfolio.profitLoss24h, specifier: "%.2f")")
                        .font(.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(portfolio.profitLoss24h >= 0 ? .green : .red)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var marketOverviewCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Market Overview")
                .font(.headline)
                .foregroundColor(.cyan)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                ForEach(tradingPairs.prefix(4), id: \.id) { pair in
                    VStack(alignment: .leading, spacing: 4) {
                        Text(pair.displaySymbol)
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                        
                        Text("$\(pair.price, specifier: "%.4f")")
                            .font(.footnote)
                            .foregroundColor(.gray)
                        
                        Text(pair.priceChangeFormatted)
                            .font(.caption)
                            .foregroundColor(pair.priceChange24h >= 0 ? .green : .red)
                    }
                    .padding(8)
                    .background(Color.gray.opacity(0.05))
                    .cornerRadius(8)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var activeStrategiesCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Active Strategies")
                    .font(.headline)
                    .foregroundColor(.cyan)
                
                Spacer()
                
                Text("\(portfolio.activeStrategies)")
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.green)
                    .cornerRadius(8)
            }
            
            if portfolio.activeStrategies == 0 {
                Text("No active strategies")
                    .font(.caption)
                    .foregroundColor(.gray)
                    .italic()
            } else {
                // Show active strategies
                VStack(alignment: .leading, spacing: 4) {
                    ForEach(AdvancedTradingStrategy.allCases.prefix(3), id: \.self) { strategy in
                        HStack {
                            Circle()
                                .fill(Color.green)
                                .frame(width: 6, height: 6)
                            
                            Text(strategy.displayName)
                                .font(.caption)
                                .foregroundColor(.white)
                            
                            Spacer()
                            
                            Text("Running")
                                .font(.caption)
                                .foregroundColor(.green)
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var recentOrdersCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recent Orders")
                .font(.headline)
                .foregroundColor(.cyan)
            
            // Placeholder for recent orders
            Text("No recent orders")
                .font(.caption)
                .foregroundColor(.gray)
                .italic()
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    // MARK: - Other Views
    private var tradingPairsListView: some View {
        List {
            ForEach(tradingPairs.prefix(20), id: \.id) { pair in
                TradingPairRowView(pair: pair) {
                    // Select pair for trading
                }
            }
        }
        .listStyle(PlainListStyle())
        .refreshable {
            loadTradingPairs()
        }
    }
    
    private var portfolioValueCard: some View {
        VStack(spacing: 16) {
            Text("Total Portfolio Value")
                .font(.headline)
                .foregroundColor(.cyan)
            
            Text("$\(portfolio.totalValueUSD, specifier: "%.2f")")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            HStack {
                Text("24h Change:")
                    .foregroundColor(.gray)
                Text("\(portfolio.profitLoss24h >= 0 ? "+" : "")$\(portfolio.profitLoss24h, specifier: "%.2f") (\(portfolio.profitLossPercentage24h, specifier: "%.2f")%)")
                    .foregroundColor(portfolio.profitLoss24h >= 0 ? .green : .red)
            }
            .font(.subheadline)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var balancesByExchangeView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Balances by Exchange")
                .font(.headline)
                .foregroundColor(.cyan)
            
            ForEach(SupportedExchange.allCases, id: \.self) { exchange in
                HStack {
                    Image(systemName: exchange.iconName)
                        .foregroundColor(.cyan)
                    
                    Text(exchange.rawValue)
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    if let balances = portfolio.balances[exchange], !balances.isEmpty {
                        Text("$\(balances.values.reduce(0, +), specifier: "%.2f")")
                            .foregroundColor(.white)
                    } else {
                        Text("$0.00")
                            .foregroundColor(.gray)
                    }
                }
                .padding(.vertical, 4)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var openPositionsView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Open Positions")
                .font(.headline)
                .foregroundColor(.cyan)
            
            if portfolio.openPositions.isEmpty {
                Text("No open positions")
                    .font(.caption)
                    .foregroundColor(.gray)
                    .italic()
            } else {
                ForEach(portfolio.openPositions, id: \.id) { position in
                    PositionRowView(position: position)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var pnlChartView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("P&L Chart")
                .font(.headline)
                .foregroundColor(.cyan)
            
            // Placeholder chart
            Rectangle()
                .fill(Color.gray.opacity(0.2))
                .frame(height: 150)
                .overlay(
                    Text("Chart Coming Soon")
                        .foregroundColor(.gray)
                )
                .cornerRadius(8)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var strategyStatsView: some View {
        HStack(spacing: 20) {
            VStack {
                Text("\(portfolio.activeStrategies)")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.green)
                Text("Active")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            
            VStack {
                Text("$0.00")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.cyan)
                Text("Profit Today")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            
            VStack {
                Text("0")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.orange)
                Text("Trades")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    // MARK: - Helper Functions
    private func loadInitialData() {
        Task {
            await loadTradingPairs()
            await loadPortfolioData()
        }
    }
    
    private func loadTradingPairs() {
        Task {
            isLoading = true
            let pairs = await apiService.fetchTradingPairs(for: selectedExchange, mode: selectedTradingMode)
            await MainActor.run {
                tradingPairs = pairs
                isLoading = false
            }
        }
    }
    
    private func loadPortfolioData() {
        Task {
            // Simulate portfolio data
            await MainActor.run {
                portfolio.totalValueUSD = 25847.32
                portfolio.profitLoss24h = 1284.67
                portfolio.profitLossPercentage24h = 5.23
                portfolio.activeStrategies = 3
            }
        }
    }
    
    private func testConnection(for exchange: SupportedExchange) {
        Task {
            let success = await apiService.testConnection(for: exchange)
            await MainActor.run {
                alertMessage = success ? "Connection successful!" : "Connection failed"
                showingAlert = true
            }
        }
    }
}

// MARK: - Supporting Views
struct TradingPairRowView: View {
    let pair: TradingPair
    let onSelect: () -> Void
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(pair.displaySymbol)
                    .font(.headline)
                    .foregroundColor(.white)
                
                Text(pair.exchange.rawValue)
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text("$\(pair.price, specifier: "%.4f")")
                    .font(.subheadline)
                    .foregroundColor(.white)
                
                Text(pair.priceChangeFormatted)
                    .font(.caption)
                    .foregroundColor(pair.priceChange24h >= 0 ? .green : .red)
            }
        }
        .contentShape(Rectangle())
        .onTapGesture(perform: onSelect)
    }
}

struct StrategyRowView: View {
    let strategy: AdvancedTradingStrategy
    let isEnabled: Bool
    let onToggle: () -> Void
    let onConfigure: () -> Void
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(strategy.displayName)
                    .font(.headline)
                    .foregroundColor(.white)
                
                Text(strategy.description)
                    .font(.caption)
                    .foregroundColor(.gray)
                
                HStack(spacing: 4) {
                    Circle()
                        .fill(Color(strategy.riskLevel.color))
                        .frame(width: 8, height: 8)
                    
                    Text(strategy.riskLevel.displayName)
                        .font(.caption)
                        .foregroundColor(Color(strategy.riskLevel.color))
                }
            }
            
            Spacer()
            
            VStack {
                Toggle("", isOn: .constant(isEnabled))
                    .scaleEffect(0.8)
                
                Button("Configure") {
                    onConfigure()
                }
                .font(.caption)
                .foregroundColor(.cyan)
            }
        }
    }
}

struct PositionRowView: View {
    let position: RealPosition
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(position.symbol)
                    .font(.headline)
                    .foregroundColor(.white)
                
                Text("\(position.side.displayName) • \(position.tradingMode.rawValue)")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text("$\(position.currentValue, specifier: "%.2f")")
                    .font(.subheadline)
                    .foregroundColor(.white)
                
                Text("\(position.unrealizedPnl >= 0 ? "+" : "")$\(position.unrealizedPnl, specifier: "%.2f")")
                    .font(.caption)
                    .foregroundColor(position.unrealizedPnl >= 0 ? .green : .red)
            }
        }
    }
}

struct ExchangeConfigRowView: View {
    let config: ExchangeConfiguration
    let isConnected: Bool
    let onTest: () -> Void
    let onEdit: () -> Void
    
    var body: some View {
        HStack {
            Image(systemName: config.exchange.iconName)
                .foregroundColor(.cyan)
            
            VStack(alignment: .leading) {
                Text(config.exchange.rawValue)
                    .foregroundColor(.white)
                
                Text(config.isEnabled ? "Enabled" : "Disabled")
                    .font(.caption)
                    .foregroundColor(config.isEnabled ? .green : .gray)
            }
            
            Spacer()
            
            Circle()
                .fill(isConnected ? Color.green : Color.red)
                .frame(width: 10, height: 10)
            
            Button("Test") {
                onTest()
            }
            .font(.caption)
            .foregroundColor(.cyan)
            
            Button("Edit") {
                onEdit()
            }
            .font(.caption)
            .foregroundColor(.orange)
        }
    }
} 