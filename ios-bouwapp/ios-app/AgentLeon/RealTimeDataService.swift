import Foundation
import Combine
import Network

// MARK: - Real-Time Data Service (CCXT-Inspired)
class RealTimeDataService: ObservableObject {
    @Published var isConnected: Bool = false
    @Published var latestTickers: [String: RealtimeTicker] = [:]
    @Published var latestOrderBooks: [String: RealtimeOrderBook] = [:]
    @Published var latestTrades: [String: [RealtimeTrade]] = [:]
    @Published var latestCandles: [String: [RealtimeCandle]] = [:]
    @Published var connectionLatency: TimeInterval = 0.0
    @Published var dataQuality: DataQualityMetrics = DataQualityMetrics()
    
    private var websocketTasks: [String: URLSessionWebSocketTask] = [:]
    private var heartbeatTimers: [String: Timer] = [:]
    private var reconnectionAttempts: [String: Int] = [:]
    private let maxReconnectionAttempts = 5
    private let urlSession = URLSession.shared
    private var cancellables = Set<AnyCancellable>()
    
    // Subscription Management
    private var activeSubscriptions: Set<DataSubscription> = []
    private let subscriptionQueue = DispatchQueue(label: "websocket.subscription", qos: .userInteractive)
    
    // Performance Monitoring
    private var lastPingTime: Date?
    private var messageCount: Int = 0
    private var errorCount: Int = 0
    
    // MARK: - Initialization
    init() {
        setupNetworkMonitoring()
        setupPerformanceMonitoring()
    }
    
    // MARK: - Public API (CCXT-Style)
    func watchTicker(symbol: String, exchange: SupportedExchange) async throws {
        let subscription = DataSubscription(
            type: .ticker,
            symbol: symbol,
            exchange: exchange,
            timeframe: nil
        )
        
        await addSubscription(subscription)
    }
    
    func watchOrderBook(symbol: String, exchange: SupportedExchange, limit: Int = 20) async throws {
        let subscription = DataSubscription(
            type: .orderBook,
            symbol: symbol,
            exchange: exchange,
            timeframe: nil,
            limit: limit
        )
        
        await addSubscription(subscription)
    }
    
    func watchTrades(symbol: String, exchange: SupportedExchange) async throws {
        let subscription = DataSubscription(
            type: .trades,
            symbol: symbol,
            exchange: exchange,
            timeframe: nil
        )
        
        await addSubscription(subscription)
    }
    
    func watchOHLCV(symbol: String, exchange: SupportedExchange, timeframe: String = "1m") async throws {
        let subscription = DataSubscription(
            type: .ohlcv,
            symbol: symbol,
            exchange: exchange,
            timeframe: timeframe
        )
        
        await addSubscription(subscription)
    }
    
    // MARK: - Subscription Management
    @MainActor
    private func addSubscription(_ subscription: DataSubscription) async {
        activeSubscriptions.insert(subscription)
        await connectToExchange(subscription.exchange)
        await sendSubscriptionMessage(subscription)
    }
    
    private func connectToExchange(_ exchange: SupportedExchange) async {
        let websocketURL = getWebSocketURL(for: exchange)
        
        guard websocketTasks[exchange.rawValue] == nil else { return }
        
        let task = urlSession.webSocketTask(with: websocketURL)
        websocketTasks[exchange.rawValue] = task
        
        task.resume()
        
        // Start listening for messages
        await startListening(for: exchange)
        
        // Setup heartbeat
        setupHeartbeat(for: exchange)
        
        await MainActor.run {
            self.isConnected = true
        }
    }
    
    // MARK: - WebSocket Message Handling
    private func startListening(for exchange: SupportedExchange) async {
        guard let task = websocketTasks[exchange.rawValue] else { return }
        
        do {
            let message = try await task.receive()
            await handleMessage(message, from: exchange)
            
            // Continue listening recursively
            Task {
                await self.startListening(for: exchange)
            }
        } catch {
            print("WebSocket error for \(exchange): \(error)")
            await handleConnectionError(exchange: exchange, error: error)
        }
    }
    
    private func handleMessage(_ message: URLSessionWebSocketTask.Message, from exchange: SupportedExchange) async {
        messageCount += 1
        
        switch message {
        case .string(let text):
            await processTextMessage(text, from: exchange)
        case .data(let data):
            await processDataMessage(data, from: exchange)
        @unknown default:
            break
        }
        
        await updateDataQuality()
    }
    
    private func processTextMessage(_ text: String, from exchange: SupportedExchange) async {
        guard let data = text.data(using: .utf8) else { return }
        await processDataMessage(data, from: exchange)
    }
    
    private func processDataMessage(_ data: Data, from exchange: SupportedExchange) async {
        do {
            let json = try JSONSerialization.jsonObject(with: data) as? [String: Any]
            await routeMessage(json, from: exchange)
        } catch {
            errorCount += 1
            print("JSON parsing error: \(error)")
        }
    }
    
    private func routeMessage(_ json: [String: Any]?, from exchange: SupportedExchange) async {
        guard let json = json else { return }
        
        // Route based on message type
        if let channel = json["channel"] as? String {
            switch channel {
            case "ticker":
                await handleTickerUpdate(json, from: exchange)
            case "depth", "orderbook":
                await handleOrderBookUpdate(json, from: exchange)
            case "trade":
                await handleTradeUpdate(json, from: exchange)
            case "kline", "candle":
                await handleCandleUpdate(json, from: exchange)
            default:
                break
            }
        }
        
        // Handle pong responses
        if json["pong"] != nil {
            await handlePongResponse()
        }
    }
    
    // MARK: - Data Processing
    private func handleTickerUpdate(_ json: [String: Any], from exchange: SupportedExchange) async {
        guard let symbol = json["symbol"] as? String,
              let price = json["price"] as? Double ?? (json["last"] as? String).flatMap(Double.init) else { return }
        
        let ticker = RealtimeTicker(
            symbol: symbol,
            exchange: exchange,
            price: price,
            bid: json["bid"] as? Double,
            ask: json["ask"] as? Double,
            volume: json["volume"] as? Double,
            high: json["high"] as? Double,
            low: json["low"] as? Double,
            change: json["change"] as? Double,
            changePercent: json["changePercent"] as? Double,
            timestamp: Date()
        )
        
        await MainActor.run {
            self.latestTickers["\(exchange.rawValue):\(symbol)"] = ticker
        }
    }
    
    private func handleOrderBookUpdate(_ json: [String: Any], from exchange: SupportedExchange) async {
        guard let symbol = json["symbol"] as? String,
              let bidsData = json["bids"] as? [[Any]],
              let asksData = json["asks"] as? [[Any]] else { return }
        
        let bids = bidsData.compactMap { bidArray -> OrderBookEntry? in
            guard bidArray.count >= 2,
                  let price = bidArray[0] as? Double ?? (bidArray[0] as? String).flatMap(Double.init),
                  let amount = bidArray[1] as? Double ?? (bidArray[1] as? String).flatMap(Double.init) else { return nil }
            return OrderBookEntry(price: price, amount: amount)
        }
        
        let asks = asksData.compactMap { askArray -> OrderBookEntry? in
            guard askArray.count >= 2,
                  let price = askArray[0] as? Double ?? (askArray[0] as? String).flatMap(Double.init),
                  let amount = askArray[1] as? Double ?? (askArray[1] as? String).flatMap(Double.init) else { return nil }
            return OrderBookEntry(price: price, amount: amount)
        }
        
        let orderBook = RealtimeOrderBook(
            symbol: symbol,
            exchange: exchange,
            bids: bids.sorted { $0.price > $1.price },
            asks: asks.sorted { $0.price < $1.price },
            timestamp: Date()
        )
        
        await MainActor.run {
            self.latestOrderBooks["\(exchange.rawValue):\(symbol)"] = orderBook
        }
    }
    
    private func handleTradeUpdate(_ json: [String: Any], from exchange: SupportedExchange) async {
        guard let symbol = json["symbol"] as? String,
              let price = json["price"] as? Double ?? (json["p"] as? String).flatMap(Double.init),
              let amount = json["amount"] as? Double ?? (json["q"] as? String).flatMap(Double.init) else { return }
        
        let trade = RealtimeTrade(
            symbol: symbol,
            exchange: exchange,
            price: price,
            amount: amount,
            side: json["side"] as? String ?? "unknown",
            timestamp: Date()
        )
        
        await MainActor.run {
            let key = "\(exchange.rawValue):\(symbol)"
            if self.latestTrades[key] == nil {
                self.latestTrades[key] = []
            }
            self.latestTrades[key]?.append(trade)
            
            // Keep only last 100 trades
            if self.latestTrades[key]?.count ?? 0 > 100 {
                self.latestTrades[key]?.removeFirst()
            }
        }
    }
    
    private func handleCandleUpdate(_ json: [String: Any], from exchange: SupportedExchange) async {
        guard let symbol = json["symbol"] as? String,
              let open = json["open"] as? Double ?? (json["o"] as? String).flatMap(Double.init),
              let high = json["high"] as? Double ?? (json["h"] as? String).flatMap(Double.init),
              let low = json["low"] as? Double ?? (json["l"] as? String).flatMap(Double.init),
              let close = json["close"] as? Double ?? (json["c"] as? String).flatMap(Double.init),
              let volume = json["volume"] as? Double ?? (json["v"] as? String).flatMap(Double.init) else { return }
        
        let candle = RealtimeCandle(
            symbol: symbol,
            exchange: exchange,
            open: open,
            high: high,
            low: low,
            close: close,
            volume: volume,
            timestamp: Date()
        )
        
        await MainActor.run {
            let key = "\(exchange.rawValue):\(symbol)"
            if self.latestCandles[key] == nil {
                self.latestCandles[key] = []
            }
            self.latestCandles[key]?.append(candle)
            
            // Keep only last 1000 candles
            if self.latestCandles[key]?.count ?? 0 > 1000 {
                self.latestCandles[key]?.removeFirst()
            }
        }
    }
    
    // MARK: - Connection Management
    private func sendSubscriptionMessage(_ subscription: DataSubscription) async {
        guard let task = websocketTasks[subscription.exchange.rawValue] else { return }
        
        let subscriptionMessage = createSubscriptionMessage(subscription)
        
        do {
            try await task.send(.string(subscriptionMessage))
        } catch {
            print("Failed to send subscription: \(error)")
        }
    }
    
    private func createSubscriptionMessage(_ subscription: DataSubscription) -> String {
        switch subscription.exchange {
        case .binance:
            return createBinanceSubscription(subscription)
        case .kucoin:
            return createKuCoinSubscription(subscription)
        case .bybit:
            return createBybitSubscription(subscription)
        case .mexc:
            return createMEXCSubscription(subscription)
        }
    }
    
    private func createBinanceSubscription(_ subscription: DataSubscription) -> String {
        let symbol = subscription.symbol.lowercased()
        let stream: String
        
        switch subscription.type {
        case .ticker:
            stream = "\(symbol)@ticker"
        case .orderBook:
            stream = "\(symbol)@depth\(subscription.limit ?? 20)"
        case .trades:
            stream = "\(symbol)@trade"
        case .ohlcv:
            stream = "\(symbol)@kline_\(subscription.timeframe ?? "1m")"
        }
        
        return """
        {
            "method": "SUBSCRIBE",
            "params": ["\(stream)"],
            "id": \(Int.random(in: 1...1000))
        }
        """
    }
    
    // MARK: - Performance & Quality Monitoring
    private func setupPerformanceMonitoring() {
        Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { _ in
            Task {
                await self.updateDataQuality()
            }
        }
    }
    
    @MainActor
    private func updateDataQuality() async {
        let now = Date()
        let messagesPerSecond = Double(messageCount) / 5.0
        let errorRate = errorCount > 0 ? Double(errorCount) / Double(messageCount) : 0.0
        
        dataQuality = DataQualityMetrics(
            messagesPerSecond: messagesPerSecond,
            errorRate: errorRate,
            latency: connectionLatency,
            lastUpdate: now
        )
        
        // Reset counters
        messageCount = 0
        errorCount = 0
    }
    
    private func setupNetworkMonitoring() {
        let monitor = NWPathMonitor()
        monitor.pathUpdateHandler = { path in
            DispatchQueue.main.async {
                if path.status != .satisfied {
                    self.handleNetworkDisconnection()
                }
            }
        }
        let queue = DispatchQueue(label: "NetworkMonitor")
        monitor.start(queue: queue)
    }
    
    // MARK: - Helper Methods
    private func getWebSocketURL(for exchange: SupportedExchange) -> URL {
        switch exchange {
        case .binance:
            return URL(string: "wss://stream.binance.com:9443/ws")!
        case .kucoin:
            return URL(string: "wss://ws-api.kucoin.com/endpoint")!
        case .bybit:
            return URL(string: "wss://stream.bybit.com/v5/public/spot")!
        case .mexc:
            return URL(string: "wss://wbs.mexc.com/ws")!
        }
    }
    
    private func setupHeartbeat(for exchange: SupportedExchange) {
        let timer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { _ in
            Task {
                await self.sendHeartbeat(to: exchange)
            }
        }
        heartbeatTimers[exchange.rawValue] = timer
    }
    
    private func sendHeartbeat(to exchange: SupportedExchange) async {
        guard let task = websocketTasks[exchange.rawValue] else { return }
        
        lastPingTime = Date()
        let pingMessage = "{'ping': \(Int(Date().timeIntervalSince1970))}"
        
        do {
            try await task.send(.string(pingMessage))
        } catch {
            print("Failed to send heartbeat: \(error)")
        }
    }
    
    private func handlePongResponse() async {
        if let pingTime = lastPingTime {
            await MainActor.run {
                self.connectionLatency = Date().timeIntervalSince(pingTime)
            }
        }
    }
    
    deinit {
        // Cleanup
        for task in websocketTasks.values {
            task.cancel()
        }
        for timer in heartbeatTimers.values {
            timer.invalidate()
        }
    }
}

// MARK: - Supporting Types
struct DataSubscription: Hashable {
    let type: DataType
    let symbol: String
    let exchange: SupportedExchange
    let timeframe: String?
    let limit: Int?
    
    init(type: DataType, symbol: String, exchange: SupportedExchange, timeframe: String? = nil, limit: Int? = nil) {
        self.type = type
        self.symbol = symbol
        self.exchange = exchange
        self.timeframe = timeframe
        self.limit = limit
    }
}

enum DataType {
    case ticker
    case orderBook
    case trades
    case ohlcv
}

struct RealtimeTicker {
    let symbol: String
    let exchange: SupportedExchange
    let price: Double
    let bid: Double?
    let ask: Double?
    let volume: Double?
    let high: Double?
    let low: Double?
    let change: Double?
    let changePercent: Double?
    let timestamp: Date
}

struct RealtimeOrderBook {
    let symbol: String
    let exchange: SupportedExchange
    let bids: [OrderBookEntry]
    let asks: [OrderBookEntry]
    let timestamp: Date
}

struct OrderBookEntry {
    let price: Double
    let amount: Double
}

struct RealtimeTrade {
    let symbol: String
    let exchange: SupportedExchange
    let price: Double
    let amount: Double
    let side: String
    let timestamp: Date
}

struct RealtimeCandle {
    let symbol: String
    let exchange: SupportedExchange
    let open: Double
    let high: Double
    let low: Double
    let close: Double
    let volume: Double
    let timestamp: Date
}

struct DataQualityMetrics {
    let messagesPerSecond: Double
    let errorRate: Double
    let latency: TimeInterval
    let lastUpdate: Date
    
    init(messagesPerSecond: Double = 0, errorRate: Double = 0, latency: TimeInterval = 0, lastUpdate: Date = Date()) {
        self.messagesPerSecond = messagesPerSecond
        self.errorRate = errorRate
        self.latency = latency
        self.lastUpdate = lastUpdate
    }
}

// MARK: - Extension Methods
extension RealTimeDataService {
    private func createKuCoinSubscription(_ subscription: DataSubscription) -> String {
        // KuCoin specific subscription format
        return "{}"
    }
    
    private func createBybitSubscription(_ subscription: DataSubscription) -> String {
        // Bybit specific subscription format
        return "{}"
    }
    
    private func createMEXCSubscription(_ subscription: DataSubscription) -> String {
        // MEXC specific subscription format
        return "{}"
    }
    
    private func handleConnectionError(exchange: SupportedExchange, error: Error) async {
        reconnectionAttempts[exchange.rawValue, default: 0] += 1
        
        if reconnectionAttempts[exchange.rawValue, default: 0] < maxReconnectionAttempts {
            // Exponential backoff
            let delay = pow(2.0, Double(reconnectionAttempts[exchange.rawValue, default: 0]))
            try? await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
            
            await reconnectToExchange(exchange)
        } else {
            await MainActor.run {
                self.isConnected = false
            }
        }
    }
    
    private func reconnectToExchange(_ exchange: SupportedExchange) async {
        // Clean up existing connection
        websocketTasks[exchange.rawValue]?.cancel()
        websocketTasks.removeValue(forKey: exchange.rawValue)
        heartbeatTimers[exchange.rawValue]?.invalidate()
        heartbeatTimers.removeValue(forKey: exchange.rawValue)
        
        // Reconnect
        await connectToExchange(exchange)
    }
    
    private func handleNetworkDisconnection() {
        isConnected = false
        
        // Attempt to reconnect all exchanges
        Task {
            for exchange in SupportedExchange.allCases {
                await self.reconnectToExchange(exchange)
            }
        }
    }
} 