import Foundation
import Combine
import Speech
import AVFoundation

// MARK: - Voice Command Agent
class VoiceCommandAgent: Agent {
    @Published var isListening: Bool = false
    @Published var lastRecognizedText: String = ""
    @Published var voiceCommands: [VoiceCommand] = []
    @Published var isVoiceEnabled: Bool = false
    @Published var recognitionConfidence: Float = 0.0
    @Published var supportedLanguages: [String] = ["nl-NL", "en-US", "de-DE", "fr-FR"]
    @Published var currentLanguage: String = "nl-NL"
    
    private let speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private let audioEngine = AVAudioEngine()
    private let synthesizer = AVSpeechSynthesizer()
    
    // Command Processing
    private var agentManager: AgentManager?
    private var commandProcessor = VoiceCommandProcessor()
    private var contextManager = VoiceContextManager()
    
    // Voice Response Configuration
    @Published var voiceResponseEnabled: Bool = true
    @Published var voiceSpeed: Float = 0.5
    @Published var voicePitch: Float = 1.0
    
    override init(name: String = "Voice Commander", status: AgentStatus = .idle, performance: Double = 100.0, capabilities: [String] = [], version: String = "1.0.0") {
        self.speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: currentLanguage))
        
        super.init(
            name: name,
            status: status,
            performance: performance,
            capabilities: ["Speech Recognition", "Voice Commands", "Natural Language Processing", "Voice Synthesis", "Multi-Language Support"],
            version: version
        )
        
        setupVoiceRecognition()
        loadVoiceCommands()
        setupAudioSession()
    }
    
    // MARK: - Voice Recognition Setup
    private func setupVoiceRecognition() {
        SFSpeechRecognizer.requestAuthorization { authStatus in
            DispatchQueue.main.async {
                switch authStatus {
                case .authorized:
                    self.isVoiceEnabled = true
                    print("✅ Speech recognition authorized")
                case .denied, .restricted, .notDetermined:
                    self.isVoiceEnabled = false
                    print("❌ Speech recognition not authorized")
                @unknown default:
                    self.isVoiceEnabled = false
                }
            }
        }
    }
    
    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.record, mode: .measurement, options: .duckOthers)
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
        } catch {
            print("Audio session setup failed: \(error)")
        }
    }
    
    // MARK: - Voice Commands
    private func loadVoiceCommands() {
        voiceCommands = [
            // Trading Commands
            VoiceCommand(
                phrase: ["koop bitcoin", "buy bitcoin", "bitcoin kopen"],
                action: .buyAsset("BTCUSDT"),
                description: "Koopt Bitcoin",
                category: .trading
            ),
            VoiceCommand(
                phrase: ["verkoop bitcoin", "sell bitcoin", "bitcoin verkopen"],
                action: .sellAsset("BTCUSDT"),
                description: "Verkoopt Bitcoin",
                category: .trading
            ),
            VoiceCommand(
                phrase: ["stop alle orders", "cancel all orders", "annuleer alles"],
                action: .cancelAllOrders,
                description: "Annuleert alle openstaande orders",
                category: .trading
            ),
            
            // Agent Commands
            VoiceCommand(
                phrase: ["herstart alle agents", "restart all agents", "reset agents"],
                action: .restartAgents,
                description: "Herstart alle agents",
                category: .agentControl
            ),
            VoiceCommand(
                phrase: ["agent status", "status rapport", "hoe gaat het"],
                action: .getStatus,
                description: "Geeft status overzicht",
                category: .agentControl
            ),
            VoiceCommand(
                phrase: ["activeer automatisch handelen", "enable auto trading", "zet trading aan"],
                action: .enableAutoTrading,
                description: "Activeert automatische trading",
                category: .trading
            ),
            
            // Analysis Commands
            VoiceCommand(
                phrase: ["analyseer bitcoin", "analyze bitcoin", "bitcoin analyse"],
                action: .analyzeAsset("BTCUSDT"),
                description: "Analyseert Bitcoin markt",
                category: .analysis
            ),
            VoiceCommand(
                phrase: ["voorspel prijs", "predict price", "wat wordt de prijs"],
                action: .predictPrice,
                description: "Voorspelt toekomstige prijzen",
                category: .analysis
            ),
            VoiceCommand(
                phrase: ["detecteer patronen", "find patterns", "zoek patronen"],
                action: .detectPatterns,
                description: "Zoekt naar chart patronen",
                category: .analysis
            ),
            
            // Portfolio Commands
            VoiceCommand(
                phrase: ["mijn portfolio", "show portfolio", "toon portefeuille"],
                action: .showPortfolio,
                description: "Toont portfolio overzicht",
                category: .portfolio
            ),
            VoiceCommand(
                phrase: ["winst verlies", "profit loss", "hoe sta ik ervoor"],
                action: .showPnL,
                description: "Toont winst/verlies overzicht",
                category: .portfolio
            ),
            
            // Risk Management
            VoiceCommand(
                phrase: ["risico analyse", "risk analysis", "check risicos"],
                action: .analyzeRisk,
                description: "Analyseert portfolio risicos",
                category: .riskManagement
            ),
            VoiceCommand(
                phrase: ["noodstop", "emergency stop", "stop alles"],
                action: .emergencyStop,
                description: "Noodstop alle trading activiteiten",
                category: .riskManagement
            ),
            
            // Settings Commands
            VoiceCommand(
                phrase: ["verander taal", "change language", "andere taal"],
                action: .changeLanguage,
                description: "Wijzigt de taal instellingen",
                category: .settings
            ),
            VoiceCommand(
                phrase: ["help", "hulp", "wat kan je"],
                action: .showHelp,
                description: "Toont beschikbare commando's",
                category: .settings
            )
        ]
    }
    
    // MARK: - Voice Recognition
    func startListening() {
        guard isVoiceEnabled else {
            speakResponse("Spraakherkenning is niet beschikbaar")
            return
        }
        
        guard !audioEngine.isRunning else { return }
        
        updateStatus(to: .active)
        isListening = true
        
        do {
            try startSpeechRecognition()
        } catch {
            print("Speech recognition failed: \(error)")
            stopListening()
        }
    }
    
    func stopListening() {
        audioEngine.stop()
        recognitionRequest?.endAudio()
        recognitionTask?.cancel()
        
        recognitionRequest = nil
        recognitionTask = nil
        
        isListening = false
        updateStatus(to: .idle)
    }
    
    private func startSpeechRecognition() throws {
        recognitionTask?.cancel()
        recognitionTask = nil
        
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            throw VoiceError.recognitionRequestFailed
        }
        
        recognitionRequest.shouldReportPartialResults = true
        
        let inputNode = audioEngine.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)
        
        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
            recognitionRequest.append(buffer)
        }
        
        audioEngine.prepare()
        try audioEngine.start()
        
        recognitionTask = speechRecognizer?.recognitionTask(with: recognitionRequest) { [weak self] result, error in
            self?.handleRecognitionResult(result: result, error: error)
        }
    }
    
    private func handleRecognitionResult(result: SFSpeechRecognitionResult?, error: Error?) {
        if let error = error {
            print("Recognition error: \(error)")
            stopListening()
            return
        }
        
        guard let result = result else { return }
        
        lastRecognizedText = result.bestTranscription.formattedString
        recognitionConfidence = result.bestTranscription.segments.last?.confidence ?? 0.0
        
        // Process command if speech is final and confidence is high enough
        if result.isFinal && recognitionConfidence > 0.7 {
            processVoiceCommand(lastRecognizedText)
            stopListening()
        }
    }
    
    // MARK: - Command Processing
    private func processVoiceCommand(_ text: String) {
        updateStatus(to: .thinking)
        
        // Find matching command
        let matchedCommand = findMatchingCommand(text: text.lowercased())
        
        if let command = matchedCommand {
            executeVoiceCommand(command)
        } else {
            // Try natural language processing
            processNaturalLanguageCommand(text)
        }
        
        updateStatus(to: .idle)
    }
    
    private func findMatchingCommand(text: String) -> VoiceCommand? {
        for command in voiceCommands {
            for phrase in command.phrase {
                if text.contains(phrase.lowercased()) {
                    return command
                }
            }
        }
        return nil
    }
    
    private func executeVoiceCommand(_ command: VoiceCommand) {
        print("🎤 Executing voice command: \(command.description)")
        
        switch command.action {
        case .buyAsset(let symbol):
            executeBuyCommand(symbol: symbol)
        case .sellAsset(let symbol):
            executeSellCommand(symbol: symbol)
        case .cancelAllOrders:
            executeCancelAllOrders()
        case .restartAgents:
            executeRestartAgents()
        case .getStatus:
            executeGetStatus()
        case .enableAutoTrading:
            executeEnableAutoTrading()
        case .analyzeAsset(let symbol):
            executeAnalyzeAsset(symbol: symbol)
        case .predictPrice:
            executePredictPrice()
        case .detectPatterns:
            executeDetectPatterns()
        case .showPortfolio:
            executeShowPortfolio()
        case .showPnL:
            executeShowPnL()
        case .analyzeRisk:
            executeAnalyzeRisk()
        case .emergencyStop:
            executeEmergencyStop()
        case .changeLanguage:
            executeChangeLanguage()
        case .showHelp:
            executeShowHelp()
        }
    }
    
    // MARK: - Command Implementations
    private func executeBuyCommand(symbol: String) {
        speakResponse("Ik plaats een kooporder voor \(symbol)")
        agentManager?.cryptoAgent?.processCommand("buy \(symbol)")
    }
    
    private func executeSellCommand(symbol: String) {
        speakResponse("Ik plaats een verkooporder voor \(symbol)")
        agentManager?.cryptoAgent?.processCommand("sell \(symbol)")
    }
    
    private func executeCancelAllOrders() {
        speakResponse("Ik annuleer alle openstaande orders")
        agentManager?.executeGlobalCommand("cancel all orders")
    }
    
    private func executeRestartAgents() {
        speakResponse("Ik herstart alle agents")
        agentManager?.restartAllAgents()
    }
    
    private func executeGetStatus() {
        guard let agentManager = agentManager else {
            speakResponse("Agent manager is niet beschikbaar")
            return
        }
        
        let activeAgents = agentManager.agents.filter { $0.status == .active }.count
        let performanceAvg = Int(agentManager.globalPerformance)
        
        speakResponse("Systeem status: \(activeAgents) agents actief. Gemiddelde performance: \(performanceAvg) procent")
    }
    
    private func executeEnableAutoTrading() {
        speakResponse("Automatische trading wordt geactiveerd")
        agentManager?.cryptoAgent?.isAutoTradingEnabled = true
    }
    
    private func executeAnalyzeAsset(symbol: String) {
        speakResponse("Ik analyseer \(symbol) voor je")
        agentManager?.executeGlobalCommand("analyze \(symbol)")
    }
    
    private func executePredictPrice() {
        speakResponse("Ik maak een prijsvoorspelling")
        // Trigger ML agent prediction
    }
    
    private func executeDetectPatterns() {
        speakResponse("Ik zoek naar chart patronen")
        // Trigger pattern detection
    }
    
    private func executeShowPortfolio() {
        guard let cryptoAgent = agentManager?.cryptoAgent else {
            speakResponse("Portfolio informatie is niet beschikbaar")
            return
        }
        
        let positionCount = cryptoAgent.portfolio.count
        let totalValue = cryptoAgent.balance + cryptoAgent.totalPnL
        
        speakResponse("Je portfolio heeft \(positionCount) posities met een totale waarde van \(Int(totalValue)) dollar")
    }
    
    private func executeShowPnL() {
        guard let cryptoAgent = agentManager?.cryptoAgent else {
            speakResponse("Winst verlies informatie is niet beschikbaar")
            return
        }
        
        let pnl = cryptoAgent.totalPnL
        let pnlText = pnl > 0 ? "winst van \(Int(pnl)) dollar" : "verlies van \(Int(abs(pnl))) dollar"
        
        speakResponse("Je hebt momenteel een \(pnlText)")
    }
    
    private func executeAnalyzeRisk() {
        speakResponse("Ik analyseer de portfolio risicos")
        agentManager?.riskManagerAgent?.analyzePortfolioRisk()
    }
    
    private func executeEmergencyStop() {
        speakResponse("Noodstop geactiveerd. Alle trading wordt gestopt")
        agentManager?.executeGlobalCommand("emergency stop")
    }
    
    private func executeChangeLanguage() {
        let nextLanguageIndex = (supportedLanguages.firstIndex(of: currentLanguage) ?? 0 + 1) % supportedLanguages.count
        currentLanguage = supportedLanguages[nextLanguageIndex]
        speakResponse("Taal gewijzigd naar \(getLanguageName(currentLanguage))")
    }
    
    private func executeShowHelp() {
        let commandsText = voiceCommands.prefix(5).map { $0.description }.joined(separator: ", ")
        speakResponse("Beschikbare commando's zijn: \(commandsText), en nog veel meer")
    }
    
    // MARK: - Natural Language Processing
    private func processNaturalLanguageCommand(_ text: String) {
        let nlpResult = commandProcessor.processNaturalLanguage(text)
        
        if let intent = nlpResult.intent {
            executeNLPIntent(intent, parameters: nlpResult.parameters)
        } else {
            speakResponse("Ik begrijp het commando niet. Zeg 'help' voor beschikbare commando's")
        }
    }
    
    private func executeNLPIntent(_ intent: VoiceIntent, parameters: [String: String]) {
        switch intent {
        case .trading:
            handleTradingIntent(parameters: parameters)
        case .analysis:
            handleAnalysisIntent(parameters: parameters)
        case .portfolio:
            handlePortfolioIntent(parameters: parameters)
        case .settings:
            handleSettingsIntent(parameters: parameters)
        }
    }
    
    private func handleTradingIntent(parameters: [String: String]) {
        if let action = parameters["action"], let asset = parameters["asset"] {
            if action == "buy" {
                executeBuyCommand(symbol: asset)
            } else if action == "sell" {
                executeSellCommand(symbol: asset)
            }
        }
    }
    
    private func handleAnalysisIntent(parameters: [String: String]) {
        if let asset = parameters["asset"] {
            executeAnalyzeAsset(symbol: asset)
        } else {
            executePredictPrice()
        }
    }
    
    private func handlePortfolioIntent(parameters: [String: String]) {
        executeShowPortfolio()
    }
    
    private func handleSettingsIntent(parameters: [String: String]) {
        if parameters["action"] == "help" {
            executeShowHelp()
        }
    }
    
    // MARK: - Voice Response
    private func speakResponse(_ text: String) {
        guard voiceResponseEnabled else { return }
        
        let utterance = AVSpeechUtterance(string: text)
        utterance.voice = AVSpeechSynthesisVoice(language: currentLanguage)
        utterance.rate = voiceSpeed
        utterance.pitchMultiplier = voicePitch
        
        synthesizer.speak(utterance)
    }
    
    // MARK: - Utility Methods
    private func getLanguageName(_ code: String) -> String {
        switch code {
        case "nl-NL": return "Nederlands"
        case "en-US": return "English"
        case "de-DE": return "Deutsch"
        case "fr-FR": return "Français"
        default: return code
        }
    }
    
    func setAgentManager(_ manager: AgentManager) {
        self.agentManager = manager
    }
    
    deinit {
        stopListening()
    }
}

// MARK: - Supporting Types
struct VoiceCommand: Identifiable {
    let id = UUID()
    let phrase: [String]
    let action: VoiceAction
    let description: String
    let category: VoiceCategory
}

enum VoiceAction {
    case buyAsset(String)
    case sellAsset(String)
    case cancelAllOrders
    case restartAgents
    case getStatus
    case enableAutoTrading
    case analyzeAsset(String)
    case predictPrice
    case detectPatterns
    case showPortfolio
    case showPnL
    case analyzeRisk
    case emergencyStop
    case changeLanguage
    case showHelp
}

enum VoiceCategory: String, CaseIterable {
    case trading = "Trading"
    case agentControl = "Agent Control"
    case analysis = "Analysis"
    case portfolio = "Portfolio"
    case riskManagement = "Risk Management"
    case settings = "Settings"
}

enum VoiceIntent {
    case trading
    case analysis
    case portfolio
    case settings
}

struct NLPResult {
    let intent: VoiceIntent?
    let parameters: [String: String]
    let confidence: Float
}

enum VoiceError: Error {
    case recognitionRequestFailed
    case audioEngineFailed
    case speechRecognizerUnavailable
}

// MARK: - Command Processor
class VoiceCommandProcessor {
    func processNaturalLanguage(_ text: String) -> NLPResult {
        let lowercaseText = text.lowercased()
        
        // Simple intent recognition
        var intent: VoiceIntent?
        var parameters: [String: String] = [:]
        var confidence: Float = 0.5
        
        // Trading intent
        if lowercaseText.contains("koop") || lowercaseText.contains("buy") ||
           lowercaseText.contains("verkoop") || lowercaseText.contains("sell") {
            intent = .trading
            confidence = 0.8
            
            if lowercaseText.contains("koop") || lowercaseText.contains("buy") {
                parameters["action"] = "buy"
            } else {
                parameters["action"] = "sell"
            }
            
            // Extract asset
            if lowercaseText.contains("bitcoin") || lowercaseText.contains("btc") {
                parameters["asset"] = "BTCUSDT"
            } else if lowercaseText.contains("ethereum") || lowercaseText.contains("eth") {
                parameters["asset"] = "ETHUSDT"
            }
        }
        
        // Analysis intent
        else if lowercaseText.contains("analyseer") || lowercaseText.contains("analyze") ||
                lowercaseText.contains("voorspel") || lowercaseText.contains("predict") {
            intent = .analysis
            confidence = 0.8
        }
        
        // Portfolio intent
        else if lowercaseText.contains("portfolio") || lowercaseText.contains("portefeuille") ||
                lowercaseText.contains("winst") || lowercaseText.contains("profit") {
            intent = .portfolio
            confidence = 0.8
        }
        
        // Settings intent
        else if lowercaseText.contains("help") || lowercaseText.contains("hulp") {
            intent = .settings
            parameters["action"] = "help"
            confidence = 0.9
        }
        
        return NLPResult(intent: intent, parameters: parameters, confidence: confidence)
    }
}

// MARK: - Context Manager
class VoiceContextManager {
    private var conversationHistory: [String] = []
    private var currentContext: VoiceContext = .general
    
    func updateContext(with command: String) {
        conversationHistory.append(command)
        
        // Keep only last 5 commands for context
        if conversationHistory.count > 5 {
            conversationHistory.removeFirst()
        }
        
        // Determine current context
        if command.contains("trading") || command.contains("handelen") {
            currentContext = .trading
        } else if command.contains("portfolio") {
            currentContext = .portfolio
        } else if command.contains("analysis") {
            currentContext = .analysis
        }
    }
    
    func getCurrentContext() -> VoiceContext {
        return currentContext
    }
}

enum VoiceContext {
    case general
    case trading
    case portfolio
    case analysis
    case settings
} 