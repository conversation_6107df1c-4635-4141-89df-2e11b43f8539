import SwiftUI

struct AnimatedAgentsView: View {
    @State private var animationPhase: CGFloat = 0
    @State private var pulseScale: CGFloat = 1.0
    
    var body: some View {
        ZStack {
            // Background gradient
            LinearGradient(
                gradient: Gradient(colors: [Color.black.opacity(0.8), Color.blue.opacity(0.3)]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .cornerRadius(20)
            
            // Animated connection lines
            ConnectionLinesView(animationPhase: animationPhase)
            
            // Agent nodes
            HStack(spacing: 40) {
                AgentNodeView(
                    name: "Agent 1",
                    color: .orange,
                    delay: 0,
                    animationPhase: animationPhase
                )
                
                AgentNodeView(
                    name: "Agent 2", 
                    color: .blue,
                    delay: 0.5,
                    animationPhase: animationPhase
                )
                
                AgentNodeView(
                    name: "Agent 3",
                    color: .green,
                    delay: 1.0,
                    animationPhase: animationPhase
                )
                
                AgentNodeView(
                    name: "Agent 4",
                    color: .purple,
                    delay: 1.5,
                    animationPhase: animationPhase
                )
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 2).repeatForever(autoreverses: true)) {
                animationPhase = 1.0
            }
        }
    }
}

struct AgentNodeView: View {
    let name: String
    let color: Color
    let delay: Double
    let animationPhase: CGFloat
    
    @State private var isActive = false
    @State private var pulseScale: CGFloat = 1.0
    
    var body: some View {
        VStack(spacing: 8) {
            ZStack {
                // Outer glow
                Circle()
                    .fill(color.opacity(0.3))
                    .frame(width: 60, height: 60)
                    .scaleEffect(pulseScale)
                    .blur(radius: 4)
                
                // Main circle
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [color, color.opacity(0.7)]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 40, height: 40)
                    .overlay(
                        Circle()
                            .stroke(Color.white.opacity(0.8), lineWidth: 2)
                    )
                
                // Activity indicator
                if isActive {
                    Circle()
                        .fill(Color.white)
                        .frame(width: 8, height: 8)
                        .scaleEffect(pulseScale)
                }
            }
            
            Text(name)
                .font(.caption2)
                .foregroundColor(.white)
                .fontWeight(.medium)
        }
        .onAppear {
            // Staggered animation start
            DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
                    pulseScale = 1.3
                }
                
                withAnimation(.easeInOut(duration: 0.8).repeatForever(autoreverses: true)) {
                    isActive.toggle()
                }
            }
        }
    }
}

struct ConnectionLinesView: View {
    let animationPhase: CGFloat
    
    var body: some View {
        Canvas { context, size in
            let centerY = size.height / 2
            let spacing = size.width / 5
            
            // Draw animated connection lines
            for i in 0..<3 {
                let startX = spacing * CGFloat(i + 1)
                let endX = spacing * CGFloat(i + 2)
                
                let path = Path { path in
                    path.move(to: CGPoint(x: startX, y: centerY))
                    path.addLine(to: CGPoint(x: endX, y: centerY))
                }
                
                // Animated stroke
                let strokeStyle = StrokeStyle(
                    lineWidth: 2,
                    lineCap: .round,
                    dash: [10, 5],
                    dashPhase: animationPhase * 15
                )
                
                context.stroke(
                    path,
                    with: .color(.white.opacity(0.6)),
                    style: strokeStyle
                )
            }
        }
    }
}

#Preview {
    AnimatedAgentsView()
        .frame(height: 200)
        .padding()
        .background(Color.black)
}
