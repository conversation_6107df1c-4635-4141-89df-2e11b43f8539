# 🚀 Agent Leon - TestFlight Beta Strategie (NL)

## 🎯 <PERSON><PERSON> en veilig feedback verzamelen van Nederlandse crypto-gebruikers om Agent Leon te verfijnen vóór App Store-release.

---

## 🗂️ Overzicht (JSON-format voor automatisering)
```json
{
  "phases": [
    {
      "id": "internal",
      "naam": "Interne Test",
      "duur_dagen": 5,
      "testers": ["teamleden"],
      "doelen": ["kritieke bugs", "basisfunctionaliteit"]
    },
    {
      "id": "closed_beta",
      "naam": "Gesloten Beta NL",
      "duur_dagen": 7,
      "testers": 50,
      "kanalen": ["Telegram NL groep", "Discord"],
      "doelen": ["AI strategieën", "exchange connectiviteit", "portfoliobugs"]
    },
    {
      "id": "public_beta",
      "naam": "Open Beta (TestFlight Link)",
      "duur_dagen": 14,
      "max_testers": 10000,
      "distributie": ["Crypto Insiders artikel", "LinkedIn post", "Influencer YouTube"],
      "doelen": ["UX verfijning", "performance", "marketing buzz"]
    }
  ]
}
```

---

## 📋 Fase-details

### 1. Interne Test (5 dagen)
- **Testers**: eigen dev-team (max 25)
- **Focus**: crashlogs, feature-flags, basisorderflow
- **Tools**: Xcode Organizer → Add ("Internal Testers")
- **Resultaat**: ✅ Build #1 gereed voor closed beta

### 2. Gesloten Beta NL (7 dagen)
- **Testers**: ±50 vertrouwde Nederlandse crypto traders
- **Uitnodiging**: stuur persoonlijke TestFlight invites via **Telegram bot**
- **Feedback-kanalen**:
  1. In-app feedback sheet (SwiftUI `sheet` + `MFMailComposeViewController`)
  2. Telegram groep `@AgentLeonBetaNL`
- **Metrics**: session length, strategie-switches, bug reports
- **Resultaat**: ✅ Build #2 met fixes → open beta

### 3. Open Beta (14 dagen)
- **Testers**: publiek (max 10k)
- **Distributie**:
  - Plaats TestFlight public link op website & social
  - Influencer video (Crypto Michaël) ⇨ link in description
- **Automatisering**: gebruik **Fastlane `pilot`** voor upload & automatic group assign
- **Exit-criteria**: crash-free sessions > 98%, 4★+ survey rating

---

## 🤖 Telegram Bot Automatisering (optie √ verrassend)
```python
# agentleon_beta_bot.py
from telegram import Update, Bot
from telegram.ext import ApplicationBuilder, CommandHandler

TOKEN = "TG_BOT_TOKEN"
TESTFLIGHT_LINK = "https://testflight.apple.com/join/ABC123XYZ"

async def start(update: Update, _):
    await update.message.reply_text(
        f"🎉 Welkom bij de Agent Leon Beta!\nDownload via TestFlight: {TESTFLIGHT_LINK}\nStuur feedback hier."
    )

a = ApplicationBuilder().token(TOKEN).build()
a.add_handler(CommandHandler("start", start))
a.run_polling()
```

---

## 📝 Checklist per Build
- [ ] Release notes NL ingevuld (max 500 tekens)
- [ ] Privacy compliance vragen beantwoord
- [ ] Beta App Review (eerste upload) goedgekeurd
- [ ] App Icon & screenshots (NL) toegevoegd
- [ ] Testers groups juist toegekend

---

## 3 Opties Samengevat
1. **Basis** – alleen interne en closed beta (laag risico)
2. **Community-gericht** – full plan hierboven (aanbevolen)
3. **Growth-hack (verrassend)** – Public beta tegelijk met influencer contest en realtime leaderboard (gamification)

Kies optie 1–3 en ik automatiseer de nodige scripts / Fastlane lanes. 🤖 